{"version": 3, "file": "overlapDodgeY.js", "sourceRoot": "", "sources": ["../../src/label-transform/overlapDodgeY.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,SAAS,EAAE,MAAM,uBAAuB,CAAC;AAMlD,SAAS,kBAAkB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACxC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACxB,CAAC;AAED,SAAS,MAAM;IACb,MAAM,GAAG,GAAG,IAAI,GAAG,EAAQ,CAAC;IAC5B,MAAM,GAAG,GAAG,CAAC,GAAM,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACrC,MAAM,GAAG,GAAG,CAAC,GAAM,EAAE,KAAQ,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IACtD,OAAO,CAAC,GAAG,EAAE,GAAG,CAAU,CAAC;AAC7B,CAAC;AAED,SAAS,yBAAyB,CAAC,KAAoB;IACrD,MAAM,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IACnC,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;IACxD,cAAc,IAAI,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;IACnD,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;IAC5C,IAAI,CAAC,OAAO,EAAE,CAAC;IACf,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AACtB,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,MAAM,aAAa,GAA8B,CAAC,OAAO,EAAE,EAAE;IAClE,MAAM,EAAE,aAAa,GAAG,EAAE,EAAE,QAAQ,GAAG,GAAG,EAAE,OAAO,GAAG,CAAC,EAAE,GAAG,OAAO,CAAC;IACpE,OAAO,CAAC,MAAuB,EAAE,EAAE;QACjC,MAAM,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;QACxB,IAAI,CAAC,IAAI,CAAC;YAAE,OAAO,MAAM,CAAC;QAE1B,oCAAoC;QACpC,MAAM,CAAC,EAAE,EAAE,KAAK,CAAC,GAAG,MAAM,EAAyB,CAAC;QACpD,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,MAAM,EAAyB,CAAC;QAClD,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,MAAM,EAAyB,CAAC;QAClD,MAAM,CAAC,EAAE,EAAE,KAAK,CAAC,GAAG,MAAM,EAAmC,CAAC;QAC9D,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;YAC1B,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,yBAAyB,CAAC,KAAK,CAAC,CAAC;YACtD,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC;YACrB,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC;YACrB,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;YACjB,IAAI,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;YAChB,IAAI,CAAC,KAAK,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;YACrB,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;SACxB;QAED,sBAAsB;QACtB,KAAK,IAAI,IAAI,GAAG,CAAC,EAAE,IAAI,GAAG,aAAa,EAAE,IAAI,EAAE,EAAE;YAC/C,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7C,IAAI,KAAK,GAAG,CAAC,CAAC;YACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC9B,MAAM,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;gBACrB,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBACd,IAAI,EAAE,CAAC;gBACP,yEAAyE;gBACzE,OAAO,CAAC,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;oBAAE,CAAC,IAAI,CAAC,CAAC;gBACvE,IAAI,EAAE,EAAE;oBACN,MAAM,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;oBACjB,MAAM,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;oBACjB,MAAM,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;oBACjB,MAAM,KAAK,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;oBAC7B,IAAI,KAAK,GAAG,OAAO,EAAE;wBACnB,MAAM,QAAQ,GAAG,CAAC,OAAO,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;wBACvC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;wBAClC,IAAI,CAAC,EAAE,EAAE,EAAE,GAAG,QAAQ,CAAC,CAAC;wBACxB,IAAI,CAAC,EAAE,EAAE,EAAE,GAAG,QAAQ,CAAC,CAAC;qBACzB;iBACF;aACF;YACD,IAAI,KAAK,GAAG,QAAQ;gBAAE,MAAM;SAC7B;QAED,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;YAC1B,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC;SACvC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC,CAAC;AACJ,CAAC,CAAC"}