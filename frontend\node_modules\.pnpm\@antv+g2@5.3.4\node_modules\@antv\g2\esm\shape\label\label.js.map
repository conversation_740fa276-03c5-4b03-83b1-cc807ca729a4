{"version": 3, "file": "label.js", "sourceRoot": "", "sources": ["../../../src/shape/label/label.ts"], "names": [], "mappings": ";;;;;;;;;;;AACA,OAAO,EAAE,MAAM,EAAE,MAAM,uBAAuB,CAAC;AAE/C,OAAO,EAAE,UAAU,EAAE,MAAM,mBAAmB,CAAC;AAC/C,OAAO,EAAE,WAAW,EAAE,UAAU,EAAE,MAAM,wBAAwB,CAAC;AACjE,OAAO,EAAE,SAAS,EAAE,MAAM,oBAAoB,CAAC;AAC/C,OAAO,EAAE,OAAO,EAAE,MAAM,iBAAiB,CAAC;AAE1C,OAAO,KAAK,iBAAiB,MAAM,YAAY,CAAC;AAUhD,SAAS,aAAa,CAAC,QAAuB,EAAE,UAAsB;IACpE,IAAI,QAAQ,KAAK,SAAS;QAAE,OAAO,QAAQ,CAAC;IAC5C,IAAI,UAAU,CAAC,UAAU,CAAC;QAAE,OAAO,QAAQ,CAAC;IAC5C,IAAI,WAAW,CAAC,UAAU,CAAC;QAAE,OAAO,OAAO,CAAC;IAC5C,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,eAAe,CACtB,MAAiB,EACjB,KAA0B,EAC1B,UAAsB,EACtB,KAAc,EACd,OAAqB,EACrB,MAAmB;IAEnB,wDAAwD;IACxD,oCAAoC;IACpC,MAAM,EAAE,QAAQ,EAAE,GAAG,KAAK,CAAC;IAC3B,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;IAC3B,MAAM,CAAC,GAAG,aAAa,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;IAC9C,MAAM,SAAS,GAAG,MAAM;QACtB,CAAC,CAAC,WAAW;QACb,CAAC,CAAC,CAAC,KAAK,QAAQ;YAChB,CAAC,CAAC,YAAY;YACd,CAAC,CAAC,OAAO,CAAC;IACZ,MAAM,CAAC,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC;IAC3B,MAAM,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;IACtC,MAAM,SAAS,GAAG,iBAAiB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;IAClD,IAAI,CAAC,SAAS,EAAE;QACd,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,EAAE,CAAC,CAAC;KAC3C;IACD,uCACK,CAAC,GACD,SAAS,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,CAAC,EACvD;AACJ,CAAC;AAED;;;GAGG;AACH,MAAM,CAAC,MAAM,KAAK,GAAqB,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;IAC1D,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;IACtC,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;IAC3B,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE;QACtC,MAAM,EACJ,IAAI,EACJ,CAAC,EACD,CAAC,EACD,SAAS,EAAE,WAAW,GAAG,EAAE,EAC3B,eAAe,EACf,SAAS,GAAG,EAAE,KAEZ,KAAK,EADJ,aAAa,UACd,KAAK,EARH,+DAQL,CAAQ,CAAC;QACV,MAAM,KAIF,eAAe,CAAC,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC,EAJhE,EACJ,MAAM,GAAG,CAAC,EACV,SAAS,GAAG,EAAE,OAEsD,EADjE,YAAY,cAHX,uBAIL,CAAqE,CAAC;QAEvE,OAAO,MAAM,CAAC,IAAI,OAAO,EAAE,CAAC;aACzB,IAAI,CAAC,UAAU,EAAE,YAAY,CAAC;aAC9B,KAAK,CAAC,MAAM,EAAE,GAAG,IAAI,EAAE,CAAC;aACxB,KAAK,CAAC,WAAW,EAAE,GAAG,SAAS,WAAW,CAAC;aAC3C,KAAK,CACJ,WAAW,EACX,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAC5D;aACA,KAAK,CACJ,gBAAgB,EAChB,GAAG,SAAS,WAAW,CAAC,MAAM,KAAK,WAAW,EAAE,CAAC,IAAI,EAAE,CACxD;aACA,KAAK,CAAC,sBAAsB,EAAE,eAAe,CAAC;aAC9C,KAAK,CAAC,aAAa,EAAE,UAAU,CAAC,SAAS,EAAE,CAAC;aAC5C,IAAI,CAAC,UAAU,EAAE,aAAa,CAAC;aAC/B,IAAI,EAAE,CAAC;IACZ,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF,KAAK,CAAC,KAAK,GAAG;IACZ,aAAa,EAAE,OAAO;CACvB,CAAC"}