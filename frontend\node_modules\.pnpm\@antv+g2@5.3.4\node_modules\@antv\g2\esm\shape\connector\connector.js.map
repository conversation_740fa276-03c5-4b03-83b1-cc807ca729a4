{"version": 3, "file": "connector.js", "sourceRoot": "", "sources": ["../../../src/shape/connector/connector.ts"], "names": [], "mappings": ";;;;;;;;;;;AAEA,OAAO,EAAkB,IAAI,EAAE,MAAM,SAAS,CAAC;AAC/C,OAAO,EAAE,MAAM,EAAE,MAAM,iBAAiB,CAAC;AACzC,OAAO,EAAE,IAAI,IAAI,MAAM,EAAE,MAAM,uBAAuB,CAAC;AAEvD,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AACrD,OAAO,EAAE,SAAS,EAAE,MAAM,oBAAoB,CAAC;AAC/C,OAAO,EAAE,MAAM,EAAE,MAAM,uBAAuB,CAAC;AAC/C,OAAO,EAAE,UAAU,EAAE,MAAM,UAAU,CAAC;AAYtC,SAAS,WAAW,CAAC,CAAS,EAAE,CAAS,EAAE,CAAS;IAClD,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;AAChF,CAAC;AAED;;GAEG;AACH,SAAS,kBAAkB,CAAC,MAAiB;IAC3C,OAAO,MAAM,EAAE;SACZ,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACd,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;AAC5B,CAAC;AAED,SAAS,SAAS,CAChB,UAAsB,EACtB,MAAiB,EACjB,aAAqB,EACrB,aAAqB,EACrB,aAAqB,EACrB,aAAqB,EACrB,OAAO,GAAG,CAAC;IAEX,MAAM,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC;IAEpC,IAAI,WAAW,CAAC,UAAU,CAAC,EAAE;QAC3B,MAAM,EAAE,GAAG,EAAE,GAAG,aAAa,CAAC;QAC9B,MAAM,EAAE,GAAG,EAAE,GAAG,aAAa,CAAC;QAC9B,MAAM,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC;QACvB,MAAM,EAAE,GAAG,EAAE,GAAG,aAAa,CAAC;QAC9B,MAAM,EAAE,GAAG,EAAE,GAAG,aAAa,CAAC;QAC9B,OAAO;YACL,CAAC,EAAE,EAAE,EAAE,CAAC;YACR,CAAC,CAAC,EAAE,EAAE,CAAC;YACP,CAAC,CAAC,EAAE,EAAE,CAAC;YACP,CAAC,EAAE,EAAE,EAAE,CAAC;SACT,CAAC;KACH;IAED,MAAM,EAAE,GAAG,EAAE,GAAG,aAAa,CAAC;IAC9B,MAAM,EAAE,GAAG,EAAE,GAAG,aAAa,CAAC;IAC9B,MAAM,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC;IACvB,MAAM,EAAE,GAAG,EAAE,GAAG,aAAa,CAAC;IAC9B,MAAM,EAAE,GAAG,EAAE,GAAG,aAAa,CAAC;IAC9B,OAAO;QACL,CAAC,EAAE,EAAE,EAAE,CAAC;QACR,CAAC,EAAE,EAAE,CAAC,CAAC;QACP,CAAC,EAAE,EAAE,CAAC,CAAC;QACP,CAAC,EAAE,EAAE,EAAE,CAAC;KACT,CAAC;AACJ,CAAC;AAED,MAAM,CAAC,MAAM,SAAS,GAAyB,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;IAClE,MAAM,EACJ,OAAO,GAAG,CAAC,EACX,aAAa,GAAG,OAAO,EACvB,aAAa,GAAG,OAAO,EACvB,OAAO,GAAG,CAAC,EACX,aAAa,GAAG,OAAO,EACvB,aAAa,GAAG,OAAO,EACvB,cAAc,EAAE,OAAO,EACvB,SAAS,GAAG,IAAI,KAEd,OAAO,EADN,KAAK,UACN,OAAO,EAVL,yHAUL,CAAU,CAAC;IACZ,MAAM,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;IAE/B,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE;QACjC,MAAM,EAAE,KAAK,EAAE,YAAY,EAAE,cAAc,KAAc,QAAQ,EAAjB,IAAI,UAAK,QAAQ,EAA3D,2BAAgD,CAAW,CAAC;QAClE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,KAAK,CAAC;QACnC,MAAM,CAAC,GAAG,SAAS,CACjB,UAAU,EACV,MAAM,EACN,aAAa,EACb,aAAa,EACb,aAAa,EACb,aAAa,EACb,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,cAAc,CAC1B,CAAC;QACF,MAAM,UAAU,GAAG,SAAS,iCAAM,KAAK,GAAK,QAAQ,GAAI,WAAW,CAAC,CAAC;QAErE,OAAO,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;aACtB,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;aACtB,KAAK,CAAC,GAAG,EAAE,kBAAkB,CAAC,CAAC,CAAC,CAAC;aACjC,KAAK,CAAC,QAAQ,EAAE,KAAK,IAAI,YAAY,CAAC;aACtC,KAAK,CAAC,WAAW,EAAE,SAAS,CAAC;aAC7B,KAAK,CACJ,WAAW,EACX,SAAS;YACP,CAAC,CAAC,IAAI,MAAM,CAAC;gBACT,SAAS,EAAE,QAAQ;gBACnB,KAAK,kCACA,UAAU,KACb,MAAM,EAAE,WAAW,GACpB;aACF,CAAC;YACJ,CAAC,CAAC,IAAI,CACT;aACA,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC;aACvB,IAAI,EAAE,CAAC;IACZ,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF,SAAS,CAAC,KAAK,GAAG;IAChB,aAAa,EAAE,MAAM;IACrB,qBAAqB,EAAE,QAAQ;IAC/B,sBAAsB,EAAE,UAAU;IAClC,oBAAoB,EAAE,SAAS;CAChC,CAAC"}