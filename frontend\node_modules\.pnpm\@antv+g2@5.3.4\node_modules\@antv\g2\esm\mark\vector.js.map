{"version": 3, "file": "vector.js", "sourceRoot": "", "sources": ["../../src/mark/vector.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,WAAW,EAAE,MAAM,UAAU,CAAC;AACvC,OAAO,EACL,oBAAoB,EACpB,iBAAiB,EACjB,gBAAgB,EAChB,SAAS,GACV,MAAM,SAAS,CAAC;AAEjB,MAAM,KAAK,GAAG;IACZ,MAAM,EAAE,WAAW;CACpB,CAAC;AAIF;;;GAGG;AACH,MAAM,CAAC,MAAM,MAAM,GAAsB,GAAG,EAAE;IAC5C,OAAO,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,EAAE;QACzC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC;QACjD,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,UAAU,CAAC,OAAO,EAAE,CAAC;QAE7C,MAAM,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE;YACxB,MAAM,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;YACtC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;YACpB,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC;YAErB,MAAM,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAC/B,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAEhC,OAAO;gBACL,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;gBAChD,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;aACpC,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,CAAC,KAAK,GAAG;IACb,YAAY,EAAE,QAAQ;IACtB,iBAAiB,EAAE,OAAO;IAC1B,SAAS,EAAE,KAAK;IAChB,KAAK;IACL,QAAQ,EAAE;QACR,GAAG,oBAAoB,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;QACvD,EAAE,IAAI,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE;QAC7B,EAAE,IAAI,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE;QAC7B,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE;QACrD,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE;KACjC;IACD,YAAY,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;IACrC,aAAa,EAAE,CAAC,GAAG,iBAAiB,EAAE,EAAE,GAAG,SAAS,EAAE,CAAC;CACxD,CAAC"}