import { MarkComponent as MC } from '../runtime';
import { BoxMark } from '../spec';
export type BoxOptions = Omit<BoxMark, 'type'>;
/**
 * Convert value for each channel to box shapes.
 *
 * p0           p2          p1
 *    ──────────┬──────────
 *              │
 *              │
 *              │
 *              │
 *              │
 *              │
 *              │ p3
 * p4 ┌─────────┴──────────┐ p5
 *    │                    │
 *    │                    │
 * p8 ├────────────────────┤ p9
 *    │                    │
 *    │        p10         │
 * p7 └─────────┬──────────┘ p6
 *              │
 *              │
 *              │
 *              │
 *              │
 *              │
 *              │
 *              │
 *   ───────────┴───────────
 * p12         p11           p13
 */
export declare const Box: MC<BoxOptions>;
