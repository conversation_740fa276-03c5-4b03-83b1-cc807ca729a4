{"version": 3, "file": "poptip.js", "sourceRoot": "", "sources": ["../../src/interaction/poptip.ts"], "names": [], "mappings": ";;;;;;;;;;;AAAA,OAAO,EAAiB,IAAI,EAAE,MAAM,SAAS,CAAC;AAC9C,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAC5C,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAE5C,SAAS,GAAG,CAAC,GAAG,EAAE,QAAQ,EAAE,KAAK;IAC/B,OAAO,IAAI,GAAG,WAAW,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;SAC3C,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,IAAI,KAAK,EAAE,CAAC;SACnD,IAAI,CAAC,GAAG,CAAC,KAAK,QAAQ,KAAK,GAAG,GAAG,CAAC;AACvC,CAAC;AAED,MAAM,eAAe,GAAG;IACtB,eAAe,EAAE,kBAAkB;IACnC,KAAK,EAAE,MAAM;IACb,KAAK,EAAE,aAAa;IACpB,OAAO,EAAE,SAAS;IAClB,QAAQ,EAAE,MAAM;IAChB,YAAY,EAAE,OAAO;IACrB,SAAS,EACP,iGAAiG;CACpG,CAAC;AAEF,SAAS,SAAS,CAAC,OAAO;IACxB,IAAI,OAAO,CAAC,QAAQ,KAAK,MAAM;QAAE,OAAO,KAAK,CAAC;IAC9C,IAAI,OAAO,CAAC,aAAa,EAAE;QAAE,OAAO,IAAI,CAAC;IACzC,OAAO,KAAK,CAAC;AACf,CAAC;AAED,MAAM,UAAU,MAAM,CAAC,EAAsC;QAAtC,EAAE,OAAO,GAAG,CAAC,EAAE,OAAO,GAAG,CAAC,OAAY,EAAP,KAAK,cAApC,sBAAsC,CAAF;IACzD,OAAO,CAAC,OAAO,EAAE,EAAE;QACjB,MAAM,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC;QAC9B,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,SAAS,CAAC,SAAS,EAAE,CAAC,GAAG,CAAC;QAC3C,MAAM,QAAQ,GAAG,SAAS,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QACzC,MAAM,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC;QAEvB,MAAM,WAAW,GAAG,CAAC,CAAC,EAAE,EAAE;YACxB,MAAM,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;YACrB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE;gBACtB,CAAC,CAAC,eAAe,EAAE,CAAC;gBACpB,OAAO;aACR;YACD,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;YAC/C,MAAM,CAAC,GAAG,MAAM,GAAG,OAAO,GAAG,EAAE,CAAC;YAChC,MAAM,CAAC,GAAG,MAAM,GAAG,OAAO,GAAG,EAAE,CAAC;YAChC,IAAI,MAAM,CAAC,GAAG,EAAE;gBACd,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;gBACvB,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;gBACvB,OAAO;aACR;YACD,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC,KAAK,CAAC;YAC9B,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC;gBAC1B,SAAS,EAAE,QAAQ;gBACnB,KAAK,EAAE;oBACL,SAAS,EAAE,GAAG,CAAC,KAAK,EAAE,IAAI,kCACrB,eAAe,GACf,QAAQ,EACX;oBACF,CAAC;oBACD,CAAC;iBACF;aACF,CAAC,CAAC;YACH,SAAS,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;YAClC,MAAM,CAAC,GAAG,GAAG,UAAU,CAAC;YACxB,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACvB,CAAC,CAAC;QAEF,MAAM,UAAU,GAAG,CAAC,CAAC,EAAE,EAAE;YACvB,MAAM,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;YACrB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE;gBACtB,CAAC,CAAC,eAAe,EAAE,CAAC;gBACpB,OAAO;aACR;YACD,IAAI,CAAC,MAAM,CAAC,GAAG;gBAAE,OAAO;YACxB,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC;YACpB,MAAM,CAAC,GAAG,GAAG,IAAI,CAAC;YAClB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAC1B,CAAC,CAAC;QAEF,SAAS,CAAC,gBAAgB,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;QACvD,SAAS,CAAC,gBAAgB,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;QAErD,OAAO,GAAG,EAAE;YACV,SAAS,CAAC,mBAAmB,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;YAC1D,SAAS,CAAC,mBAAmB,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;YACxD,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE,CAAE,GAAqB,CAAC,MAAM,EAAE,CAAC,CAAC;QACzD,CAAC,CAAC;IACJ,CAAC,CAAC;AACJ,CAAC;AAED,MAAM,CAAC,KAAK,GAAG;IACb,iBAAiB,EAAE,IAAI;CACxB,CAAC"}