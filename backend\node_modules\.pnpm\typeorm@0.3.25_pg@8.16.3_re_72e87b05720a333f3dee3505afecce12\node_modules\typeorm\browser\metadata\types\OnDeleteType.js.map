{"version": 3, "sources": ["../browser/src/metadata/types/OnDeleteType.ts"], "names": [], "mappings": "", "file": "OnDeleteType.js", "sourcesContent": ["/**\n * ON_DELETE type to be used to specify delete strategy when some relation is being deleted from the database.\n */\nexport type OnDeleteType =\n    | \"RESTRICT\"\n    | \"CASCADE\"\n    | \"SET NULL\"\n    | \"DEFAULT\"\n    | \"NO ACTION\"\n"], "sourceRoot": "../.."}