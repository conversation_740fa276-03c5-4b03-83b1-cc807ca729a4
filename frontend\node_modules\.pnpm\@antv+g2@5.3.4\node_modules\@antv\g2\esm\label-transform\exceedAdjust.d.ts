import { LabelTransformComponent as LLC } from '../runtime';
import { ExceedAdjustLabel } from '../spec';
export type ExceedAdjustOptions = Omit<ExceedAdjustLabel, 'type'> & {
    /** X-axis offset default is 0 */
    offsetX?: number;
    /** Y-axis offset default is 0 */
    offsetY?: number;
};
/**
 * adjust the label when exceed the specific area
 */
export declare const ExceedAdjust: LLC<ExceedAdjustOptions>;
