import React from 'react';
import { useParams } from 'react-router-dom';
import { 
  Card, 
  Row, 
  Col, 
  Typography, 
  Tag, 
  Descriptions, 
  Image, 
  Button,
  Space,
  Divider,
  List,
  Avatar
} from 'antd';
import { 
  PlayCircleOutlined, 
  DownloadOutlined, 
  GlobalOutlined,
  SoundOutlined,
  PictureOutlined
} from '@ant-design/icons';

const { Title, Paragraph, Text } = Typography;

const SpeciesDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();

  // 模拟数据
  const mockSpecies = {
    id: id,
    chineseName: '蓝鲸',
    englishName: 'Blue Whale',
    latinName: 'Balaenoptera musculus',
    conservationStatus: 'EN',
    description: `蓝鲸是世界上现存最大的动物，也是地球历史上最大的动物。它们主要以磷虾为食，
    分布在全球各大洋中。蓝鲸的叫声是动物王国中最响亮的声音之一，可以传播数百公里。
    
    蓝鲸的发声频率通常在10-40赫兹之间，这些低频声音可以在海洋中传播很远的距离，
    用于与其他蓝鲸进行交流。科学家们通过研究这些声音来了解蓝鲸的行为、迁徙模式和种群状况。`,
    classification: {
      kingdom: '动物界',
      phylum: '脊索动物门',
      class: '哺乳纲',
      order: '鲸目',
      family: '须鲸科',
      genus: '须鲸属',
      species: '蓝鲸'
    },
    stats: {
      imageCount: 15,
      videoCount: 3,
      audioCount: 28,
      distributionCount: 5
    }
  };

  const mockAudioFiles = [
    {
      id: 'audio-1',
      fileName: '蓝鲸叫声_北太平洋_2023.wav',
      recordingLocation: '北太平洋',
      recordingTime: '2023-08-15',
      behaviorDescription: '觅食行为',
      duration: '00:02:35'
    },
    {
      id: 'audio-2',
      fileName: '蓝鲸歌声_南极海域_2023.wav',
      recordingLocation: '南极海域',
      recordingTime: '2023-07-22',
      behaviorDescription: '交流行为',
      duration: '00:01:48'
    },
    {
      id: 'audio-3',
      fileName: '蓝鲸低频声_大西洋_2023.wav',
      recordingLocation: '北大西洋',
      recordingTime: '2023-09-03',
      behaviorDescription: '长距离通讯',
      duration: '00:03:12'
    }
  ];

  const conservationStatusMap = {
    'LC': { text: '无危', color: 'green' },
    'NT': { text: '近危', color: 'blue' },
    'VU': { text: '易危', color: 'orange' },
    'EN': { text: '濒危', color: 'red' },
    'CR': { text: '极危', color: 'purple' }
  };

  return (
    <div style={{ padding: '24px' }}>
      <Row gutter={[24, 24]}>
        {/* 左侧主要内容 */}
        <Col xs={24} lg={16}>
          {/* 基本信息 */}
          <Card style={{ marginBottom: '24px' }}>
            <div style={{ marginBottom: '24px' }}>
              <Space align="start" size="large">
                <Avatar 
                  size={80} 
                  style={{ 
                    backgroundColor: '#1890ff',
                    fontSize: '32px'
                  }}
                >
                  {mockSpecies.chineseName.charAt(0)}
                </Avatar>
                <div>
                  <Title level={1} style={{ margin: 0, marginBottom: '8px' }}>
                    {mockSpecies.chineseName}
                    <Tag 
                      color={conservationStatusMap[mockSpecies.conservationStatus as keyof typeof conservationStatusMap]?.color}
                      style={{ marginLeft: '12px' }}
                    >
                      {conservationStatusMap[mockSpecies.conservationStatus as keyof typeof conservationStatusMap]?.text}
                    </Tag>
                  </Title>
                  <Text type="secondary" style={{ fontSize: '18px', display: 'block' }}>
                    {mockSpecies.englishName}
                  </Text>
                  <Text type="secondary" italic style={{ fontSize: '16px' }}>
                    {mockSpecies.latinName}
                  </Text>
                </div>
              </Space>
            </div>

            <Descriptions column={2} bordered>
              <Descriptions.Item label="界">{mockSpecies.classification.kingdom}</Descriptions.Item>
              <Descriptions.Item label="门">{mockSpecies.classification.phylum}</Descriptions.Item>
              <Descriptions.Item label="纲">{mockSpecies.classification.class}</Descriptions.Item>
              <Descriptions.Item label="目">{mockSpecies.classification.order}</Descriptions.Item>
              <Descriptions.Item label="科">{mockSpecies.classification.family}</Descriptions.Item>
              <Descriptions.Item label="属">{mockSpecies.classification.genus}</Descriptions.Item>
              <Descriptions.Item label="种" span={2}>{mockSpecies.classification.species}</Descriptions.Item>
            </Descriptions>
          </Card>

          {/* 详细描述 */}
          <Card title="物种介绍" style={{ marginBottom: '24px' }}>
            <Paragraph style={{ fontSize: '16px', lineHeight: '1.8' }}>
              {mockSpecies.description}
            </Paragraph>
          </Card>

          {/* 音频文件 */}
          <Card 
            title={
              <Space>
                <SoundOutlined />
                音频文件 ({mockSpecies.stats.audioCount})
              </Space>
            }
            style={{ marginBottom: '24px' }}
          >
            <List
              itemLayout="horizontal"
              dataSource={mockAudioFiles}
              renderItem={(audio) => (
                <List.Item
                  actions={[
                    <Button 
                      type="primary" 
                      icon={<PlayCircleOutlined />}
                      key="play"
                    >
                      播放
                    </Button>,
                    <Button 
                      icon={<DownloadOutlined />}
                      key="download"
                    >
                      下载
                    </Button>
                  ]}
                >
                  <List.Item.Meta
                    avatar={<Avatar icon={<SoundOutlined />} />}
                    title={audio.fileName}
                    description={
                      <Space direction="vertical" size={4}>
                        <Text type="secondary">
                          录音地点: {audio.recordingLocation} | 
                          录音时间: {audio.recordingTime} | 
                          时长: {audio.duration}
                        </Text>
                        <Text type="secondary">
                          行为描述: {audio.behaviorDescription}
                        </Text>
                      </Space>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>

        {/* 右侧边栏 */}
        <Col xs={24} lg={8}>
          {/* 代表性图片 */}
          <Card 
            title={
              <Space>
                <PictureOutlined />
                代表性图片
              </Space>
            }
            style={{ marginBottom: '24px' }}
          >
            <div style={{
              width: '100%',
              height: '200px',
              background: '#f5f5f5',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              borderRadius: '6px',
              color: '#999'
            }}>
              暂无图片
            </div>
          </Card>

          {/* 数据统计 */}
          <Card title="数据统计" style={{ marginBottom: '24px' }}>
            <Row gutter={[16, 16]} style={{ textAlign: 'center' }}>
              <Col span={12}>
                <div>
                  <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1890ff' }}>
                    {mockSpecies.stats.imageCount}
                  </div>
                  <div style={{ color: '#666' }}>图片</div>
                </div>
              </Col>
              <Col span={12}>
                <div>
                  <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#52c41a' }}>
                    {mockSpecies.stats.videoCount}
                  </div>
                  <div style={{ color: '#666' }}>视频</div>
                </div>
              </Col>
              <Col span={12}>
                <div>
                  <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#fa8c16' }}>
                    {mockSpecies.stats.audioCount}
                  </div>
                  <div style={{ color: '#666' }}>音频</div>
                </div>
              </Col>
              <Col span={12}>
                <div>
                  <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#722ed1' }}>
                    {mockSpecies.stats.distributionCount}
                  </div>
                  <div style={{ color: '#666' }}>分布区域</div>
                </div>
              </Col>
            </Row>
          </Card>

          {/* 快速操作 */}
          <Card title="快速操作">
            <Space direction="vertical" style={{ width: '100%' }}>
              <Button 
                type="primary" 
                icon={<GlobalOutlined />} 
                block
                size="large"
              >
                在地图上查看
              </Button>
              <Button 
                icon={<DownloadOutlined />} 
                block
                size="large"
              >
                下载所有音频
              </Button>
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default SpeciesDetailPage;
