import React, { useState, useEffect } from 'react';
import { 
  Typography, 
  Row, 
  Col, 
  Card, 
  Button, 
  Space, 
  DatePicker, 
  Select,
  message
} from 'antd';
import { 
  DashboardOutlined, 
  DownloadOutlined, 
  ReloadOutlined,
  FilterOutlined
} from '@ant-design/icons';
import SpeciesStatsChart from '../components/Charts/SpeciesStatsChart';
import InteractiveMap from '../components/Map/InteractiveMap';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

const DashboardPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [dateRange, setDateRange] = useState<any>(null);
  const [filterType, setFilterType] = useState<string>('all');

  // 刷新数据
  const handleRefresh = async () => {
    setLoading(true);
    try {
      // TODO: 实际的数据刷新逻辑
      await new Promise(resolve => setTimeout(resolve, 1000));
      message.success('数据已刷新');
    } catch (error) {
      message.error('数据刷新失败');
    } finally {
      setLoading(false);
    }
  };

  // 导出数据
  const handleExport = () => {
    // TODO: 实现数据导出功能
    message.info('数据导出功能开发中...');
  };

  // 应用筛选
  const handleFilter = () => {
    // TODO: 实现筛选逻辑
    message.info('筛选功能开发中...');
  };

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面头部 */}
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        marginBottom: '24px'
      }}>
        <div>
          <Title level={2} style={{ margin: 0, marginBottom: '8px' }}>
            <DashboardOutlined /> 数据仪表盘
          </Title>
          <Text type="secondary">
            海洋生物声音平台数据统计与分析
          </Text>
        </div>

        <Space>
          {/* 筛选控件 */}
          <RangePicker
            placeholder={['开始日期', '结束日期']}
            onChange={setDateRange}
            style={{ width: '240px' }}
          />
          <Select
            value={filterType}
            onChange={setFilterType}
            style={{ width: '120px' }}
            placeholder="数据类型"
          >
            <Option value="all">全部数据</Option>
            <Option value="species">物种数据</Option>
            <Option value="audio">音频数据</Option>
            <Option value="image">图片数据</Option>
          </Select>
          <Button 
            icon={<FilterOutlined />}
            onClick={handleFilter}
          >
            应用筛选
          </Button>
          <Button 
            icon={<ReloadOutlined />}
            onClick={handleRefresh}
            loading={loading}
          >
            刷新
          </Button>
          <Button 
            type="primary"
            icon={<DownloadOutlined />}
            onClick={handleExport}
          >
            导出数据
          </Button>
        </Space>
      </div>

      {/* 统计图表区域 */}
      <div style={{ marginBottom: '24px' }}>
        <SpeciesStatsChart loading={loading} />
      </div>

      {/* 地图和其他可视化 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} xl={16}>
          <Card
            title="地理分布概览"
            extra={
              <Button size="small">
                查看详细地图
              </Button>
            }
          >
            <InteractiveMap height="400px" />
          </Card>
        </Col>
        
        <Col xs={24} xl={8}>
          <Space direction="vertical" style={{ width: '100%' }} size="middle">
            {/* 快速统计 */}
            <Card title="快速统计" size="small">
              <div style={{ textAlign: 'center' }}>
                <div style={{ marginBottom: '16px' }}>
                  <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1890ff' }}>
                    156
                  </div>
                  <div style={{ color: '#666', fontSize: '12px' }}>
                    物种总数
                  </div>
                </div>
                
                <Row gutter={16}>
                  <Col span={8}>
                    <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#52c41a' }}>
                      1,248
                    </div>
                    <div style={{ color: '#666', fontSize: '11px' }}>
                      音频文件
                    </div>
                  </Col>
                  <Col span={8}>
                    <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#fa8c16' }}>
                      892
                    </div>
                    <div style={{ color: '#666', fontSize: '11px' }}>
                      图片文件
                    </div>
                  </Col>
                  <Col span={8}>
                    <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#722ed1' }}>
                      89
                    </div>
                    <div style={{ color: '#666', fontSize: '11px' }}>
                      分布区域
                    </div>
                  </Col>
                </Row>
              </div>
            </Card>

            {/* 最近活动 */}
            <Card title="最近活动" size="small">
              <div style={{ fontSize: '12px' }}>
                <div style={{ marginBottom: '8px', paddingBottom: '8px', borderBottom: '1px solid #f0f0f0' }}>
                  <Text strong>新增物种:</Text> 蓝鲸 (Balaenoptera musculus)
                  <br />
                  <Text type="secondary">2小时前</Text>
                </div>
                <div style={{ marginBottom: '8px', paddingBottom: '8px', borderBottom: '1px solid #f0f0f0' }}>
                  <Text strong>上传音频:</Text> 座头鲸歌声录音
                  <br />
                  <Text type="secondary">4小时前</Text>
                </div>
                <div style={{ marginBottom: '8px', paddingBottom: '8px', borderBottom: '1px solid #f0f0f0' }}>
                  <Text strong>更新分布:</Text> 虎鲸分布范围
                  <br />
                  <Text type="secondary">1天前</Text>
                </div>
                <div>
                  <Text strong>新增图片:</Text> 海豚跳跃照片
                  <br />
                  <Text type="secondary">2天前</Text>
                </div>
              </div>
            </Card>

            {/* 系统状态 */}
            <Card title="系统状态" size="small">
              <Space direction="vertical" style={{ width: '100%' }} size="small">
                <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: '12px' }}>
                  <span>数据库连接:</span>
                  <span style={{ color: '#52c41a' }}>正常</span>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: '12px' }}>
                  <span>文件存储:</span>
                  <span style={{ color: '#52c41a' }}>正常</span>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: '12px' }}>
                  <span>API服务:</span>
                  <span style={{ color: '#52c41a' }}>正常</span>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: '12px' }}>
                  <span>存储使用:</span>
                  <span style={{ color: '#fa8c16' }}>78%</span>
                </div>
              </Space>
            </Card>
          </Space>
        </Col>
      </Row>
    </div>
  );
};

export default DashboardPage;
