# 海洋生物声音平台 - 快速开始指南

## 项目概述

海洋生物声音平台是一个专业的海洋生物发声数据管理和展示平台，通过交互式地图和多媒体页面向科研人员、教育工作者及公众展示海洋生物声音数据。

## 技术栈

### 前端
- **React 19** + TypeScript
- **Vite** (构建工具)
- **Redux Toolkit** (状态管理)
- **Ant Design** (UI组件库)
- **React Router** (路由管理)

### 后端
- **Node.js** + Express 4 + TypeScript
- **TypeORM** (ORM)
- **PostgreSQL** + PostGIS (数据库)
- **JWT** (认证)
- **Multer** (文件上传)

## 环境要求

- **Node.js** >= 18.0.0
- **pnpm** >= 8.0.0
- **PostgreSQL** >= 13 (带PostGIS扩展)

## 快速启动

### 1. 克隆项目
```bash
git clone <repository-url>
cd mbdp
```

### 2. 安装依赖
```bash
# 安装前端依赖
cd frontend
pnpm install

# 安装后端依赖
cd ../backend
pnpm install
```

### 3. 数据库设置

#### 安装PostgreSQL和PostGIS
- **Windows**: 下载PostgreSQL安装包，确保包含PostGIS扩展
- **macOS**: `brew install postgresql postgis`
- **Ubuntu**: `sudo apt install postgresql postgresql-contrib postgis`

#### 创建数据库
```sql
-- 连接到PostgreSQL
sudo -u postgres psql

-- 创建用户
CREATE USER marine_user WITH PASSWORD 'your_password';
ALTER USER marine_user CREATEDB;

-- 创建数据库
CREATE DATABASE marine_bio_platform OWNER marine_user;

-- 连接到新数据库并启用PostGIS
\c marine_bio_platform
CREATE EXTENSION IF NOT EXISTS postgis;
```

### 4. 环境配置

#### 后端环境变量
复制并编辑后端环境变量：
```bash
cd backend
cp .env.example .env
```

编辑 `backend/.env`：
```env
# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=marine_user
DB_PASSWORD=your_password
DB_DATABASE=marine_bio_platform

# JWT配置
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=7d

# 文件上传配置
UPLOAD_DIR=uploads
```

#### 前端环境变量
前端环境变量已预配置，如需修改：
```bash
cd frontend
# 编辑 .env 文件
```

### 5. 初始化数据库
```bash
cd backend
pnpm run db:init
```

这将：
- 运行所有数据库迁移
- 创建默认角色（admin、editor、viewer）
- 创建默认管理员用户

**默认管理员账户：**
- 用户名: `admin`
- 密码: `admin123`

⚠️ **重要**: 请在生产环境中立即修改默认密码！

### 6. 启动服务

#### 启动后端服务
```bash
cd backend
pnpm run dev
```
后端服务将在 http://localhost:3001 启动

#### 启动前端服务
```bash
cd frontend
pnpm run dev
```
前端应用将在 http://localhost:5173 启动

## 验证安装

### 1. 检查后端API
访问 http://localhost:3001/health 应该返回：
```json
{
  "status": "ok",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "database": "connected"
}
```

### 2. 检查前端应用
访问 http://localhost:5173 应该看到海洋生物声音平台首页

### 3. 测试登录功能
1. 点击右上角"登录"按钮
2. 使用默认管理员账户登录
3. 登录成功后应该看到用户名显示在右上角

## 开发指南

### 代码规范
项目使用ESLint + Prettier进行代码规范化：

```bash
# 前端代码检查
cd frontend
pnpm run lint

# 后端代码检查和格式化
cd backend
pnpm run lint
pnpm run format
```

### API测试
后端提供了完整的API文档，参考 `backend/API_TESTING.md`

### 数据库管理
```bash
# 运行新的迁移
cd backend
pnpm run db:migration:run

# 回滚迁移
pnpm run db:migration:revert

# 重新初始化数据库
pnpm run db:init
```

## 项目结构

```
mbdp/
├── frontend/                 # React前端应用
│   ├── src/
│   │   ├── components/      # 通用组件
│   │   ├── pages/          # 页面组件
│   │   ├── store/          # Redux状态管理
│   │   ├── router/         # 路由配置
│   │   └── types/          # TypeScript类型定义
│   └── package.json
├── backend/                 # Node.js后端服务
│   ├── src/
│   │   ├── controllers/    # 控制器
│   │   ├── services/       # 业务逻辑
│   │   ├── models/         # 数据模型
│   │   ├── middleware/     # 中间件
│   │   ├── routes/         # 路由
│   │   ├── migrations/     # 数据库迁移
│   │   └── utils/          # 工具函数
│   └── package.json
├── .kiro/                   # 项目规格文档
└── README.md
```

## 常见问题

### 1. 数据库连接失败
- 检查PostgreSQL服务是否运行
- 验证数据库连接参数
- 确保PostGIS扩展已安装

### 2. 前端无法连接后端
- 确保后端服务在3001端口运行
- 检查前端环境变量中的API地址

### 3. 权限错误
- 确保数据库用户有足够权限
- 检查文件上传目录权限

## 下一步

1. **浏览功能**: 探索物种数据库和交互式地图
2. **上传数据**: 使用管理员账户上传物种信息和音频文件
3. **API集成**: 参考API文档进行二次开发
4. **自定义开发**: 根据需求扩展功能

## 获取帮助

- 查看项目文档: `.kiro/specs/`
- API文档: `backend/API_TESTING.md`
- 数据库文档: `backend/DATABASE_SETUP.md`

---

🎉 **恭喜！您已成功搭建海洋生物声音平台开发环境！**
