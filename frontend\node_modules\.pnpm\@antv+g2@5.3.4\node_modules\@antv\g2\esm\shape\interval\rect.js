import { Color } from './color';
/**
 * <PERSON><PERSON> rect in different coordinate and using color channel for stroke and fill attribute.
 * The stroke attribute is valid with specified lineWidth attribute which defaults to zero.
 */
export const Rect = (options, context) => {
    return Color(Object.assign({ colorAttribute: 'fill' }, options), context);
};
Rect.props = Object.assign(Object.assign({}, Color.props), { defaultMarker: 'square' });
//# sourceMappingURL=rect.js.map