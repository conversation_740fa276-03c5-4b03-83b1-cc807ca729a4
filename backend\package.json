{"name": "backend", "version": "1.0.0", "description": "海洋生物声音平台后端服务", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "db:init": "ts-node src/scripts/initDatabase.ts", "db:migration:generate": "typeorm-ts-node-commonjs migration:generate", "db:migration:run": "typeorm-ts-node-commonjs migration:run", "db:migration:revert": "typeorm-ts-node-commonjs migration:revert", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["marine", "biology", "sound", "platform"], "author": "", "license": "ISC", "packageManager": "pnpm@10.11.0", "dependencies": {"@types/uuid": "^10.0.0", "@types/xml2js": "^0.4.14", "bcryptjs": "^3.0.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^4.21.2", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.1", "multer": "^2.0.2", "pg": "^8.16.3", "reflect-metadata": "^0.2.2", "typeorm": "^0.3.25", "uuid": "^11.1.0", "xml2js": "^0.6.2"}, "devDependencies": {"@types/bcryptjs": "^3.0.0", "@types/cors": "^2.8.19", "@types/express": "^4.17.23", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.10", "@types/multer": "^2.0.0", "@types/node": "^24.0.14", "@types/pg": "^8.15.4", "@typescript-eslint/eslint-plugin": "^8.37.0", "@typescript-eslint/parser": "^8.37.0", "eslint": "^9.31.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "nodemon": "^3.1.10", "prettier": "^3.6.2", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}