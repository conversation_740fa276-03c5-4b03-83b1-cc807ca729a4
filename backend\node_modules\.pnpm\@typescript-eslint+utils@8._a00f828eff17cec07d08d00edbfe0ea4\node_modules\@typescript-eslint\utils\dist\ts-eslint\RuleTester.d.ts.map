{"version": 3, "file": "RuleTester.d.ts", "sourceRoot": "", "sources": ["../../src/ts-eslint/RuleTester.ts"], "names": [], "mappings": "AAGA,OAAO,KAAK,EAAE,cAAc,EAAE,eAAe,EAAE,MAAM,cAAc,CAAC;AACpE,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,UAAU,CAAC;AAC9C,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AACvC,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AACrD,OAAO,KAAK,EACV,2BAA2B,EAC3B,kBAAkB,EAClB,UAAU,EACV,2BAA2B,EAC5B,MAAM,QAAQ,CAAC;AAEhB;;GAEG;AACH,MAAM,WAAW,aAAa,CAAC,OAAO,SAAS,SAAS,OAAO,EAAE;IAC/D;;OAEG;IACH,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC;IACtB;;OAEG;IACH,QAAQ,CAAC,GAAG,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;IAClD;;OAEG;IACH,QAAQ,CAAC,QAAQ,CAAC,EAAE,MAAM,CAAC;IAC3B;;OAEG;IACH,QAAQ,CAAC,OAAO,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;IAClD;;OAEG;IACH,QAAQ,CAAC,IAAI,CAAC,EAAE,MAAM,CAAC;IACvB;;OAEG;IACH,QAAQ,CAAC,IAAI,CAAC,EAAE,OAAO,CAAC;IACxB;;OAEG;IACH,QAAQ,CAAC,OAAO,CAAC,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;IACrC;;OAEG;IACH,QAAQ,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC;IACzB;;OAEG;IACH,QAAQ,CAAC,aAAa,CAAC,EAAE,QAAQ,CAAC,aAAa,CAAC,CAAC;IACjD;;OAEG;IACH,QAAQ,CAAC,QAAQ,CAAC,EAAE,QAAQ,CAAC,2BAA2B,CAAC,CAAC;CAC3D;AAED;;GAEG;AACH,MAAM,WAAW,gBAAgB,CAAC,UAAU,SAAS,MAAM;IACzD;;OAEG;IACH,QAAQ,CAAC,IAAI,CAAC,EAAE,2BAA2B,CAAC;IAC5C;;OAEG;IACH,QAAQ,CAAC,SAAS,EAAE,UAAU,CAAC;IAC/B;;;OAGG;IACH,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC;CAIzB;AAED;;GAEG;AACH,MAAM,WAAW,eAAe,CAC9B,UAAU,SAAS,MAAM,EACzB,OAAO,SAAS,SAAS,OAAO,EAAE,CAClC,SAAQ,aAAa,CAAC,OAAO,CAAC;IAC9B;;OAEG;IACH,QAAQ,CAAC,MAAM,EAAE,SAAS,aAAa,CAAC,UAAU,CAAC,EAAE,CAAC;IACtD;;OAEG;IACH,QAAQ,CAAC,MAAM,CAAC,EAAE,MAAM,GAAG,MAAM,EAAE,GAAG,IAAI,CAAC;CAC5C;AAED;;GAEG;AACH,MAAM,WAAW,aAAa,CAAC,UAAU,SAAS,MAAM;IACtD;;OAEG;IACH,QAAQ,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC;IACzB;;OAEG;IACH,QAAQ,CAAC,IAAI,CAAC,EAAE,2BAA2B,CAAC;IAC5C;;OAEG;IACH,QAAQ,CAAC,SAAS,CAAC,EAAE,MAAM,CAAC;IAC5B;;OAEG;IACH,QAAQ,CAAC,OAAO,CAAC,EAAE,MAAM,CAAC;IAC1B;;OAEG;IACH,QAAQ,CAAC,IAAI,CAAC,EAAE,MAAM,CAAC;IACvB;;OAEG;IACH,QAAQ,CAAC,SAAS,EAAE,UAAU,CAAC;IAC/B;;OAEG;IACH,QAAQ,CAAC,WAAW,CAAC,EAAE,SAAS,gBAAgB,CAAC,UAAU,CAAC,EAAE,GAAG,IAAI,CAAC;IACtE;;OAEG;IACH,QAAQ,CAAC,IAAI,CAAC,EAAE,cAAc,GAAG,eAAe,CAAC;CAIlD;AAED;;;GAGG;AACH,MAAM,MAAM,+BAA+B,GAAG,CAC5C,IAAI,EAAE,MAAM,EACZ,QAAQ,EAAE,MAAM,IAAI,KACjB,IAAI,CAAC;AAEV;;GAEG;AACH,MAAM,WAAW,QAAQ,CACvB,UAAU,SAAS,MAAM,EACzB,OAAO,SAAS,SAAS,OAAO,EAAE;IAGlC,QAAQ,CAAC,OAAO,EAAE,SAAS,eAAe,CAAC,UAAU,EAAE,OAAO,CAAC,EAAE,CAAC;IAClE,QAAQ,CAAC,KAAK,EAAE,SAAS,CAAC,MAAM,GAAG,aAAa,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC;CAC9D;AAED;;GAEG;AACH,MAAM,WAAW,gBAAiB,SAAQ,aAAa,CAAC,MAAM;IAE5D,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC;IACxB,QAAQ,CAAC,aAAa,CAAC,EAAE,QAAQ,CAAC,aAAa,CAAC,CAAC;CAClD;AAED;;GAEG;AAEH,OAAO,OAAO,cAAc;IAC1B;;;OAGG;gBACS,YAAY,CAAC,EAAE,gBAAgB;IAE3C;;;;;OAKG;IACH,GAAG,CAAC,UAAU,SAAS,MAAM,EAAE,OAAO,SAAS,SAAS,OAAO,EAAE,EAC/D,QAAQ,EAAE,MAAM,EAChB,IAAI,EAAE,UAAU,CAAC,UAAU,EAAE,OAAO,CAAC,EACrC,KAAK,EAAE,QAAQ,CAAC,UAAU,EAAE,OAAO,CAAC,GACnC,IAAI;IAEP;;;OAGG;IACH,MAAM,KAAK,QAAQ,IAAI,+BAA+B,CAAC;IACvD,MAAM,KAAK,QAAQ,CAAC,KAAK,EAAE,+BAA+B,GAAG,SAAS,EAAE;IAExE;;;OAGG;IACH,MAAM,KAAK,EAAE,IAAI,+BAA+B,CAAC;IACjD,MAAM,KAAK,EAAE,CAAC,KAAK,EAAE,+BAA+B,GAAG,SAAS,EAAE;IAElE;;;OAGG;IACH,MAAM,KAAK,MAAM,IAAI,+BAA+B,CAAC;IACrD,MAAM,KAAK,MAAM,CAAC,KAAK,EAAE,+BAA+B,GAAG,SAAS,EAAE;IAEtE;;OAEG;IACH,UAAU,CAAC,UAAU,SAAS,MAAM,EAAE,OAAO,SAAS,SAAS,OAAO,EAAE,EACtE,IAAI,EAAE,MAAM,EACZ,IAAI,EACA,kBAAkB,CAAC,UAAU,EAAE,OAAO,CAAC,GACvC,UAAU,CAAC,UAAU,EAAE,OAAO,CAAC,GAClC,IAAI;CACR;+BAKoD,OAAO,cAAc;AAH1E;;GAEG;AACH,qBAAa,UAAW,SAAQ,eAA2C;CAAG"}