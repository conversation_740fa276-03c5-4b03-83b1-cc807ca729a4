{"version": 3, "sources": ["../browser/src/metadata-builder/MetadataUtils.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,MAAM,OAAO,aAAa;IACtB;;;;;OAKG;IACH,MAAM,CAAC,kBAAkB,CAAC,MAAgB;QACtC,MAAM,IAAI,GAAe,CAAC,MAAM,CAAC,CAAA;QACjC,MAAM,cAAc,GAAG,CAAC,MAAgB,EAAQ,EAAE;YAC9C,MAAM,KAAK,GAAG,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;YAC3C,IAAI,KAAK,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC;gBACtB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;gBAChB,cAAc,CAAC,KAAK,CAAC,CAAA;YACzB,CAAC;QACL,CAAC,CAAA;QACD,cAAc,CAAC,MAAM,CAAC,CAAA;QACtB,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,WAAW,CAAC,OAAiB,EAAE,OAAiB;QACnD,OAAO,OAAO,CAAC,SAAS,YAAY,OAAO,CAAA;IAC/C,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,cAAc,CACjB,KAAU,EACV,OAAe;QAEf,IAAI,CAAC,OAAO;YAAE,OAAO,KAAK,CAAA;QAC1B,OAAO,KAAK,CAAC,MAAM,CACf,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAC/D,CAAA;IACL,CAAC;CACJ", "file": "MetadataUtils.js", "sourcesContent": ["/**\n * Metadata args utility functions.\n */\nexport class MetadataUtils {\n    /**\n     * Gets given's entity all inherited classes.\n     * Gives in order from parents to children.\n     * For example Post extends ContentModel which extends Unit it will give\n     * [Unit, ContentModel, Post]\n     */\n    static getInheritanceTree(entity: Function): Function[] {\n        const tree: Function[] = [entity]\n        const getPrototypeOf = (object: Function): void => {\n            const proto = Object.getPrototypeOf(object)\n            if (proto && proto.name) {\n                tree.push(proto)\n                getPrototypeOf(proto)\n            }\n        }\n        getPrototypeOf(entity)\n        return tree\n    }\n\n    /**\n     * Checks if this table is inherited from another table.\n     */\n    static isInherited(target1: Function, target2: Function) {\n        return target1.prototype instanceof target2\n    }\n\n    /**\n     * Filters given array of targets by a given classes.\n     * If classes are not given, then it returns array itself.\n     */\n    static filterByTarget<T extends { target?: any }>(\n        array: T[],\n        classes?: any[],\n    ): T[] {\n        if (!classes) return array\n        return array.filter(\n            (item) => item.target && classes.indexOf(item.target) !== -1,\n        )\n    }\n}\n"], "sourceRoot": ".."}