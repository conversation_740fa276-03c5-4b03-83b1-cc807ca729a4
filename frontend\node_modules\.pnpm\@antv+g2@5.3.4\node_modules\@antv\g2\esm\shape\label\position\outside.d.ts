import { Coordinate } from '@antv/coord';
import { Vector2 } from '../../../runtime';
import type { LabelPosition } from './default';
export declare function linePoints(center: Vector2, angle: any, radius: any, radius1: any, offsetX: any): number[][];
export declare function radiusOf(points: any, value: any, coordinate: any): number;
export declare function angleOf(points: any, value: any, coordinate: any): number;
export declare function inferOutsideCircularStyle(position: LabelPosition, points: Vector2[], value: Record<string, any>, coordinate: Coordinate): {
    connector: any;
    connectorPoints: number[][];
    textAlign: string;
    textBaseline: string;
    rotate: number;
    x0: number;
    y0: number;
    x: number;
    y: number;
};
export declare function outside(position: LabelPosition, points: Vector2[], value: Record<string, any>, coordinate: Coordinate): any;
