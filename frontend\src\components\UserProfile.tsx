import React, { useState } from 'react';
import { 
  Card, 
  Avatar, 
  Typography, 
  Descriptions, 
  Button, 
  Space, 
  Modal,
  Form,
  Input,
  message,
  Tag
} from 'antd';
import { 
  UserOutlined, 
  EditOutlined, 
  LockOutlined,
  MailOutlined
} from '@ant-design/icons';
import { useAppSelector, useAppDispatch } from '../store/hooks';
import { changePassword } from '../store/slices/authSlice';

const { Title, Text } = Typography;

const UserProfile: React.FC = () => {
  const [passwordModalVisible, setPasswordModalVisible] = useState(false);
  const [passwordForm] = Form.useForm();
  
  const dispatch = useAppDispatch();
  const { user, loading } = useAppSelector(state => state.auth);

  const handleChangePassword = async (values: any) => {
    try {
      await dispatch(changePassword(values)).unwrap();
      message.success('密码修改成功');
      setPasswordModalVisible(false);
      passwordForm.resetFields();
    } catch (error) {
      message.error(error as string);
    }
  };

  if (!user) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '40px' }}>
          <Text type="secondary">请先登录</Text>
        </div>
      </Card>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <div style={{ display: 'flex', alignItems: 'flex-start', marginBottom: '24px' }}>
          <Avatar 
            size={80} 
            icon={<UserOutlined />}
            style={{ marginRight: '24px' }}
          />
          <div style={{ flex: 1 }}>
            <Title level={3} style={{ margin: 0, marginBottom: '8px' }}>
              {user.username}
            </Title>
            <Space wrap>
              {user.roles.map(role => (
                <Tag key={role.id} color="blue">
                  {role.description || role.name}
                </Tag>
              ))}
            </Space>
          </div>
          <Space>
            <Button icon={<EditOutlined />}>
              编辑资料
            </Button>
            <Button 
              icon={<LockOutlined />}
              onClick={() => setPasswordModalVisible(true)}
            >
              修改密码
            </Button>
          </Space>
        </div>

        <Descriptions column={2} bordered>
          <Descriptions.Item label="用户名">
            {user.username}
          </Descriptions.Item>
          <Descriptions.Item label="邮箱">
            {user.email || '未设置'}
          </Descriptions.Item>
          <Descriptions.Item label="角色">
            {user.roles.map(role => role.description || role.name).join(', ')}
          </Descriptions.Item>
          <Descriptions.Item label="最后登录">
            {user.lastLogin ? new Date(user.lastLogin).toLocaleString() : '从未登录'}
          </Descriptions.Item>
          <Descriptions.Item label="注册时间">
            {new Date(user.createdAt).toLocaleString()}
          </Descriptions.Item>
          <Descriptions.Item label="更新时间">
            {new Date(user.updatedAt).toLocaleString()}
          </Descriptions.Item>
        </Descriptions>
      </Card>

      {/* 修改密码模态框 */}
      <Modal
        title="修改密码"
        open={passwordModalVisible}
        onCancel={() => {
          setPasswordModalVisible(false);
          passwordForm.resetFields();
        }}
        footer={null}
      >
        <Form
          form={passwordForm}
          layout="vertical"
          onFinish={handleChangePassword}
        >
          <Form.Item
            name="oldPassword"
            label="当前密码"
            rules={[
              { required: true, message: '请输入当前密码' }
            ]}
          >
            <Input.Password 
              prefix={<LockOutlined />}
              placeholder="请输入当前密码"
            />
          </Form.Item>

          <Form.Item
            name="newPassword"
            label="新密码"
            rules={[
              { required: true, message: '请输入新密码' },
              { min: 6, message: '密码长度至少6个字符' }
            ]}
          >
            <Input.Password 
              prefix={<LockOutlined />}
              placeholder="请输入新密码"
            />
          </Form.Item>

          <Form.Item
            name="confirmPassword"
            label="确认新密码"
            dependencies={['newPassword']}
            rules={[
              { required: true, message: '请确认新密码' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('newPassword') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('两次输入的密码不一致'));
                },
              }),
            ]}
          >
            <Input.Password 
              prefix={<LockOutlined />}
              placeholder="请再次输入新密码"
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button 
                onClick={() => {
                  setPasswordModalVisible(false);
                  passwordForm.resetFields();
                }}
              >
                取消
              </Button>
              <Button 
                type="primary" 
                htmlType="submit"
                loading={loading}
              >
                确认修改
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default UserProfile;
