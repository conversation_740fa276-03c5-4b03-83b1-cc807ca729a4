import { Coordinate } from '@antv/coord';
import { ShapeComponent as SC, Vector2 } from '../../runtime';
export type FunnelOptions = {
    adjustPoints?: (points: Vector2[], nextPoints: Vector2[], coordinate: Coordinate) => Vector2[];
    [key: string]: any;
};
/**
 * Render funnel in different coordinate and using color channel for stroke and fill attribute.
 */
export declare const Funnel: SC<FunnelOptions>;
