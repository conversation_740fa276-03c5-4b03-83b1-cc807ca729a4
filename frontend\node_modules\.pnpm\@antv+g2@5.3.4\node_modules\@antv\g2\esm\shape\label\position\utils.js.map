{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../../../src/shape/label/position/utils.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,EAAE,MAAM,uBAAuB,CAAC;AAE7C,8GAA8G;AAC9G,MAAM,UAAU,MAAM,CACpB,MAA6B,EAC7B,UAA+B,EAAE;IAEjC,MAAM,EAAE,WAAW,GAAG,EAAE,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;IAE7C,2DAA2D;IAC3D,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9C,MAAM,CAAC,GAAG,YAAY,CAAC,MAAM,CAAC;IAC9B,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;IAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;QAC1B,MAAM,KAAK,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;QAC9B,MAAM,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC;QACpB,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,WAAW,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;KACpD;IAED,+DAA+D;IAC/D,0EAA0E;IAC1E,IAAI,OAAO,GAAG,IAAI,CAAC;IACnB,OAAO,OAAO,EAAE;QACd,OAAO,GAAG,KAAK,CAAC;QAChB,6CAA6C;QAC7C,KAAK,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YACzC,MAAM,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACrB,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAC5B,IAAI,MAAM,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,EAAE;gBACrB,OAAO,GAAG,IAAI,CAAC;gBACf,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC;gBAClC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBAEnB,6CAA6C;gBAC7C,MAAM,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC;gBAE5B,wDAAwD;gBACxD,MAAM,SAAS,GAAG,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC;gBACvC,MAAM,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,SAAS,CAAC,CAAC;gBAC7D,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,EAAE,GAAG,SAAS,CAAC;aAClC;SACF;KACF;IAED,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,KAAK,MAAM,GAAG,IAAI,KAAK,EAAE;QACvB,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,GAAG,GAAG,CAAC;QAC1B,IAAI,KAAK,GAAG,CAAC,GAAG,WAAW,CAAC;QAC5B,KAAK,MAAM,IAAI,IAAI,MAAM,EAAE;YACzB,MAAM,KAAK,GAAG,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC;YAChC,MAAM,SAAS,GAAG,KAAK,GAAG,WAAW,CAAC;YACtC,MAAM,EAAE,GAAG,SAAS,GAAG,IAAI,CAAC;YAC5B,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YAClC,KAAK,CAAC,CAAC,GAAG,KAAK,GAAG,WAAW,CAAC;YAC9B,KAAK,IAAI,WAAW,CAAC;SACtB;KACF;AACH,CAAC;AAED,MAAM,UAAU,aAAa,CAC3B,QAA+B,EAC/B,OAA4B;IAE5B,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1C,MAAM,EAAE,MAAM,EAAE,WAAW,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;IAC7C,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC,CAAC;IACjD,IAAI,MAAM,CAAC,MAAM,IAAI,QAAQ;QAAE,OAAO,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAC9D,MAAM,QAAQ,GAAG,EAAE,CAAC;IACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACtC,4BAA4B;QAC5B,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,QAAQ,EAAE;YAChC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC;YACtB,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC;SAC7B;;YAAM,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;KACjC;IACD,MAAM,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;AAC5B,CAAC"}