export declare const AXIS_CLASS_NAME = "axis";
export declare const AXIS_LINE_CLASS_NAME = "axis-line";
export declare const AXIS_MAIN_CLASS_NAME = "axis-main-group";
export declare const AXIS_HOT_AREA_CLASS_NAME = "axis-hot-area";
export declare function brushAxisHighlight(root: any, { axes: axesOf, // given root, return axes
elements: elementsOf, // given root, return elements
points: pointsOf, // given shape, return control points
horizontal: isHorizontal, // given axis, return direction
datum, // given shape, return datum
offsetY, // offsetY for shape area
offsetX, // offsetX for shape area
reverse, state, emitter, coordinate, ...rest }: {
    [x: string]: any;
    axes: any;
    elements: any;
    points: any;
    horizontal: any;
    datum: any;
    offsetY: any;
    offsetX: any;
    reverse?: boolean;
    state?: {};
    emitter: any;
    coordinate: any;
}): () => void;
/**
 * @todo Support mask size.
 */
export declare function BrushAxisHighlight(options: any): (target: any, _: any, emitter: any) => () => void;
