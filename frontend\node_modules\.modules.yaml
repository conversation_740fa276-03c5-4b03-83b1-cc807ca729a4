hoistPattern:
  - '*'
hoistedDependencies:
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@ant-design/charts-util@0.0.1-alpha.7(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@ant-design/charts-util': private
  '@ant-design/colors@8.0.0':
    '@ant-design/colors': private
  '@ant-design/cssinjs-utils@1.1.3(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@ant-design/cssinjs-utils': private
  '@ant-design/cssinjs@1.24.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@ant-design/cssinjs': private
  '@ant-design/fast-color@2.0.6':
    '@ant-design/fast-color': private
  '@ant-design/graphs@2.1.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(workerize-loader@2.0.2(webpack@5.100.2))':
    '@ant-design/graphs': private
  '@ant-design/icons-svg@4.4.2':
    '@ant-design/icons-svg': private
  '@ant-design/plots@2.6.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@ant-design/plots': private
  '@ant-design/react-slick@1.1.2(react@19.1.0)':
    '@ant-design/react-slick': private
  '@antv/algorithm@0.1.26':
    '@antv/algorithm': private
  '@antv/component@2.1.4':
    '@antv/component': private
  '@antv/coord@0.4.7':
    '@antv/coord': private
  '@antv/event-emitter@0.1.3':
    '@antv/event-emitter': private
  '@antv/expr@1.0.2':
    '@antv/expr': private
  '@antv/g-camera-api@2.0.40':
    '@antv/g-camera-api': private
  '@antv/g-canvas@2.0.47':
    '@antv/g-canvas': private
  '@antv/g-dom-mutation-observer-api@2.0.37':
    '@antv/g-dom-mutation-observer-api': private
  '@antv/g-lite@2.3.1':
    '@antv/g-lite': private
  '@antv/g-math@3.0.1':
    '@antv/g-math': private
  '@antv/g-plugin-canvas-path-generator@2.1.21':
    '@antv/g-plugin-canvas-path-generator': private
  '@antv/g-plugin-canvas-picker@2.1.26':
    '@antv/g-plugin-canvas-picker': private
  '@antv/g-plugin-canvas-renderer@2.3.2':
    '@antv/g-plugin-canvas-renderer': private
  '@antv/g-plugin-dom-interaction@2.1.26':
    '@antv/g-plugin-dom-interaction': private
  '@antv/g-plugin-dragndrop@2.0.37':
    '@antv/g-plugin-dragndrop': private
  '@antv/g-plugin-html-renderer@2.1.26':
    '@antv/g-plugin-html-renderer': private
  '@antv/g-plugin-image-loader@2.1.25':
    '@antv/g-plugin-image-loader': private
  '@antv/g-plugin-svg-picker@2.0.41':
    '@antv/g-plugin-svg-picker': private
  '@antv/g-plugin-svg-renderer@2.2.23':
    '@antv/g-plugin-svg-renderer': private
  '@antv/g-svg@2.0.41':
    '@antv/g-svg': private
  '@antv/g-web-animations-api@2.1.27':
    '@antv/g-web-animations-api': private
  '@antv/g2-extension-plot@0.2.2':
    '@antv/g2-extension-plot': private
  '@antv/g2@5.3.4':
    '@antv/g2': private
  '@antv/g6-extension-react@0.2.4(@antv/g6@5.0.49(workerize-loader@2.0.2(webpack@5.100.2)))(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@antv/g6-extension-react': private
  '@antv/g6@5.0.49(workerize-loader@2.0.2(webpack@5.100.2))':
    '@antv/g6': private
  '@antv/g@6.1.27':
    '@antv/g': private
  '@antv/graphin@3.0.5(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(workerize-loader@2.0.2(webpack@5.100.2))':
    '@antv/graphin': private
  '@antv/graphlib@2.0.4':
    '@antv/graphlib': private
  '@antv/hierarchy@0.6.14':
    '@antv/hierarchy': private
  '@antv/layout@1.2.14-beta.9(workerize-loader@2.0.2(webpack@5.100.2))':
    '@antv/layout': private
  '@antv/scale@0.4.16':
    '@antv/scale': private
  '@antv/util@3.3.11':
    '@antv/util': private
  '@antv/vendor@1.0.11':
    '@antv/vendor': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/compat-data@7.28.0':
    '@babel/compat-data': private
  '@babel/core@7.28.0':
    '@babel/core': private
  '@babel/generator@7.28.0':
    '@babel/generator': private
  '@babel/helper-compilation-targets@7.27.2':
    '@babel/helper-compilation-targets': private
  '@babel/helper-globals@7.28.0':
    '@babel/helper-globals': private
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.27.3(@babel/core@7.28.0)':
    '@babel/helper-module-transforms': private
  '@babel/helper-plugin-utils@7.27.1':
    '@babel/helper-plugin-utils': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.27.1':
    '@babel/helper-validator-option': private
  '@babel/helpers@7.27.6':
    '@babel/helpers': private
  '@babel/parser@7.28.0':
    '@babel/parser': private
  '@babel/plugin-transform-react-jsx-self@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-react-jsx-self': private
  '@babel/plugin-transform-react-jsx-source@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-react-jsx-source': private
  '@babel/runtime@7.27.6':
    '@babel/runtime': private
  '@babel/template@7.27.2':
    '@babel/template': private
  '@babel/traverse@7.28.0':
    '@babel/traverse': private
  '@babel/types@7.28.1':
    '@babel/types': private
  '@emotion/hash@0.8.0':
    '@emotion/hash': private
  '@emotion/is-prop-valid@1.2.2':
    '@emotion/is-prop-valid': private
  '@emotion/memoize@0.8.1':
    '@emotion/memoize': private
  '@emotion/unitless@0.7.5':
    '@emotion/unitless': private
  '@esbuild/aix-ppc64@0.25.6':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.25.6':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.25.6':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.25.6':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.25.6':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.25.6':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.25.6':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.25.6':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.25.6':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.25.6':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.25.6':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.25.6':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.25.6':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.25.6':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.25.6':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.25.6':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.25.6':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-arm64@0.25.6':
    '@esbuild/netbsd-arm64': private
  '@esbuild/netbsd-x64@0.25.6':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-arm64@0.25.6':
    '@esbuild/openbsd-arm64': private
  '@esbuild/openbsd-x64@0.25.6':
    '@esbuild/openbsd-x64': private
  '@esbuild/openharmony-arm64@0.25.6':
    '@esbuild/openharmony-arm64': private
  '@esbuild/sunos-x64@0.25.6':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.25.6':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.25.6':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.25.6':
    '@esbuild/win32-x64': private
  '@eslint-community/eslint-utils@4.7.0(eslint@9.31.0)':
    '@eslint-community/eslint-utils': private
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': private
  '@eslint/config-array@0.21.0':
    '@eslint/config-array': private
  '@eslint/config-helpers@0.3.0':
    '@eslint/config-helpers': private
  '@eslint/core@0.15.1':
    '@eslint/core': private
  '@eslint/eslintrc@3.3.1':
    '@eslint/eslintrc': private
  '@eslint/object-schema@2.1.6':
    '@eslint/object-schema': private
  '@eslint/plugin-kit@0.3.3':
    '@eslint/plugin-kit': private
  '@humanfs/core@0.19.1':
    '@humanfs/core': private
  '@humanfs/node@0.16.6':
    '@humanfs/node': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/retry@0.4.3':
    '@humanwhocodes/retry': private
  '@jridgewell/gen-mapping@0.3.12':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/source-map@0.3.10':
    '@jridgewell/source-map': private
  '@jridgewell/sourcemap-codec@1.5.4':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.29':
    '@jridgewell/trace-mapping': private
  '@naoak/workerize-transferable@0.1.0(workerize-loader@2.0.2(webpack@5.100.2))':
    '@naoak/workerize-transferable': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@rc-component/async-validator@5.0.4':
    '@rc-component/async-validator': private
  '@rc-component/color-picker@2.0.1(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@rc-component/color-picker': private
  '@rc-component/context@1.4.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@rc-component/context': private
  '@rc-component/mini-decimal@1.1.0':
    '@rc-component/mini-decimal': private
  '@rc-component/mutate-observer@1.1.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@rc-component/mutate-observer': private
  '@rc-component/portal@1.1.2(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@rc-component/portal': private
  '@rc-component/qrcode@1.0.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@rc-component/qrcode': private
  '@rc-component/tour@1.15.1(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@rc-component/tour': private
  '@rc-component/trigger@2.2.7(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@rc-component/trigger': private
  '@rc-component/util@1.2.1(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@rc-component/util': private
  '@react-leaflet/core@3.0.0(leaflet@1.9.4)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@react-leaflet/core': private
  '@rolldown/pluginutils@1.0.0-beta.27':
    '@rolldown/pluginutils': private
  '@rollup/rollup-android-arm-eabi@4.45.1':
    '@rollup/rollup-android-arm-eabi': private
  '@rollup/rollup-android-arm64@4.45.1':
    '@rollup/rollup-android-arm64': private
  '@rollup/rollup-darwin-arm64@4.45.1':
    '@rollup/rollup-darwin-arm64': private
  '@rollup/rollup-darwin-x64@4.45.1':
    '@rollup/rollup-darwin-x64': private
  '@rollup/rollup-freebsd-arm64@4.45.1':
    '@rollup/rollup-freebsd-arm64': private
  '@rollup/rollup-freebsd-x64@4.45.1':
    '@rollup/rollup-freebsd-x64': private
  '@rollup/rollup-linux-arm-gnueabihf@4.45.1':
    '@rollup/rollup-linux-arm-gnueabihf': private
  '@rollup/rollup-linux-arm-musleabihf@4.45.1':
    '@rollup/rollup-linux-arm-musleabihf': private
  '@rollup/rollup-linux-arm64-gnu@4.45.1':
    '@rollup/rollup-linux-arm64-gnu': private
  '@rollup/rollup-linux-arm64-musl@4.45.1':
    '@rollup/rollup-linux-arm64-musl': private
  '@rollup/rollup-linux-loongarch64-gnu@4.45.1':
    '@rollup/rollup-linux-loongarch64-gnu': private
  '@rollup/rollup-linux-powerpc64le-gnu@4.45.1':
    '@rollup/rollup-linux-powerpc64le-gnu': private
  '@rollup/rollup-linux-riscv64-gnu@4.45.1':
    '@rollup/rollup-linux-riscv64-gnu': private
  '@rollup/rollup-linux-riscv64-musl@4.45.1':
    '@rollup/rollup-linux-riscv64-musl': private
  '@rollup/rollup-linux-s390x-gnu@4.45.1':
    '@rollup/rollup-linux-s390x-gnu': private
  '@rollup/rollup-linux-x64-gnu@4.45.1':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.45.1':
    '@rollup/rollup-linux-x64-musl': private
  '@rollup/rollup-win32-arm64-msvc@4.45.1':
    '@rollup/rollup-win32-arm64-msvc': private
  '@rollup/rollup-win32-ia32-msvc@4.45.1':
    '@rollup/rollup-win32-ia32-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.45.1':
    '@rollup/rollup-win32-x64-msvc': private
  '@standard-schema/spec@1.0.0':
    '@standard-schema/spec': private
  '@standard-schema/utils@0.3.0':
    '@standard-schema/utils': private
  '@types/babel__core@7.20.5':
    '@types/babel__core': private
  '@types/babel__generator@7.27.0':
    '@types/babel__generator': private
  '@types/babel__template@7.4.4':
    '@types/babel__template': private
  '@types/babel__traverse@7.20.7':
    '@types/babel__traverse': private
  '@types/d3-array@3.2.1':
    '@types/d3-array': private
  '@types/d3-color@3.1.3':
    '@types/d3-color': private
  '@types/d3-dispatch@3.0.6':
    '@types/d3-dispatch': private
  '@types/d3-dsv@3.0.7':
    '@types/d3-dsv': private
  '@types/d3-ease@3.0.2':
    '@types/d3-ease': private
  '@types/d3-fetch@3.0.7':
    '@types/d3-fetch': private
  '@types/d3-force@3.0.10':
    '@types/d3-force': private
  '@types/d3-format@3.0.4':
    '@types/d3-format': private
  '@types/d3-geo@3.1.0':
    '@types/d3-geo': private
  '@types/d3-hierarchy@3.1.7':
    '@types/d3-hierarchy': private
  '@types/d3-interpolate@3.0.4':
    '@types/d3-interpolate': private
  '@types/d3-path@3.1.1':
    '@types/d3-path': private
  '@types/d3-quadtree@3.0.6':
    '@types/d3-quadtree': private
  '@types/d3-random@3.0.3':
    '@types/d3-random': private
  '@types/d3-scale-chromatic@3.1.0':
    '@types/d3-scale-chromatic': private
  '@types/d3-scale@4.0.9':
    '@types/d3-scale': private
  '@types/d3-shape@3.1.7':
    '@types/d3-shape': private
  '@types/d3-time@3.0.4':
    '@types/d3-time': private
  '@types/d3-timer@3.0.2':
    '@types/d3-timer': private
  '@types/debounce@1.2.4':
    '@types/debounce': private
  '@types/eslint-scope@3.7.7':
    '@types/eslint-scope': private
  '@types/eslint@9.6.1':
    '@types/eslint': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/geojson@7946.0.16':
    '@types/geojson': private
  '@types/history@4.7.11':
    '@types/history': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/node@24.0.14':
    '@types/node': private
  '@types/react-router@5.1.20':
    '@types/react-router': private
  '@types/stylis@4.2.5':
    '@types/stylis': private
  '@types/use-sync-external-store@0.0.6':
    '@types/use-sync-external-store': private
  '@typescript-eslint/eslint-plugin@8.37.0(@typescript-eslint/parser@8.37.0(eslint@9.31.0)(typescript@5.8.3))(eslint@9.31.0)(typescript@5.8.3)':
    '@typescript-eslint/eslint-plugin': private
  '@typescript-eslint/parser@8.37.0(eslint@9.31.0)(typescript@5.8.3)':
    '@typescript-eslint/parser': private
  '@typescript-eslint/project-service@8.37.0(typescript@5.8.3)':
    '@typescript-eslint/project-service': private
  '@typescript-eslint/scope-manager@8.37.0':
    '@typescript-eslint/scope-manager': private
  '@typescript-eslint/tsconfig-utils@8.37.0(typescript@5.8.3)':
    '@typescript-eslint/tsconfig-utils': private
  '@typescript-eslint/type-utils@8.37.0(eslint@9.31.0)(typescript@5.8.3)':
    '@typescript-eslint/type-utils': private
  '@typescript-eslint/types@8.37.0':
    '@typescript-eslint/types': private
  '@typescript-eslint/typescript-estree@8.37.0(typescript@5.8.3)':
    '@typescript-eslint/typescript-estree': private
  '@typescript-eslint/utils@8.37.0(eslint@9.31.0)(typescript@5.8.3)':
    '@typescript-eslint/utils': private
  '@typescript-eslint/visitor-keys@8.37.0':
    '@typescript-eslint/visitor-keys': private
  '@webassemblyjs/ast@1.14.1':
    '@webassemblyjs/ast': private
  '@webassemblyjs/floating-point-hex-parser@1.13.2':
    '@webassemblyjs/floating-point-hex-parser': private
  '@webassemblyjs/helper-api-error@1.13.2':
    '@webassemblyjs/helper-api-error': private
  '@webassemblyjs/helper-buffer@1.14.1':
    '@webassemblyjs/helper-buffer': private
  '@webassemblyjs/helper-numbers@1.13.2':
    '@webassemblyjs/helper-numbers': private
  '@webassemblyjs/helper-wasm-bytecode@1.13.2':
    '@webassemblyjs/helper-wasm-bytecode': private
  '@webassemblyjs/helper-wasm-section@1.14.1':
    '@webassemblyjs/helper-wasm-section': private
  '@webassemblyjs/ieee754@1.13.2':
    '@webassemblyjs/ieee754': private
  '@webassemblyjs/leb128@1.13.2':
    '@webassemblyjs/leb128': private
  '@webassemblyjs/utf8@1.13.2':
    '@webassemblyjs/utf8': private
  '@webassemblyjs/wasm-edit@1.14.1':
    '@webassemblyjs/wasm-edit': private
  '@webassemblyjs/wasm-gen@1.14.1':
    '@webassemblyjs/wasm-gen': private
  '@webassemblyjs/wasm-opt@1.14.1':
    '@webassemblyjs/wasm-opt': private
  '@webassemblyjs/wasm-parser@1.14.1':
    '@webassemblyjs/wasm-parser': private
  '@webassemblyjs/wast-printer@1.14.1':
    '@webassemblyjs/wast-printer': private
  '@xtuc/ieee754@1.2.0':
    '@xtuc/ieee754': private
  '@xtuc/long@4.2.2':
    '@xtuc/long': private
  acorn-import-phases@1.0.4(acorn@8.15.0):
    acorn-import-phases: private
  acorn-jsx@5.3.2(acorn@8.15.0):
    acorn-jsx: private
  acorn@8.15.0:
    acorn: private
  ajv-formats@2.1.1(ajv@8.17.1):
    ajv-formats: private
  ajv-keywords@5.1.0(ajv@8.17.1):
    ajv-keywords: private
  ajv@6.12.6:
    ajv: private
  ansi-styles@4.3.0:
    ansi-styles: private
  argparse@2.0.1:
    argparse: private
  asynckit@0.4.0:
    asynckit: private
  balanced-match@1.0.2:
    balanced-match: private
  big.js@5.2.2:
    big.js: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserslist@4.25.1:
    browserslist: private
  bubblesets-js@2.3.4:
    bubblesets-js: private
  buffer-from@1.1.2:
    buffer-from: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  callsites@3.1.0:
    callsites: private
  camelize@1.0.1:
    camelize: private
  caniuse-lite@1.0.30001727:
    caniuse-lite: private
  chalk@4.1.2:
    chalk: private
  chrome-trace-event@1.0.4:
    chrome-trace-event: private
  classnames@2.5.1:
    classnames: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  color-string@1.9.1:
    color-string: private
  combined-stream@1.0.8:
    combined-stream: private
  comlink@4.4.2:
    comlink: private
  commander@2.20.3:
    commander: private
  compute-scroll-into-view@3.1.1:
    compute-scroll-into-view: private
  concat-map@0.0.1:
    concat-map: private
  convert-source-map@2.0.0:
    convert-source-map: private
  cookie@1.0.2:
    cookie: private
  copy-to-clipboard@3.3.3:
    copy-to-clipboard: private
  cross-spawn@7.0.6:
    cross-spawn: private
  css-color-keywords@1.0.0:
    css-color-keywords: private
  css-to-react-native@3.2.0:
    css-to-react-native: private
  csstype@3.1.3:
    csstype: private
  d3-array@3.2.4:
    d3-array: private
  d3-binarytree@1.0.2:
    d3-binarytree: private
  d3-color@3.1.0:
    d3-color: private
  d3-dispatch@3.0.1:
    d3-dispatch: private
  d3-dsv@3.0.1:
    d3-dsv: private
  d3-ease@3.0.1:
    d3-ease: private
  d3-fetch@3.0.1:
    d3-fetch: private
  d3-force-3d@3.0.6:
    d3-force-3d: private
  d3-force@3.0.0:
    d3-force: private
  d3-format@3.1.0:
    d3-format: private
  d3-geo-projection@4.0.0:
    d3-geo-projection: private
  d3-geo@3.1.1:
    d3-geo: private
  d3-hierarchy@3.1.2:
    d3-hierarchy: private
  d3-interpolate@3.0.1:
    d3-interpolate: private
  d3-octree@1.1.0:
    d3-octree: private
  d3-path@3.1.0:
    d3-path: private
  d3-quadtree@3.0.1:
    d3-quadtree: private
  d3-random@3.0.1:
    d3-random: private
  d3-regression@1.3.10:
    d3-regression: private
  d3-scale-chromatic@3.1.0:
    d3-scale-chromatic: private
  d3-scale@4.0.2:
    d3-scale: private
  d3-shape@3.2.0:
    d3-shape: private
  d3-time-format@4.1.0:
    d3-time-format: private
  d3-time@3.1.0:
    d3-time: private
  d3-timer@3.0.1:
    d3-timer: private
  dagre@0.8.5:
    dagre: private
  dayjs@1.11.13:
    dayjs: private
  debug@4.4.1:
    debug: private
  deep-is@0.1.4:
    deep-is: private
  delayed-stream@1.0.0:
    delayed-stream: private
  dunder-proto@1.0.1:
    dunder-proto: private
  electron-to-chromium@1.5.187:
    electron-to-chromium: private
  emojis-list@3.0.0:
    emojis-list: private
  enhanced-resolve@5.18.2:
    enhanced-resolve: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-module-lexer@1.7.0:
    es-module-lexer: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  esbuild@0.25.6:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-scope@8.4.0:
    eslint-scope: private
  eslint-visitor-keys@4.2.1:
    eslint-visitor-keys: private
  espree@10.4.0:
    espree: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  esutils@2.0.3:
    esutils: private
  eventemitter3@5.0.1:
    eventemitter3: private
  events@3.3.0:
    events: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fast-uri@3.0.6:
    fast-uri: private
  fastq@1.19.1:
    fastq: private
  fdir@6.4.6(picomatch@4.0.3):
    fdir: private
  fecha@4.2.3:
    fecha: private
  file-entry-cache@8.0.0:
    file-entry-cache: private
  fill-range@7.1.1:
    fill-range: private
  find-up@5.0.0:
    find-up: private
  flat-cache@4.0.1:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  flru@1.0.2:
    flru: private
  follow-redirects@1.15.9:
    follow-redirects: private
  form-data@4.0.4:
    form-data: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  gensync@1.0.0-beta.2:
    gensync: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-proto@1.0.1:
    get-proto: private
  gl-matrix@3.4.3:
    gl-matrix: private
  glob-parent@6.0.2:
    glob-parent: private
  glob-to-regexp@0.4.1:
    glob-to-regexp: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  graphemer@1.4.0:
    graphemer: private
  graphlib@2.1.8:
    graphlib: private
  has-flag@4.0.0:
    has-flag: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  iconv-lite@0.6.3:
    iconv-lite: private
  ignore@5.3.2:
    ignore: private
  immer@10.1.1:
    immer: private
  import-fresh@3.3.1:
    import-fresh: private
  imurmurhash@0.1.4:
    imurmurhash: private
  internmap@2.0.3:
    internmap: private
  is-any-array@2.0.1:
    is-any-array: private
  is-arrayish@0.3.2:
    is-arrayish: private
  is-extglob@2.1.1:
    is-extglob: private
  is-glob@4.0.3:
    is-glob: private
  is-number@7.0.0:
    is-number: private
  isexe@2.0.0:
    isexe: private
  jest-worker@27.5.1:
    jest-worker: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsesc@3.1.0:
    jsesc: private
  json-buffer@3.0.1:
    json-buffer: private
  json-parse-even-better-errors@2.3.1:
    json-parse-even-better-errors: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json2mq@0.2.0:
    json2mq: private
  json5@2.2.3:
    json5: private
  keyv@4.5.4:
    keyv: private
  levn@0.4.1:
    levn: private
  loader-runner@4.3.0:
    loader-runner: private
  loader-utils@2.0.4:
    loader-utils: private
  locate-path@6.0.0:
    locate-path: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash@4.17.21:
    lodash: private
  lru-cache@5.1.1:
    lru-cache: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  merge-stream@2.0.0:
    merge-stream: private
  merge2@1.4.1:
    merge2: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  minimatch@3.1.2:
    minimatch: private
  ml-array-max@1.2.4:
    ml-array-max: private
  ml-array-min@1.2.3:
    ml-array-min: private
  ml-array-rescale@1.3.7:
    ml-array-rescale: private
  ml-matrix@6.12.1:
    ml-matrix: private
  ms@2.1.3:
    ms: private
  nanoid@3.3.11:
    nanoid: private
  natural-compare@1.4.0:
    natural-compare: private
  neo-async@2.6.2:
    neo-async: private
  node-releases@2.0.19:
    node-releases: private
  optionator@0.9.4:
    optionator: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  parent-module@1.0.1:
    parent-module: private
  path-exists@4.0.0:
    path-exists: private
  path-key@3.1.1:
    path-key: private
  pdfast@0.2.0:
    pdfast: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.3:
    picomatch: private
  postcss-value-parser@4.2.0:
    postcss-value-parser: private
  postcss@8.5.6:
    postcss: private
  prelude-ls@1.2.1:
    prelude-ls: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  punycode@2.3.1:
    punycode: private
  queue-microtask@1.2.3:
    queue-microtask: private
  quickselect@2.0.0:
    quickselect: private
  randombytes@2.1.0:
    randombytes: private
  rbush@3.0.1:
    rbush: private
  rc-cascader@3.34.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-cascader: private
  rc-checkbox@3.5.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-checkbox: private
  rc-collapse@3.9.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-collapse: private
  rc-dialog@9.6.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-dialog: private
  rc-drawer@7.3.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-drawer: private
  rc-dropdown@4.2.1(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-dropdown: private
  rc-field-form@2.7.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-field-form: private
  rc-image@7.12.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-image: private
  rc-input-number@9.5.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-input-number: private
  rc-input@1.8.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-input: private
  rc-mentions@2.20.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-mentions: private
  rc-menu@9.16.1(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-menu: private
  rc-motion@2.9.5(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-motion: private
  rc-notification@5.6.4(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-notification: private
  rc-overflow@1.4.1(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-overflow: private
  rc-pagination@5.1.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-pagination: private
  rc-picker@4.11.3(dayjs@1.11.13)(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-picker: private
  rc-progress@4.0.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-progress: private
  rc-rate@2.13.1(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-rate: private
  rc-resize-observer@1.4.3(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-resize-observer: private
  rc-segmented@2.7.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-segmented: private
  rc-select@14.16.8(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-select: private
  rc-slider@11.1.8(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-slider: private
  rc-steps@6.0.1(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-steps: private
  rc-switch@4.1.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-switch: private
  rc-table@7.51.1(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-table: private
  rc-tabs@15.6.1(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-tabs: private
  rc-textarea@1.10.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-textarea: private
  rc-tooltip@6.4.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-tooltip: private
  rc-tree-select@5.27.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-tree-select: private
  rc-tree@5.13.1(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-tree: private
  rc-upload@4.9.2(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-upload: private
  rc-util@5.44.4(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-util: private
  rc-virtual-list@3.19.1(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-virtual-list: private
  react-is@18.3.1:
    react-is: private
  react-refresh@0.17.0:
    react-refresh: private
  react-router@7.7.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    react-router: private
  redux-thunk@3.1.0(redux@5.0.1):
    redux-thunk: private
  redux@5.0.1:
    redux: private
  require-from-string@2.0.2:
    require-from-string: private
  reselect@5.1.1:
    reselect: private
  resize-observer-polyfill@1.5.1:
    resize-observer-polyfill: private
  resolve-from@4.0.0:
    resolve-from: private
  reusify@1.1.0:
    reusify: private
  rollup@4.45.1:
    rollup: private
  run-parallel@1.2.0:
    run-parallel: private
  rw@1.3.3:
    rw: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safer-buffer@2.1.2:
    safer-buffer: private
  scheduler@0.26.0:
    scheduler: private
  schema-utils@4.3.2:
    schema-utils: private
  scroll-into-view-if-needed@3.1.0:
    scroll-into-view-if-needed: private
  semver@6.3.1:
    semver: private
  serialize-javascript@6.0.2:
    serialize-javascript: private
  set-cookie-parser@2.7.1:
    set-cookie-parser: private
  shallowequal@1.1.0:
    shallowequal: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  simple-swizzle@0.2.2:
    simple-swizzle: private
  size-sensor@1.0.2:
    size-sensor: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map-support@0.5.21:
    source-map-support: private
  source-map@0.6.1:
    source-map: private
  string-convert@0.2.1:
    string-convert: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  styled-components@6.1.19(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    styled-components: private
  stylis@4.3.6:
    stylis: private
  supports-color@7.2.0:
    supports-color: private
  svg-path-parser@1.1.0:
    svg-path-parser: private
  tapable@2.2.2:
    tapable: private
  terser-webpack-plugin@5.3.14(webpack@5.100.2):
    terser-webpack-plugin: private
  terser@5.43.1:
    terser: private
  throttle-debounce@5.0.2:
    throttle-debounce: private
  tinyglobby@0.2.14:
    tinyglobby: private
  to-regex-range@5.0.1:
    to-regex-range: private
  toggle-selection@1.0.6:
    toggle-selection: private
  ts-api-utils@2.1.0(typescript@5.8.3):
    ts-api-utils: private
  tslib@2.3.0:
    tslib: private
  type-check@0.4.0:
    type-check: private
  undici-types@7.8.0:
    undici-types: private
  update-browserslist-db@1.1.3(browserslist@4.25.1):
    update-browserslist-db: private
  uri-js@4.4.1:
    uri-js: private
  use-sync-external-store@1.5.0(react@19.1.0):
    use-sync-external-store: private
  watchpack@2.4.4:
    watchpack: private
  webpack-sources@3.3.3:
    webpack-sources: private
  webpack@5.100.2:
    webpack: private
  which@2.0.2:
    which: private
  word-wrap@1.2.5:
    word-wrap: private
  workerize-loader@2.0.2(webpack@5.100.2):
    workerize-loader: private
  yallist@3.1.1:
    yallist: private
  yocto-queue@0.1.0:
    yocto-queue: private
  zrender@5.6.1:
    zrender: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.11.0
pendingBuilds: []
prunedAt: Fri, 18 Jul 2025 14:18:33 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@esbuild/aix-ppc64@0.25.6'
  - '@esbuild/android-arm64@0.25.6'
  - '@esbuild/android-arm@0.25.6'
  - '@esbuild/android-x64@0.25.6'
  - '@esbuild/darwin-arm64@0.25.6'
  - '@esbuild/darwin-x64@0.25.6'
  - '@esbuild/freebsd-arm64@0.25.6'
  - '@esbuild/freebsd-x64@0.25.6'
  - '@esbuild/linux-arm64@0.25.6'
  - '@esbuild/linux-arm@0.25.6'
  - '@esbuild/linux-ia32@0.25.6'
  - '@esbuild/linux-loong64@0.25.6'
  - '@esbuild/linux-mips64el@0.25.6'
  - '@esbuild/linux-ppc64@0.25.6'
  - '@esbuild/linux-riscv64@0.25.6'
  - '@esbuild/linux-s390x@0.25.6'
  - '@esbuild/linux-x64@0.25.6'
  - '@esbuild/netbsd-arm64@0.25.6'
  - '@esbuild/netbsd-x64@0.25.6'
  - '@esbuild/openbsd-arm64@0.25.6'
  - '@esbuild/openbsd-x64@0.25.6'
  - '@esbuild/openharmony-arm64@0.25.6'
  - '@esbuild/sunos-x64@0.25.6'
  - '@esbuild/win32-arm64@0.25.6'
  - '@esbuild/win32-ia32@0.25.6'
  - '@rollup/rollup-android-arm-eabi@4.45.1'
  - '@rollup/rollup-android-arm64@4.45.1'
  - '@rollup/rollup-darwin-arm64@4.45.1'
  - '@rollup/rollup-darwin-x64@4.45.1'
  - '@rollup/rollup-freebsd-arm64@4.45.1'
  - '@rollup/rollup-freebsd-x64@4.45.1'
  - '@rollup/rollup-linux-arm-gnueabihf@4.45.1'
  - '@rollup/rollup-linux-arm-musleabihf@4.45.1'
  - '@rollup/rollup-linux-arm64-gnu@4.45.1'
  - '@rollup/rollup-linux-arm64-musl@4.45.1'
  - '@rollup/rollup-linux-loongarch64-gnu@4.45.1'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.45.1'
  - '@rollup/rollup-linux-riscv64-gnu@4.45.1'
  - '@rollup/rollup-linux-riscv64-musl@4.45.1'
  - '@rollup/rollup-linux-s390x-gnu@4.45.1'
  - '@rollup/rollup-linux-x64-gnu@4.45.1'
  - '@rollup/rollup-linux-x64-musl@4.45.1'
  - '@rollup/rollup-win32-arm64-msvc@4.45.1'
  - '@rollup/rollup-win32-ia32-msvc@4.45.1'
  - fsevents@2.3.3
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v10
virtualStoreDir: C:\workspaces\dd\mbdp\frontend\node_modules\.pnpm
virtualStoreDirMaxLength: 60
