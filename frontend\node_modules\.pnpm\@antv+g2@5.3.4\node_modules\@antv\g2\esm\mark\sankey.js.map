{"version": 3, "file": "sankey.js", "sourceRoot": "", "sources": ["../../src/mark/sankey.ts"], "names": [], "mappings": ";;;;;;;;;;;AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AAGrC,OAAO,EAAE,MAAM,IAAI,eAAe,EAAE,MAAM,gBAAgB,CAAC;AAC3D,OAAO,EAAE,gBAAgB,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAC9D,OAAO,EAAE,UAAU,EAAE,cAAc,EAAE,MAAM,eAAe,CAAC;AAC3D,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,MAAM,SAAS,CAAC;AAEhD,MAAM,sBAAsB,GAAG;IAC7B,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG;IACpB,SAAS,EAAE,IAAI;IACf,WAAW,EAAE,IAAI;CAClB,CAAC;AAEF,MAAM,oBAAoB,GAAG;IAC3B,IAAI,EAAE,SAAS;IACf,IAAI,EAAE,KAAK;IACX,MAAM,EAAE,KAAK;IACb,MAAM,EAAE;QACN,KAAK,EAAE,SAAS;QAChB,CAAC,EAAE,GAAG;QACN,CAAC,EAAE,GAAG;KACP;IACD,KAAK,EAAE;QACL,CAAC,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE;QACvB,CAAC,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE;KACxB;IACD,KAAK,EAAE;QACL,MAAM,EAAE,MAAM;KACf;CACF,CAAC;AAEF,MAAM,oBAAoB,GAAG;IAC3B,IAAI,EAAE,SAAS;IACf,IAAI,EAAE,KAAK;IACX,MAAM,EAAE,KAAK;IACb,MAAM,EAAE;QACN,KAAK,EAAE,QAAQ;QACf,CAAC,EAAE,GAAG;QACN,CAAC,EAAE,GAAG;KACP;IACD,KAAK,EAAE;QACL,WAAW,EAAE,GAAG;QAChB,MAAM,EAAE,SAAS;KAClB;CACF,CAAC;AAEF,MAAM,qBAAqB,GAAG;IAC5B,SAAS,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC;IAClD,QAAQ,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC;IAClD,QAAQ,EAAE,EAAE;CACb,CAAC;AAIF;;;GAGG;AACH,MAAM,CAAC,MAAM,MAAM,GAAsB,CAAC,OAAO,EAAE,EAAE;IACnD,MAAM,EACJ,IAAI,EACJ,MAAM,GAAG,EAAE,EACX,KAAK,EACL,KAAK,GAAG,EAAE,EACV,MAAM,GAAG,EAAE,EACX,UAAU,GAAG,EAAE,EACf,UAAU,GAAG,EAAE,EACf,OAAO,GAAG,EAAE,EACZ,OAAO,GAAG,EAAE,EACZ,WAAW,EACX,KAAK,GAAG,EAAE,GACX,GAAG,OAAO,CAAC;IAEZ,iEAAiE;IACjE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,cAAc,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IAEtD,oCAAoC;IACpC,MAAM,UAAU,GAAG,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAC7C,MAAM,UAAU,GAAG,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAC7C,MAAM,EAAE,GAAG,EAAE,OAAO,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,GAAG,OAAO,EAAE,GAAG,UAAU,CAAC;IAEpE,2CAA2C;IAC3C,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,eAAe,+CACvD,sBAAsB,KACzB,MAAM,EAAE,KAAK,CAAC,OAAO,CAAC,KACnB,MAAM,EACT,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;IAErB,0CAA0C;IAC1C,MAAM,KAIF,SAAS,CAAC,KAAK,EAAE,OAAO,CAAC,EAJvB,EACJ,IAAI,GAAG,OAAO,EACd,OAAO,GAAG,CAAC,OAEgB,EADxB,UAAU,cAHT,mBAIL,CAA4B,CAAC;IAE9B,MAAM,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC;IAE5B,MAAM,WAAW,GAAG,UAAU,CAC5B,OAAO,EACP,MAAM,EACN;QACE,KAAK,EAAE,IAAI;QACX,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC;KAC5B,EACD,IAAI,CACL,CAAC;IACF,MAAM,WAAW,GAAG,UAAU,CAAC,OAAO,EAAE,MAAM,EAAE;QAC9C,KAAK,EAAE,EAAE;QACT,KAAK,EAAE;YACL,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC;YAClD,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC;SACnD;KACF,CAAC,CAAC;IACH,+BAA+B;IAC/B,MAAM,CAAC,SAAS,EAAE,SAAS,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,MAAM,CACzD,CAAC,GAAG,EAAE,CAAC,SAAS,EAAE,QAAQ,CAAC,EAAE,EAAE;QAC7B,MAAM,WAAW,GAAG,gBAAgB,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;QAC/D,MAAM,SAAS,GAAG,SAAS,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAC9C,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,mCAAQ,WAAW,GAAK,SAAS,CAAE,CAAC;QACrD,MAAM,SAAS,GAAG,SAAS,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAC9C,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,mCAAQ,WAAW,GAAK,SAAS,CAAE,CAAC;QACrD,OAAO,GAAG,CAAC;IACb,CAAC,EACD,CAAC,EAAE,EAAE,EAAE,CAAC,CACT,CAAC;IACF,OAAO;QACL,OAAO,CAAC,EAAE,EAAE,oBAAoB,EAAE;YAChC,IAAI,EAAE,QAAQ;YACd,MAAM,kCAAO,UAAU,KAAE,KAAK,GAAE;YAChC,KAAK;YACL,KAAK,EAAE,SAAS,CAAC,KAAK,EAAE,MAAM,CAAC;YAC/B,MAAM,EAAE;8DAED,qBAAqB,KACxB,IAAI,EACJ,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAC3C,UAAU;gBAEf,GAAG,UAAU;aACd;YACD,OAAO,EAAE,WAAW;YACpB,OAAO,EAAE,cAAc,CAAC,OAAO,EAAE,MAAM,CAAC;YACxC,IAAI,EAAE,KAAK;YACX,WAAW;YACX,KAAK,EAAE,SAAS;SACjB,CAAC;QACF,OAAO,CAAC,EAAE,EAAE,oBAAoB,EAAE;YAChC,IAAI,EAAE,QAAQ;YACd,MAAM,EAAE,UAAU;YAClB,MAAM,EAAE,UAAU;YAClB,KAAK,kBACH,IAAI,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,EAC3C,SAAS,EAAE,CAAC,IACT,SAAS,CAAC,KAAK,EAAE,MAAM,CAAC,CAC5B;YACD,OAAO,EAAE,WAAW;YACpB,OAAO,EAAE,cAAc,CAAC,OAAO,EAAE,MAAM,CAAC;YACxC,WAAW;YACX,KAAK,EAAE,SAAS;SACjB,CAAC;KACH,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,CAAC,KAAK,GAAG,EAAE,CAAC"}