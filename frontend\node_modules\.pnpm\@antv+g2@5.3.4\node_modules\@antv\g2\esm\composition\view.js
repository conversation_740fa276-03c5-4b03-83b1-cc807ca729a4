var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
import { deepMix } from '@antv/util';
import { mergeData } from './utils';
/**
 * @todo Propagate more options to children.(e.g. filter)
 * @todo Propagate encode options to children. This is useful for Matrix composition.
 * @todo Move this to runtime, do not treat it as a composition to cause confusion.
 */
export const View = () => {
    return (options) => {
        const { children } = options, restOptions = __rest(options, ["children"]);
        if (!Array.isArray(children))
            return [];
        const { data: viewData, scale: viewScale = {}, axis: viewAxis = {}, legend: viewLegend = {}, encode: viewEncode = {}, transform: viewTransform = [] } = restOptions, rest = __rest(restOptions, ["data", "scale", "axis", "legend", "encode", "transform"]);
        const marks = children.map((_a) => {
            var { data, scale = {}, axis = {}, legend = {}, encode = {}, transform = [] } = _a, rest = __rest(_a, ["data", "scale", "axis", "legend", "encode", "transform"]);
            return (Object.assign({ data: mergeData(data, viewData), scale: deepMix({}, viewScale, scale), encode: deepMix({}, viewEncode, encode), transform: [...viewTransform, ...transform], axis: axis && viewAxis ? deepMix({}, viewAxis, axis) : false, legend: legend && viewLegend ? deepMix({}, viewLegend, legend) : false }, rest));
        });
        return [Object.assign(Object.assign({}, rest), { marks, type: 'standardView' })];
    };
};
View.props = {};
//# sourceMappingURL=view.js.map