{"version": 3, "file": "overlapHide.js", "sourceRoot": "", "sources": ["../../src/label-transform/overlapHide.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AACvD,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,gBAAgB,CAAC;AAI5C;;GAEG;AACH,MAAM,CAAC,MAAM,WAAW,GAA4B,CAAC,OAAO,EAAE,EAAE;IAC9D,MAAM,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC;IAC7B,OAAO,CAAC,MAAuB,EAAE,EAAE;QACjC,MAAM,aAAa,GAAG,EAAE,CAAC;QACzB,0CAA0C;QAC1C,IAAI,QAAQ;YAAE,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEpC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;YACnB,IAAI,CAAC,CAAC,CAAC,CAAC;YAER,MAAM,EAAE,GAAG,CAAC,CAAC,cAAc,EAAE,CAAC;YAC9B,MAAM,UAAU,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,CAC3C,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,EAAE,CAAC,cAAc,EAAE,CAAC,CAAC,CACzD,CAAC;YAEF,IAAI,UAAU;gBAAE,IAAI,CAAC,CAAC,CAAC,CAAC;;gBACnB,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC;IAChB,CAAC,CAAC;AACJ,CAAC,CAAC"}