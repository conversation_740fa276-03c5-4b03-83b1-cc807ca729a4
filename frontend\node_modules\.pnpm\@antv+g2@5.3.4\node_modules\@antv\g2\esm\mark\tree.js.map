{"version": 3, "file": "tree.js", "sourceRoot": "", "sources": ["../../src/mark/tree.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AACrC,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAG5C,OAAO,EACL,IAAI,IAAI,aAAa,GAEtB,MAAM,cAAc,CAAC;AACtB,OAAO,EAAE,cAAc,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAE3D,MAAM,sBAAsB,GAAyB;IACnD,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK;CACpC,CAAC;AAEF,MAAM,oBAAoB,GAAG;IAC3B,IAAI,EAAE,KAAK;IACX,MAAM,EAAE,KAAK;IACb,IAAI,EAAE,OAAO;IACb,MAAM,EAAE;QACN,CAAC,EAAE,GAAG;QACN,CAAC,EAAE,GAAG;QACN,IAAI,EAAE,CAAC;QACP,KAAK,EAAE,OAAO;KACf;CACF,CAAC;AAEF,MAAM,oBAAoB,GAAG;IAC3B,IAAI,EAAE,MAAM;IACZ,MAAM,EAAE;QACN,CAAC,EAAE,GAAG;QACN,CAAC,EAAE,GAAG;QACN,KAAK,EAAE,QAAQ;KAChB;CACF,CAAC;AAEF,MAAM,qBAAqB,GAAG;IAC5B,IAAI,EAAE,EAAE;IACR,QAAQ,EAAE,EAAE;CACb,CAAC;AAIF,MAAM,CAAC,MAAM,IAAI,GAAoB,CAAC,OAAO,EAAE,EAAE;IAC/C,MAAM,EACJ,IAAI,EACJ,MAAM,GAAG,EAAE,EACX,KAAK,GAAG,EAAE,EACV,KAAK,GAAG,EAAE,EACV,MAAM,GAAG,EAAE,EACX,UAAU,GAAG,EAAE,EACf,UAAU,GAAG,EAAE,EACf,OAAO,GAAG,EAAE,EACZ,OAAO,GAAG,EAAE,GACb,GAAG,OAAO,CAAC;IACZ,MAAM,WAAW,GAAG,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,KAAK,CAAC;IAClC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,aAAa,+CACjC,sBAAsB,GACtB,MAAM,KACT,KAAK,EAAE,WAAW,IAClB,CAAC,IAAI,CAAC,CAAC;IAET,MAAM,WAAW,GAAG,UAAU,CAC5B,OAAO,EACP,MAAM,EACN;QACE,KAAK,EAAE,MAAM;QACb,KAAK,EAAE,CAAC,OAAO,CAAC;KACjB,EACD,IAAI,CACL,CAAC;IAEF,MAAM,WAAW,GAAG,UAAU,CAAC,OAAO,EAAE,MAAM,EAAE;QAC9C,KAAK,EAAE,EAAE;QACT,KAAK,EAAE;YACL,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;YACjD,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;SAClD;KACF,CAAC,CAAC;IAEH,OAAO;QACL,OAAO,CAAC,EAAE,EAAE,oBAAoB,EAAE;YAChC,IAAI,EAAE,KAAK;YACX,MAAM,EAAE,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC;YACjC,KAAK,EAAE,SAAS,CAAC,KAAK,EAAE,MAAM,CAAC;YAC/B,MAAM,EAAE,UAAU;YAClB,KAAK,kBAAI,MAAM,EAAE,MAAM,IAAK,SAAS,CAAC,KAAK,EAAE,MAAM,CAAC,CAAE;YACtD,OAAO,EAAE,WAAW;YACpB,OAAO,EAAE,cAAc,CAAC,OAAO,EAAE,MAAM,CAAC;SACzC,CAAC;QACF,OAAO,CAAC,EAAE,EAAE,oBAAoB,EAAE;YAChC,IAAI,EAAE,KAAK;YACX,KAAK,EAAE,SAAS,CAAC,KAAK,EAAE,MAAM,CAAC;YAC/B,MAAM,EAAE,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC;YACjC,MAAM,EAAE;gDAED,qBAAqB,GACrB,SAAS,CAAC,KAAK,EAAE,OAAO,CAAC;gBAE9B,GAAG,UAAU;aACd;YACD,KAAK,oBAAO,SAAS,CAAC,KAAK,EAAE,MAAM,CAAC,CAAE;YACtC,OAAO,EAAE,WAAW;YACpB,OAAO,EAAE,cAAc,CAAC,OAAO,EAAE,MAAM,CAAC;SACzC,CAAC;KACH,CAAC;AACJ,CAAC,CAAC;AAEF,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC"}