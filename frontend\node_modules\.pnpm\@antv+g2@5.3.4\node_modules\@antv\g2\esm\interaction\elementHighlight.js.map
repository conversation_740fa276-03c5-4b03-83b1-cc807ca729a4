{"version": 3, "file": "elementHighlight.js", "sourceRoot": "", "sources": ["../../src/interaction/elementHighlight.ts"], "names": [], "mappings": ";;;;;;;;;;;AACA,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AACrC,OAAO,EAAE,KAAK,EAAE,MAAM,uBAAuB,CAAC;AAC9C,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAC5C,OAAO,EACL,aAAa,EACb,wBAAwB,EACxB,cAAc,EACd,aAAa,EACb,UAAU,EACV,UAAU,EACV,eAAe,EACf,gBAAgB,EAChB,UAAU,EACV,mBAAmB,EACnB,gBAAgB,EAChB,cAAc,EACd,qBAAqB,GACtB,MAAM,SAAS,CAAC;AAEjB;;GAEG;AACH,MAAM,UAAU,gBAAgB,CAC9B,IAAmB,EACnB,EACE,QAAQ,EAAE,UAAU,EAAE,6DAA6D;AACnF,KAAK,EAAE,6CAA6C;AACpD,QAAQ,EAAE,WAAW,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,kCAAkC;AACpE,cAAc,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,0CAA0C;AACrE,IAAI,GAAG,KAAK,EAAE,mBAAmB;AACjC,UAAU,GAAG,KAAK,EAAE,yBAAyB;AAC7C,KAAK,GAAG,EAAE,EAAE,iCAAiC;AAC7C,KAAK,EACL,UAAU,EACV,OAAO,EACP,KAAK,GAAG,EAAE,EACV,MAAM,GAAG,KAAK,EACd,eAAe,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,EAAE,sEAAsE;EACzH;;IAEtB,MAAM,WAAW,GAAG,MAAA,UAAU,CAAC,IAAI,CAAC,mCAAI,EAAE,CAAC;IAC3C,MAAM,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC;IAC5E,MAAM,UAAU,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,CAAC;IACrC,MAAM,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,WAAW,CAAC;IACvD,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAC3C,MAAM,WAAW,GAAG,wBAAwB,CAAC;QAC3C,UAAU;QACV,IAAI;QACJ,UAAU;QACV,KAAK;KACN,CAAC,CAAC;IAEH,MAAM,OAAO,GAAG,aAAa,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;IAC/C,MAAM,CAAC,UAAU,EAAE,UAAU,CAAC,GAAG,UAAU,iBACzC,QAAQ;QACR,OAAO;QACP,IAAI;QACJ,UAAU,IACP,SAAS,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,CAAC,EAClC,CAAC;IACH,MAAM,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,YAAY,CAAC,GAAG,gBAAgB,iBACzE,QAAQ,EAAE,IAAI,CAAC,aAAa,EAC5B,KAAK;QACL,UAAU;QACV,UAAU;QACV,OAAO,IACJ,SAAS,CAAC,KAAK,CAAC,MAAM,EAAE,YAAY,CAAC,EACxC,CAAC;IAEH,MAAM,YAAY,GAAG,OAAO,CAAC,KAAK,EAAE;QAClC,MAAM,oBACD,CAAC,CAAA,MAAA,KAAK,CAAC,MAAM,0CAAE,MAAM,KAAI;YAC1B,oCAAoC;YACpC,SAAS,EAAE,CAAC,GAAG,MAAM,EAAE,EAAE;gBACvB,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;gBAC7C,MAAM,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC;gBACrB,OAAO,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;YACzD,CAAC;SACF,CAAC,CACH;KACF,CAAC,CAAC;IAEH,MAAM,QAAQ,GAAG,cAAc,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;IAExD,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,QAAQ,EAAE,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC;IAEjE,IAAI,GAAG,CAAC,CAAC,oCAAoC;IAC7C,MAAM,WAAW,GAAG,CAAC,KAAK,EAAE,EAAE;QAC5B,MAAM,EAAE,WAAW,GAAG,IAAI,EAAE,GAAG,KAAK,CAAC;QACrC,IAAI,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC;QAC3B,IAAI,MAAM,EAAE;YACV,OAAO,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;SAC9B;QACD,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC;YAAE,OAAO;QACrC,IAAI,GAAG;YAAE,YAAY,CAAC,GAAG,CAAC,CAAC;QAC3B,MAAM,CAAC,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC;QAC5B,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC9B,MAAM,QAAQ,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC;QAChC,KAAK,MAAM,CAAC,IAAI,QAAQ,EAAE;YACxB,IAAI,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;gBACnB,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC;oBAAE,WAAW,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;aACtD;iBAAM;gBACL,WAAW,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;gBAC3B,UAAU,CAAC,CAAC,CAAC,CAAC;aACf;YACD,IAAI,CAAC,KAAK,OAAO;gBAAE,gBAAgB,CAAC,CAAC,CAAC,CAAC;SACxC;QACD,gBAAgB,CAAC,OAAO,CAAC,CAAC;QAC1B,UAAU,CAAC,KAAK,CAAC,CAAC;QAElB,eAAe;QACf,IAAI,CAAC,WAAW;YAAE,OAAO;QACzB,OAAO,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAChC,WAAW;YACX,IAAI,EAAE;gBACJ,IAAI,EAAE,KAAK,CAAC,OAAO,CAAC;gBACpB,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC;aACxB;SACF,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,MAAM,kBAAkB,GAAG,GAAG,EAAE;QAC9B,IAAI,GAAG;YAAE,YAAY,CAAC,GAAG,CAAC,CAAC;QAC3B,GAAG,GAAG,UAAU,CAAC,GAAG,EAAE;YACpB,aAAa,EAAE,CAAC;YAChB,GAAG,GAAG,IAAI,CAAC;QACb,CAAC,EAAE,KAAK,CAAC,CAAC;IACZ,CAAC,CAAC;IAEF,MAAM,aAAa,GAAG,CAAC,WAAW,GAAG,IAAI,EAAE,EAAE;QAC3C,KAAK,MAAM,CAAC,IAAI,QAAQ,EAAE;YACxB,WAAW,CAAC,CAAC,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;YACrC,gBAAgB,CAAC,CAAC,CAAC,CAAC;YACpB,UAAU,CAAC,CAAC,CAAC,CAAC;SACf;QACD,IAAI,WAAW,EAAE;YACf,OAAO,CAAC,IAAI,CAAC,qBAAqB,EAAE,EAAE,WAAW,EAAE,CAAC,CAAC;SACtD;IACH,CAAC,CAAC;IAEF,MAAM,UAAU,GAAG,CAAC,KAAK,EAAE,EAAE;QAC3B,IAAI,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC;QAC3B,IAAI,MAAM,EAAE;YACV,OAAO,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;SAC9B;QACD,IAAI,CAAC,OAAO,EAAE;YACZ,IAAI,KAAK,GAAG,CAAC;gBAAE,kBAAkB,EAAE,CAAC;;gBAC/B,aAAa,EAAE,CAAC;YACrB,OAAO;SACR;QACD,IAAI,UAAU,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;YAAE,OAAO;QACjD,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC;YAAE,OAAO;QACpD,IAAI,KAAK,GAAG,CAAC;YAAE,kBAAkB,EAAE,CAAC;;YAC/B,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC;IAEF,MAAM,YAAY,GAAG,GAAG,EAAE;QACxB,aAAa,EAAE,CAAC;IAClB,CAAC,CAAC;IAEF,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;IAClD,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;IAClD,IAAI,CAAC,gBAAgB,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;IAChD,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;IAEpD,MAAM,MAAM,GAAG,CAAC,CAAC,EAAE,EAAE;QACnB,MAAM,EAAE,WAAW,EAAE,GAAG,CAAC,CAAC;QAC1B,IAAI,WAAW;YAAE,OAAO;QACxB,aAAa,CAAC,KAAK,CAAC,CAAC;IACvB,CAAC,CAAC;IAEF,MAAM,WAAW,GAAG,CAAC,CAAC,EAAE,EAAE;QACxB,MAAM,EAAE,WAAW,EAAE,GAAG,CAAC,CAAC;QAC1B,IAAI,WAAW;YAAE,OAAO;QACxB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC;QACxB,MAAM,OAAO,GAAG,mBAAmB,CAAC,QAAQ,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QAC3D,IAAI,CAAC,OAAO;YAAE,OAAO;QACrB,WAAW,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,CAAC,CAAC;IACvD,CAAC,CAAC;IAEF,OAAO,CAAC,EAAE,CAAC,mBAAmB,EAAE,WAAW,CAAC,CAAC;IAC7C,OAAO,CAAC,EAAE,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAC;IAE1C,OAAO,GAAG,EAAE;QACV,IAAI,CAAC,mBAAmB,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;QACrD,IAAI,CAAC,mBAAmB,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;QACrD,IAAI,CAAC,mBAAmB,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;QACnD,IAAI,CAAC,mBAAmB,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;QACvD,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,WAAW,CAAC,CAAC;QAC9C,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAC;QAC3C,KAAK,MAAM,CAAC,IAAI,QAAQ,EAAE;YACxB,gBAAgB,CAAC,CAAC,CAAC,CAAC;YACpB,UAAU,CAAC,CAAC,CAAC,CAAC;SACf;IACH,CAAC,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,gBAAgB,CAAC,EAOhC;QAPgC,EAC/B,KAAK,EACL,WAAW,EACX,iBAAiB,EACjB,UAAU,GAAG,KAAK,EAClB,IAAI,GAAG,KAAK,OAEb,EADI,IAAI,cANwB,mEAOhC,CADQ;IAEP,OAAO,CAAC,OAAO,EAAE,CAAC,EAAE,OAAO,EAAE,EAAE;QAC7B,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC;QAC7C,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC;QACnC,MAAM,QAAQ,GAAG,cAAc,CAAC,SAAS,CAAC,CAAC;QAE3C,OAAO,gBAAgB,CAAC,QAAQ,kBAC9B,QAAQ,EAAE,gBAAgB,EAC1B,KAAK,EAAE,aAAa,CAAC,IAAI,CAAC,EAC1B,QAAQ,EAAE,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,EACrD,cAAc,EAAE,iBAAiB;gBAC/B,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC;gBACzB,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,EACpB,UAAU;YACV,KAAK,EACL,KAAK,EAAE,UAAU,CAAC,OAAO,EAAE;gBACzB,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,SAAS,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;gBAChE,UAAU;aACX,CAAC,EACF,UAAU;YACV,IAAI;YACJ,KAAK;YACL,OAAO,IACJ,IAAI,EACP,CAAC;IACL,CAAC,CAAC;AACJ,CAAC;AAED,gBAAgB,CAAC,KAAK,GAAG;IACvB,iBAAiB,EAAE,IAAI;CACxB,CAAC"}