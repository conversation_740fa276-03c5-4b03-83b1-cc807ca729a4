{"version": 3, "file": "line.js", "sourceRoot": "", "sources": ["../../src/mark/line.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,KAAK,EAAE,MAAM,uBAAuB,CAAC;AAC9C,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,qBAAqB,CAAC;AAG1D,OAAO,EACL,SAAS,EACT,MAAM,EACN,MAAM,EACN,OAAO,EACP,SAAS,EACT,UAAU,GACX,MAAM,UAAU,CAAC;AAClB,OAAO,EAAE,WAAW,EAAE,aAAa,EAAE,MAAM,cAAc,CAAC;AAC1D,OAAO,EACL,oBAAoB,EACpB,iBAAiB,EACjB,gBAAgB,EAChB,SAAS,EACT,SAAS,GACV,MAAM,SAAS,CAAC;AAEjB,MAAM,KAAK,GAAG;IACZ,IAAI,EAAE,SAAS;IACf,MAAM,EAAE,UAAU;IAClB,EAAE,EAAE,MAAM;IACV,EAAE,EAAE,MAAM;IACV,GAAG,EAAE,OAAO;IACZ,KAAK,EAAE,SAAS;CACjB,CAAC;AAIF,MAAM,IAAI,GAAS,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,EAAE;;IACrD,MAAM,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC;IACxC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC;IAEvB,kEAAkE;IAClE,oDAAoD;IACpD,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,SAAS,EAAE;QACtC,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;KACvD;IAED,0BAA0B;IAC1B,4DAA4D;IAC5D,MAAM,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;IAC5E,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC;IAEzE,2CAA2C;IAC3C,MAAM,OAAO,GAAG,CAAC,CAAA,MAAA,CAAC,aAAD,CAAC,uBAAD,CAAC,CAAE,YAAY,iDAAI,KAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IAC/C,MAAM,OAAO,GAAG,CAAC,CAAA,MAAA,CAAC,aAAD,CAAC,uBAAD,CAAC,CAAE,YAAY,iDAAI,KAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IAC/C,MAAM,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE;QACjC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CACjB,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CACtC,CAAC;IACjB,CAAC,CAAC,CAAC;IACH,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;AACxB,CAAC,CAAC;AAEF,MAAM,QAAQ,GAAS,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,EAAE;IACzD,mDAAmD;IACnD,MAAM,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;SAC7B,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;SAC7C,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC;IAE7B,mEAAmE;IACnE,oDAAoD;IACpD,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE;QACnB,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;KACzD;IACD,2DAA2D;IAC3D,IAAI,OAAO,CAAC,UAAU,CAAC;QAAE,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAExC,oCAAoC;IACpC,MAAM,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,EAAE;QAChC,sEAAsE;QACtE,kCAAkC;QAClC,MAAM,MAAM,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACtC,MAAM,OAAO,GAAG,UAAU,CAAC,GAAG,CAAC,MAAM,CAAW,CAAC;QAEjD,qEAAqE;QACrE,+CAA+C;QAC/C,MAAM,MAAM,GAAG,EAAE,CAAC;QAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;YAC1C,MAAM,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;SAC3C;QACD,OAAO,MAAM,CAAC;IAChB,CAAC,CAAC,CAAC;IACH,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AACpB,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,IAAI,GAAoB,GAAG,EAAE;IACxC,OAAO,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,EAAE;QACzC,MAAM,IAAI,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC;QACtD,OAAQ,IAAmB,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;IAC/D,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF,IAAI,CAAC,KAAK,GAAG;IACX,YAAY,EAAE,MAAM;IACpB,iBAAiB,EAAE,OAAO;IAC1B,SAAS,EAAE,KAAK;IAChB,KAAK;IACL,QAAQ,EAAE;QACR,GAAG,oBAAoB,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;QACvD,EAAE,IAAI,EAAE,GAAG,EAAE;QACb,EAAE,IAAI,EAAE,GAAG,EAAE;QACb,EAAE,IAAI,EAAE,UAAU,EAAE,WAAW,EAAE,IAAI,EAAE;QACvC,EAAE,IAAI,EAAE,MAAM,EAAE;QAChB,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE;KAClC;IACD,YAAY,EAAE;QACZ,GAAG,gBAAgB,EAAE;QACrB,wCAAwC;QACxC,EAAE,IAAI,EAAE,aAAa,EAAE;QACvB,EAAE,IAAI,EAAE,WAAW,EAAE;KACtB;IACD,aAAa,EAAE,CAAC,GAAG,iBAAiB,EAAE,EAAE,GAAG,SAAS,EAAE,EAAE,GAAG,SAAS,EAAE,CAAC;IACvE,WAAW,EAAE;QACX,YAAY,EAAE,IAAI;QAClB,aAAa,EAAE,IAAI;QACnB,UAAU,EAAE,IAAI;KACjB;CACF,CAAC"}