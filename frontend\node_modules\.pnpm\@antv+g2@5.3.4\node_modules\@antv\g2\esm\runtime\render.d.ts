import { DisplayObject } from '@antv/g';
import { G2Context, G2ViewTree } from './types/options';
export declare function render<T extends G2ViewTree = G2ViewTree>(options: T, context?: G2Context, resolve?: () => void, reject?: (e?: any) => void): HTMLElement;
export declare function renderToMountedElement<T extends G2ViewTree = G2ViewTree>(options: T, context?: G2Context, resolve?: () => void, reject?: (e?: any) => never): DisplayObject;
export declare function destroy<T extends G2ViewTree = G2ViewTree>(options: T, context?: G2Context, isDestroyCanvas?: boolean, isClearEvents?: boolean): void;
