import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Event, ScrollbarFilter } from '../interaction';
/**
 * In test stage, don't use it.
 */
export declare function litelib(): {
    readonly 'data.inline': import("../runtime").DataComponent<import("../data").InlineOptions>;
    readonly 'coordinate.cartesian': import("../runtime").CoordinateComponent<import("../coordinate").CartesianOptions>;
    readonly 'encode.constant': import("../runtime").EncodeComponent<import("../encode").ConstantOptions>;
    readonly 'encode.field': import("../runtime").EncodeComponent<import("../encode").FieldOptions>;
    readonly 'encode.transform': import("../runtime").EncodeComponent<import("../encode").TransformOptions>;
    readonly 'encode.column': import("../runtime").EncodeComponent<import("../encode").ColumnOptions>;
    readonly 'mark.interval': import("..").MarkComponent<import("../mark").IntervalOptions>;
    readonly 'shape.label.label': import("..").ShapeComponent<import("../shape").LabelShapeOptions>;
    readonly 'palette.category10': import("../runtime").PaletteComponent<import("../palette").Category10Options>;
    readonly 'palette.category20': import("../runtime").PaletteComponent<import("../palette").Category20Options>;
    readonly 'scale.linear': import("../runtime").ScaleComponent<import("../scale").LinearOptions>;
    readonly 'scale.ordinal': import("../runtime").ScaleComponent<import("../scale").OrdinalOptions>;
    readonly 'scale.band': import("../runtime").ScaleComponent<import("../scale").BandOptions>;
    readonly 'scale.identity': import("../runtime").ScaleComponent<import("../scale").IdentityOptions>;
    readonly 'scale.point': import("../runtime").ScaleComponent<import("../scale").PointOptions>;
    readonly 'scale.time': import("../runtime").ScaleComponent<import("../scale").TimeOptions>;
    readonly 'scale.log': import("../runtime").ScaleComponent<import("../scale").LogOptions>;
    readonly 'scale.pow': import("../runtime").ScaleComponent<import("../scale").PowOptions>;
    readonly 'scale.sqrt': import("../runtime").ScaleComponent<import("../scale").SqrtOptions>;
    readonly 'scale.threshold': import("../runtime").ScaleComponent<import("../scale").ThresholdOptions>;
    readonly 'scale.quantile': import("../runtime").ScaleComponent<import("../scale").QuantileOptions>;
    readonly 'scale.quantize': import("../runtime").ScaleComponent<import("../scale").QuantizeOptions>;
    readonly 'scale.sequential': import("../runtime").ScaleComponent<import("../scale").SequentialOptions>;
    readonly 'scale.constant': import("../runtime").ScaleComponent<import("../scale").ConstantOptions>;
    readonly 'theme.classic': import("../runtime").ThemeComponent<import("../runtime").G2Theme>;
    readonly 'component.axisX': import("..").GuideComponentComponent<import("..").AxisOptions>;
    readonly 'component.axisY': import("..").GuideComponentComponent<import("..").AxisOptions>;
    readonly 'component.axisRadar': import("..").GuideComponentComponent<import("../component/axisRadar").AxisRadarOptions>;
    readonly 'component.axisLinear': import("..").GuideComponentComponent<import("..").AxisOptions>;
    readonly 'component.axisArc': import("..").GuideComponentComponent<import("..").AxisOptions>;
    readonly 'component.legendCategory': import("..").GuideComponentComponent<import("../component").LegendCategoryOptions>;
    readonly 'component.legendContinuous': import("..").GuideComponentComponent<import("../component").LegendContinuousOptions>;
    readonly 'component.legendContinuousBlock': import("..").GuideComponentComponent<import("../component").LegendContinuousOptions>;
    readonly 'component.legendContinuousBlockSize': import("..").GuideComponentComponent<import("../component").LegendContinuousOptions>;
    readonly 'component.legendContinuousSize': import("..").GuideComponentComponent<import("../component").LegendContinuousOptions>;
    readonly 'component.legends': import("..").GuideComponentComponent<import("../component").LegendsOptions>;
    readonly 'component.title': import("..").GuideComponentComponent<import("../runtime").G2Title>;
    readonly 'component.sliderX': import("..").GuideComponentComponent<import("../component/slider").SliderOptions>;
    readonly 'component.sliderY': import("..").GuideComponentComponent<import("../component/slider").SliderOptions>;
    readonly 'component.scrollbarX': import("..").GuideComponentComponent<import("../component/scrollbar").ScrollbarOptions>;
    readonly 'component.scrollbarY': import("..").GuideComponentComponent<import("../component/scrollbar").ScrollbarOptions>;
    readonly 'animation.scaleInX': import("../runtime").AnimationComponent<import("../animation/types").Animation>;
    readonly 'animation.scaleOutX': import("../runtime").AnimationComponent<import("../animation/types").Animation>;
    readonly 'animation.scaleInY': import("../runtime").AnimationComponent<import("../animation/types").Animation>;
    readonly 'animation.scaleOutY': import("../runtime").AnimationComponent<import("../animation/types").Animation>;
    readonly 'animation.waveIn': import("../runtime").AnimationComponent<import("../animation/types").Animation>;
    readonly 'animation.fadeIn': import("../runtime").AnimationComponent<import("../animation/types").Animation>;
    readonly 'animation.fadeOut': import("../runtime").AnimationComponent<import("../animation/types").Animation>;
    readonly 'animation.morphing': import("../runtime").AnimationComponent<import("../animation").MorphingOptions>;
    readonly 'interaction.tooltip': typeof Tooltip;
    readonly 'interaction.legendFilter': typeof LegendFilter;
    readonly 'interaction.legendHighlight': typeof LegendHighlight;
    readonly 'interaction.sliderFilter': typeof SliderFilter;
    readonly 'interaction.scrollbarFilter': typeof ScrollbarFilter;
    readonly 'interaction.poptip': typeof Poptip;
    readonly 'interaction.event': typeof Event;
    readonly 'composition.mark': import("../runtime").CompositionComponent<import("../composition").MarkOptions>;
    readonly 'composition.view': import("../runtime").CompositionComponent<import("../composition").ViewOptions>;
};
