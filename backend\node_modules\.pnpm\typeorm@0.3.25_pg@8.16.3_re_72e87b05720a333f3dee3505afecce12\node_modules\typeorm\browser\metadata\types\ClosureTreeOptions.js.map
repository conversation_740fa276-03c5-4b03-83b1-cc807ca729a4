{"version": 3, "sources": ["../browser/src/metadata/types/ClosureTreeOptions.ts"], "names": [], "mappings": "", "file": "ClosureTreeOptions.js", "sourcesContent": ["/**\n * Tree type.\n * Specifies what table pattern will be used for the tree entity.\n */\nimport { ColumnMetadata } from \"../ColumnMetadata\"\n\nexport interface ClosureTreeOptions {\n    closureTableName?: string\n    ancestorColumnName?: (column: ColumnMetadata) => string\n    descendantColumnName?: (column: ColumnMetadata) => string\n}\n"], "sourceRoot": "../.."}