import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

export interface TaxonomicClassification {
  kingdom?: string;
  phylum?: string;
  class?: string;
  order?: string;
  family?: string;
  genus?: string;
  species?: string;
}

export interface Species {
  id: string;
  chineseName: string;
  englishName?: string;
  latinName: string;
  conservationStatus?: string;
  description?: string;
  representativeImagePath?: string;
  classification?: TaxonomicClassification;
  createdAt: string;
  updatedAt: string;
}

export interface SpeciesSearchParams {
  keyword?: string;
  conservationStatus?: string;
  kingdom?: string;
  phylum?: string;
  class?: string;
  order?: string;
  family?: string;
  genus?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
}

export interface SpeciesListResult {
  species: Species[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface SpeciesState {
  // 物种列表
  speciesList: Species[];
  searchParams: SpeciesSearchParams;
  pagination: {
    current: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
  
  // 当前物种详情
  currentSpecies: Species | null;
  speciesDetails: any | null;
  
  // 统计信息
  taxonomyStats: any | null;
  conservationStats: any[] | null;
  
  // 加载状态
  loading: {
    list: boolean;
    detail: boolean;
    stats: boolean;
  };
  
  error: string | null;
}

const initialState: SpeciesState = {
  speciesList: [],
  searchParams: {
    page: 1,
    limit: 20,
    sortBy: 'chineseName',
    sortOrder: 'ASC',
  },
  pagination: {
    current: 1,
    pageSize: 20,
    total: 0,
    totalPages: 0,
  },
  currentSpecies: null,
  speciesDetails: null,
  taxonomyStats: null,
  conservationStats: null,
  loading: {
    list: false,
    detail: false,
    stats: false,
  },
  error: null,
};

// 搜索物种
export const searchSpecies = createAsyncThunk(
  'species/searchSpecies',
  async (params: SpeciesSearchParams, { rejectWithValue }) => {
    try {
      const queryParams = new URLSearchParams();
      
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          queryParams.append(key, value.toString());
        }
      });

      const response = await fetch(
        `${import.meta.env.VITE_API_BASE_URL}/species/search?${queryParams}`
      );

      if (!response.ok) {
        const error = await response.json();
        return rejectWithValue(error.error?.message || '搜索失败');
      }

      const data = await response.json();
      return data.data;
    } catch (error) {
      return rejectWithValue('网络错误，请稍后重试');
    }
  }
);

// 获取物种详情
export const fetchSpeciesDetails = createAsyncThunk(
  'species/fetchSpeciesDetails',
  async (speciesId: string, { rejectWithValue }) => {
    try {
      const response = await fetch(
        `${import.meta.env.VITE_API_BASE_URL}/species/${speciesId}/details`
      );

      if (!response.ok) {
        const error = await response.json();
        return rejectWithValue(error.error?.message || '获取物种详情失败');
      }

      const data = await response.json();
      return data.data;
    } catch (error) {
      return rejectWithValue('网络错误，请稍后重试');
    }
  }
);

// 获取分类学统计
export const fetchTaxonomyStats = createAsyncThunk(
  'species/fetchTaxonomyStats',
  async (_, { rejectWithValue }) => {
    try {
      const response = await fetch(
        `${import.meta.env.VITE_API_BASE_URL}/species/stats/taxonomy`
      );

      if (!response.ok) {
        const error = await response.json();
        return rejectWithValue(error.error?.message || '获取统计信息失败');
      }

      const data = await response.json();
      return data.data;
    } catch (error) {
      return rejectWithValue('网络错误，请稍后重试');
    }
  }
);

// 获取保护状态统计
export const fetchConservationStats = createAsyncThunk(
  'species/fetchConservationStats',
  async (_, { rejectWithValue }) => {
    try {
      const response = await fetch(
        `${import.meta.env.VITE_API_BASE_URL}/species/stats/conservation`
      );

      if (!response.ok) {
        const error = await response.json();
        return rejectWithValue(error.error?.message || '获取统计信息失败');
      }

      const data = await response.json();
      return data.data;
    } catch (error) {
      return rejectWithValue('网络错误，请稍后重试');
    }
  }
);

// 获取最近物种
export const fetchRecentSpecies = createAsyncThunk(
  'species/fetchRecentSpecies',
  async (limit: number = 10, { rejectWithValue }) => {
    try {
      const response = await fetch(
        `${import.meta.env.VITE_API_BASE_URL}/species/recent?limit=${limit}`
      );

      if (!response.ok) {
        const error = await response.json();
        return rejectWithValue(error.error?.message || '获取最近物种失败');
      }

      const data = await response.json();
      return data.data;
    } catch (error) {
      return rejectWithValue('网络错误，请稍后重试');
    }
  }
);

// 获取随机物种
export const fetchRandomSpecies = createAsyncThunk(
  'species/fetchRandomSpecies',
  async (limit: number = 6, { rejectWithValue }) => {
    try {
      const response = await fetch(
        `${import.meta.env.VITE_API_BASE_URL}/species/random?limit=${limit}`
      );

      if (!response.ok) {
        const error = await response.json();
        return rejectWithValue(error.error?.message || '获取随机物种失败');
      }

      const data = await response.json();
      return data.data;
    } catch (error) {
      return rejectWithValue('网络错误，请稍后重试');
    }
  }
);

const speciesSlice = createSlice({
  name: 'species',
  initialState,
  reducers: {
    setSearchParams: (state, action: PayloadAction<Partial<SpeciesSearchParams>>) => {
      state.searchParams = { ...state.searchParams, ...action.payload };
    },
    clearCurrentSpecies: (state) => {
      state.currentSpecies = null;
      state.speciesDetails = null;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    // 搜索物种
    builder
      .addCase(searchSpecies.pending, (state) => {
        state.loading.list = true;
        state.error = null;
      })
      .addCase(searchSpecies.fulfilled, (state, action) => {
        state.loading.list = false;
        state.speciesList = action.payload.species;
        state.pagination = {
          current: action.payload.page,
          pageSize: action.payload.limit,
          total: action.payload.total,
          totalPages: action.payload.totalPages,
        };
      })
      .addCase(searchSpecies.rejected, (state, action) => {
        state.loading.list = false;
        state.error = action.payload as string;
      });

    // 获取物种详情
    builder
      .addCase(fetchSpeciesDetails.pending, (state) => {
        state.loading.detail = true;
        state.error = null;
      })
      .addCase(fetchSpeciesDetails.fulfilled, (state, action) => {
        state.loading.detail = false;
        state.speciesDetails = action.payload;
        state.currentSpecies = action.payload;
      })
      .addCase(fetchSpeciesDetails.rejected, (state, action) => {
        state.loading.detail = false;
        state.error = action.payload as string;
      });

    // 获取统计信息
    builder
      .addCase(fetchTaxonomyStats.pending, (state) => {
        state.loading.stats = true;
      })
      .addCase(fetchTaxonomyStats.fulfilled, (state, action) => {
        state.loading.stats = false;
        state.taxonomyStats = action.payload;
      })
      .addCase(fetchTaxonomyStats.rejected, (state, action) => {
        state.loading.stats = false;
        state.error = action.payload as string;
      });

    builder
      .addCase(fetchConservationStats.fulfilled, (state, action) => {
        state.conservationStats = action.payload;
      });
  },
});

export const { setSearchParams, clearCurrentSpecies, clearError } = speciesSlice.actions;
export default speciesSlice.reducer;
