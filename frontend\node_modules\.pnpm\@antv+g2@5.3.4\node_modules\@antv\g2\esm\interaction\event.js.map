{"version": 3, "file": "event.js", "sourceRoot": "", "sources": ["../../src/interaction/event.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,gBAAgB,CAAC;AAC5C,OAAO,EAAE,MAAM,EAAE,MAAM,iBAAiB,CAAC;AACzC,OAAO,EAAE,SAAS,EAAE,MAAM,SAAS,CAAC;AAEpC,yBAAyB;AACzB,SAAS,kBAAkB,CAAC,IAAI;IAC9B,OAAO,SAAS,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,SAAS,KAAK,WAAW,CAAC,CAAC;AACnE,CAAC;AAED,sBAAsB;AACtB,SAAS,gBAAgB,CAAC,IAAI;IAC5B,OAAO,SAAS,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,SAAS,KAAK,SAAS,CAAC,CAAC;AACjE,CAAC;AAED,sBAAsB;AACtB,SAAS,cAAc,CAAC,IAAI;IAC1B,OAAO,SAAS,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,SAAS,KAAK,OAAO,CAAC,CAAC;AAC/D,CAAC;AAED,SAAS,YAAY,CAAC,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI;IACzE,OAAO,CAAC,CAAC,EAAE,EAAE;QACX,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;YAAE,OAAO;QAE1B,oBAAoB;QACpB,OAAO,CAAC,IAAI,CAAC,QAAQ,SAAS,EAAE,EAAE,CAAC,CAAC,CAAC;QAErC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;QAErB,kEAAkE;QAClE,IAAI,CAAC,MAAM;YAAE,OAAO;QAEpB,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,CAAC;QAE7B,yDAAyD;QACzD,IAAI,SAAS,KAAK,MAAM;YAAE,OAAO;QAEjC,4CAA4C;QAC5C,MAAM,WAAW,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC;QAC7C,gDAAgD;QAChD,MAAM,aAAa,GAAG,kBAAkB,CAAC,MAAM,CAAC,CAAC;QACjD,yCAAyC;QACzC,MAAM,SAAS,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC;QAEzC,MAAM,IAAI,GAAG,WAAW,IAAI,aAAa,IAAI,SAAS,CAAC;QAEvD,IAAI,CAAC,IAAI;YAAE,OAAO;QAClB,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC;QAClD,MAAM,EAAE,mCACH,CAAC,KACJ,WAAW,EAAE,IAAI,GAClB,CAAC;QACF,IAAI,WAAW,KAAK,SAAS,EAAE;YAC7B,EAAE,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC;YAC1C,OAAO,CAAC,IAAI,CAAC,WAAW,SAAS,EAAE,EAAE,EAAE,CAAC,CAAC;YACzC,OAAO,CAAC,IAAI,CAAC,GAAG,QAAQ,IAAI,SAAS,EAAE,EAAE,EAAE,CAAC,CAAC;SAC9C;aAAM,IAAI,WAAW,KAAK,OAAO,EAAE;YAClC,uCAAuC;YACvC,EAAE,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;YAC7C,OAAO,CAAC,IAAI,CAAC,SAAS,SAAS,EAAE,EAAE,EAAE,CAAC,CAAC;YACvC,OAAO,CAAC,IAAI,CAAC,GAAG,SAAS,IAAI,SAAS,EAAE,EAAE,EAAE,CAAC,CAAC;SAC/C;aAAM;YACL,OAAO,CAAC,IAAI,CAAC,aAAa,SAAS,EAAE,EAAE,EAAE,CAAC,CAAC;YAC3C,OAAO,CAAC,IAAI,CAAC,GAAG,SAAS,IAAI,SAAS,EAAE,EAAE,EAAE,CAAC,CAAC;SAC/C;IACH,CAAC,CAAC;AACJ,CAAC;AAED,6CAA6C;AAC7C,MAAM,UAAU,KAAK;IACnB,OAAO,CAAC,OAAO,EAAE,CAAC,EAAE,OAAO,EAAE,EAAE;QAC7B,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;QAEpC,gBAAgB;QAChB,MAAM,KAAK,GAAG,YAAY,CACxB,UAAU,CAAC,KAAK,EAChB,IAAI,EACJ,OAAO,EACP,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,CAAC,CACtB,CAAC;QACF,MAAM,QAAQ,GAAG,YAAY,CAC3B,UAAU,CAAC,QAAQ,EACnB,IAAI,EACJ,OAAO,EACP,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,CAAC,CACtB,CAAC;QAEF,kBAAkB;QAClB,MAAM,UAAU,GAAG,YAAY,CAAC,UAAU,CAAC,WAAW,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QACvE,MAAM,WAAW,GAAG,YAAY,CAAC,UAAU,CAAC,YAAY,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QACzE,MAAM,SAAS,GAAG,YAAY,CAAC,UAAU,CAAC,UAAU,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QACrE,MAAM,WAAW,GAAG,YAAY,CAAC,UAAU,CAAC,YAAY,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QACzE,MAAM,UAAU,GAAG,YAAY,CAAC,UAAU,CAAC,WAAW,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QACvE,MAAM,WAAW,GAAG,YAAY,CAAC,UAAU,CAAC,YAAY,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QACzE,MAAM,YAAY,GAAG,YAAY,CAAC,UAAU,CAAC,aAAa,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QAC3E,MAAM,YAAY,GAAG,YAAY,CAAC,UAAU,CAAC,aAAa,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QAC3E,MAAM,gBAAgB,GAAG,YAAY,CACnC,UAAU,CAAC,iBAAiB,EAC5B,IAAI,EACJ,OAAO,CACR,CAAC;QAEF,wBAAwB;QACxB,MAAM,SAAS,GAAG,YAAY,CAAC,UAAU,CAAC,UAAU,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QACrE,MAAM,IAAI,GAAG,YAAY,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QAC1D,MAAM,OAAO,GAAG,YAAY,CAAC,UAAU,CAAC,QAAQ,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QACjE,MAAM,SAAS,GAAG,YAAY,CAAC,UAAU,CAAC,UAAU,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QACrE,MAAM,SAAS,GAAG,YAAY,CAAC,UAAU,CAAC,UAAU,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QACrE,MAAM,QAAQ,GAAG,YAAY,CAAC,UAAU,CAAC,SAAS,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QACnE,MAAM,IAAI,GAAG,YAAY,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QAE1D,oBAAoB;QACpB,SAAS,CAAC,gBAAgB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAC3C,SAAS,CAAC,gBAAgB,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;QAE9C,oBAAoB;QACpB,SAAS,CAAC,gBAAgB,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;QACrD,SAAS,CAAC,gBAAgB,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;QACvD,SAAS,CAAC,gBAAgB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;QACnD,SAAS,CAAC,gBAAgB,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;QACvD,SAAS,CAAC,gBAAgB,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;QACrD,SAAS,CAAC,gBAAgB,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;QACvD,SAAS,CAAC,gBAAgB,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;QACzD,SAAS,CAAC,gBAAgB,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;QACzD,SAAS,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,gBAAgB,CAAC,CAAC;QAEjE,iBAAiB;QACjB,SAAS,CAAC,gBAAgB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;QACnD,SAAS,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QACzC,SAAS,CAAC,gBAAgB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAC/C,SAAS,CAAC,gBAAgB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;QACnD,SAAS,CAAC,gBAAgB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;QACnD,SAAS,CAAC,gBAAgB,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;QACjD,SAAS,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAEzC,OAAO,GAAG,EAAE;YACV,SAAS,CAAC,mBAAmB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YAC9C,SAAS,CAAC,mBAAmB,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;YAEjD,SAAS,CAAC,mBAAmB,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;YACxD,SAAS,CAAC,mBAAmB,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;YAC1D,SAAS,CAAC,mBAAmB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACtD,SAAS,CAAC,mBAAmB,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;YAC1D,SAAS,CAAC,mBAAmB,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;YACxD,SAAS,CAAC,mBAAmB,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;YAC1D,SAAS,CAAC,mBAAmB,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;YAC5D,SAAS,CAAC,mBAAmB,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;YAC5D,SAAS,CAAC,mBAAmB,CAAC,kBAAkB,EAAE,gBAAgB,CAAC,CAAC;YAEpE,SAAS,CAAC,mBAAmB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACtD,SAAS,CAAC,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;YAC5C,SAAS,CAAC,mBAAmB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YAClD,SAAS,CAAC,mBAAmB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACtD,SAAS,CAAC,mBAAmB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACtD,SAAS,CAAC,mBAAmB,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YACpD,SAAS,CAAC,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAC9C,CAAC,CAAC;IACJ,CAAC,CAAC;AACJ,CAAC;AAED,KAAK,CAAC,KAAK,GAAG;IACZ,iBAAiB,EAAE,IAAI;CACxB,CAAC"}