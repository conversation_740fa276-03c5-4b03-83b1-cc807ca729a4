{"version": 3, "sources": ["../browser/src/metadata-args/JoinTableMetadataArgs.ts"], "names": [], "mappings": "", "file": "JoinTableMetadataArgs.js", "sourcesContent": ["import { JoinColumnMetadataArgs } from \"./JoinColumnMetadataArgs\"\n\n/**\n * Arguments for JoinTableMetadata class.\n */\nexport interface JoinTableMetadataArgs {\n    /**\n     * Class to which this column is applied.\n     */\n    readonly target: Function | string\n\n    /**\n     * Class's property name to which this column is applied.\n     */\n    readonly propertyName: string\n\n    /**\n     * Name of the table that will be created to store values of the both tables (join table).\n     * By default is auto generated.\n     */\n    readonly name?: string\n\n    /**\n     * First column of the join table.\n     */\n    readonly joinColumns?: JoinColumnMetadataArgs[]\n\n    /**\n     * Second (inverse) column of the join table.\n     */\n    readonly inverseJoinColumns?: JoinColumnMetadataArgs[]\n\n    /**\n     * Database where join table will be created.\n     * Works only in some databases (like mysql and mssql).\n     */\n    readonly database?: string\n\n    /**\n     * Schema where join table will be created.\n     * Works only in some databases (like postgres and mssql).\n     */\n    readonly schema?: string\n\n    /**\n     * Indicates if schema synchronization is enabled or disabled junction table.\n     * If it will be set to false then schema sync will and migrations ignores junction table.\n     * By default schema synchronization is enabled.\n     */\n    readonly synchronize?: boolean\n}\n"], "sourceRoot": ".."}