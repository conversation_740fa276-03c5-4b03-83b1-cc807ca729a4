import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface UIState {
  // 全局加载状态
  globalLoading: boolean;
  
  // 侧边栏状态
  sidebarCollapsed: boolean;
  
  // 主题设置
  theme: 'light' | 'dark';
  
  // 语言设置
  locale: 'zh-CN' | 'en-US';
  
  // 地图相关状态
  mapMode: 'distribution' | 'collection';
  mapCenter: [number, number];
  mapZoom: number;
  
  // 音频播放器状态
  audioPlayer: {
    isPlaying: boolean;
    currentTrack: string | null;
    volume: number;
    currentTime: number;
    duration: number;
    playlist: string[];
    playMode: 'single' | 'loop' | 'shuffle';
  };
  
  // 搜索状态
  searchHistory: string[];
  
  // 通知消息
  notifications: Array<{
    id: string;
    type: 'success' | 'error' | 'warning' | 'info';
    title: string;
    message: string;
    timestamp: number;
    read: boolean;
  }>;
  
  // 模态框状态
  modals: {
    loginVisible: boolean;
    registerVisible: boolean;
    profileVisible: boolean;
    settingsVisible: boolean;
  };
  
  // 页面状态
  pageLoading: Record<string, boolean>;
}

const initialState: UIState = {
  globalLoading: false,
  sidebarCollapsed: false,
  theme: 'light',
  locale: 'zh-CN',
  mapMode: 'distribution',
  mapCenter: [30.0, 120.0], // 默认中心点
  mapZoom: 5,
  audioPlayer: {
    isPlaying: false,
    currentTrack: null,
    volume: 0.8,
    currentTime: 0,
    duration: 0,
    playlist: [],
    playMode: 'single',
  },
  searchHistory: JSON.parse(localStorage.getItem('searchHistory') || '[]'),
  notifications: [],
  modals: {
    loginVisible: false,
    registerVisible: false,
    profileVisible: false,
    settingsVisible: false,
  },
  pageLoading: {},
};

const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    // 全局加载状态
    setGlobalLoading: (state, action: PayloadAction<boolean>) => {
      state.globalLoading = action.payload;
    },
    
    // 侧边栏
    toggleSidebar: (state) => {
      state.sidebarCollapsed = !state.sidebarCollapsed;
    },
    setSidebarCollapsed: (state, action: PayloadAction<boolean>) => {
      state.sidebarCollapsed = action.payload;
    },
    
    // 主题
    setTheme: (state, action: PayloadAction<'light' | 'dark'>) => {
      state.theme = action.payload;
      localStorage.setItem('theme', action.payload);
    },
    
    // 语言
    setLocale: (state, action: PayloadAction<'zh-CN' | 'en-US'>) => {
      state.locale = action.payload;
      localStorage.setItem('locale', action.payload);
    },
    
    // 地图状态
    setMapMode: (state, action: PayloadAction<'distribution' | 'collection'>) => {
      state.mapMode = action.payload;
    },
    setMapCenter: (state, action: PayloadAction<[number, number]>) => {
      state.mapCenter = action.payload;
    },
    setMapZoom: (state, action: PayloadAction<number>) => {
      state.mapZoom = action.payload;
    },
    
    // 音频播放器
    setAudioPlaying: (state, action: PayloadAction<boolean>) => {
      state.audioPlayer.isPlaying = action.payload;
    },
    setCurrentTrack: (state, action: PayloadAction<string | null>) => {
      state.audioPlayer.currentTrack = action.payload;
    },
    setAudioVolume: (state, action: PayloadAction<number>) => {
      state.audioPlayer.volume = action.payload;
    },
    setAudioCurrentTime: (state, action: PayloadAction<number>) => {
      state.audioPlayer.currentTime = action.payload;
    },
    setAudioDuration: (state, action: PayloadAction<number>) => {
      state.audioPlayer.duration = action.payload;
    },
    setPlaylist: (state, action: PayloadAction<string[]>) => {
      state.audioPlayer.playlist = action.payload;
    },
    setPlayMode: (state, action: PayloadAction<'single' | 'loop' | 'shuffle'>) => {
      state.audioPlayer.playMode = action.payload;
    },
    
    // 搜索历史
    addSearchHistory: (state, action: PayloadAction<string>) => {
      const keyword = action.payload.trim();
      if (keyword && !state.searchHistory.includes(keyword)) {
        state.searchHistory.unshift(keyword);
        // 限制历史记录数量
        if (state.searchHistory.length > 10) {
          state.searchHistory = state.searchHistory.slice(0, 10);
        }
        localStorage.setItem('searchHistory', JSON.stringify(state.searchHistory));
      }
    },
    clearSearchHistory: (state) => {
      state.searchHistory = [];
      localStorage.removeItem('searchHistory');
    },
    
    // 通知消息
    addNotification: (state, action: PayloadAction<{
      type: 'success' | 'error' | 'warning' | 'info';
      title: string;
      message: string;
    }>) => {
      const notification = {
        id: Date.now().toString(),
        ...action.payload,
        timestamp: Date.now(),
        read: false,
      };
      state.notifications.unshift(notification);
      
      // 限制通知数量
      if (state.notifications.length > 50) {
        state.notifications = state.notifications.slice(0, 50);
      }
    },
    markNotificationAsRead: (state, action: PayloadAction<string>) => {
      const notification = state.notifications.find(n => n.id === action.payload);
      if (notification) {
        notification.read = true;
      }
    },
    removeNotification: (state, action: PayloadAction<string>) => {
      state.notifications = state.notifications.filter(n => n.id !== action.payload);
    },
    clearAllNotifications: (state) => {
      state.notifications = [];
    },
    
    // 模态框
    setModalVisible: (state, action: PayloadAction<{
      modal: keyof UIState['modals'];
      visible: boolean;
    }>) => {
      state.modals[action.payload.modal] = action.payload.visible;
    },
    
    // 页面加载状态
    setPageLoading: (state, action: PayloadAction<{
      page: string;
      loading: boolean;
    }>) => {
      state.pageLoading[action.payload.page] = action.payload.loading;
    },
  },
});

export const {
  setGlobalLoading,
  toggleSidebar,
  setSidebarCollapsed,
  setTheme,
  setLocale,
  setMapMode,
  setMapCenter,
  setMapZoom,
  setAudioPlaying,
  setCurrentTrack,
  setAudioVolume,
  setAudioCurrentTime,
  setAudioDuration,
  setPlaylist,
  setPlayMode,
  addSearchHistory,
  clearSearchHistory,
  addNotification,
  markNotificationAsRead,
  removeNotification,
  clearAllNotifications,
  setModalVisible,
  setPageLoading,
} = uiSlice.actions;

export default uiSlice.reducer;
