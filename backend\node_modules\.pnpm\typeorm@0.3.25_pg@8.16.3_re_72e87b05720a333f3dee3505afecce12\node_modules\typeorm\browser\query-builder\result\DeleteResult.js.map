{"version": 3, "sources": ["../browser/src/query-builder/result/DeleteResult.ts"], "names": [], "mappings": "AAEA;;GAEG;AACH,MAAM,OAAO,YAAY;IACrB,MAAM,CAAC,IAAI,CAAC,WAAwB;QAChC,MAAM,MAAM,GAAG,IAAI,IAAI,EAAE,CAAA;QAEzB,MAAM,CAAC,GAAG,GAAG,WAAW,CAAC,OAAO,CAAA;QAChC,MAAM,CAAC,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAA;QAEtC,OAAO,MAAM,CAAA;IACjB,CAAC;CAYJ", "file": "DeleteResult.js", "sourcesContent": ["import { QueryResult } from \"../../query-runner/QueryResult\"\n\n/**\n * Result object returned by DeleteQueryBuilder execution.\n */\nexport class DeleteResult {\n    static from(queryResult: QueryResult) {\n        const result = new this()\n\n        result.raw = queryResult.records\n        result.affected = queryResult.affected\n\n        return result\n    }\n\n    /**\n     * Raw SQL result returned by executed query.\n     */\n    raw: any\n\n    /**\n     * Number of affected rows/documents\n     * Not all drivers support this\n     */\n    affected?: number | null\n}\n"], "sourceRoot": "../.."}