import React, { useState } from 'react';
import { 
  Modal, 
  Form, 
  Select, 
  DatePicker, 
  Checkbox, 
  Button, 
  Space, 
  Typography,
  Progress,
  message
} from 'antd';
import { 
  DownloadOutlined, 
  FileExcelOutlined, 
  FilePdfOutlined,
  FileTextOutlined
} from '@ant-design/icons';

const { Option } = Select;
const { RangePicker } = DatePicker;
const { Text } = Typography;

interface DataExportProps {
  visible: boolean;
  onClose: () => void;
  onExport?: (config: ExportConfig) => void;
}

interface ExportConfig {
  dataType: string[];
  format: string;
  dateRange?: [string, string];
  includeImages: boolean;
  includeAudio: boolean;
  includeMetadata: boolean;
}

const DataExport: React.FC<DataExportProps> = ({
  visible,
  onClose,
  onExport,
}) => {
  const [form] = Form.useForm();
  const [exporting, setExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);

  // 处理导出
  const handleExport = async (values: any) => {
    setExporting(true);
    setExportProgress(0);

    try {
      const config: ExportConfig = {
        dataType: values.dataType,
        format: values.format,
        dateRange: values.dateRange ? [
          values.dateRange[0].format('YYYY-MM-DD'),
          values.dateRange[1].format('YYYY-MM-DD')
        ] : undefined,
        includeImages: values.includeImages || false,
        includeAudio: values.includeAudio || false,
        includeMetadata: values.includeMetadata || false,
      };

      // 模拟导出进度
      const progressInterval = setInterval(() => {
        setExportProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + 10;
        });
      }, 200);

      // 调用导出API
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/export`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify(config),
      });

      clearInterval(progressInterval);
      setExportProgress(100);

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `marine_bio_data_${Date.now()}.${config.format}`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        message.success('数据导出成功');
        onExport?.(config);
        onClose();
      } else {
        throw new Error('导出失败');
      }
    } catch (error) {
      message.error('数据导出失败，请重试');
    } finally {
      setExporting(false);
      setExportProgress(0);
    }
  };

  // 重置表单
  const handleReset = () => {
    form.resetFields();
    setExportProgress(0);
  };

  return (
    <Modal
      title={
        <Space>
          <DownloadOutlined />
          数据导出
        </Space>
      }
      open={visible}
      onCancel={onClose}
      footer={null}
      width={600}
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleExport}
        initialValues={{
          dataType: ['species'],
          format: 'xlsx',
          includeMetadata: true,
        }}
      >
        {/* 数据类型选择 */}
        <Form.Item
          name="dataType"
          label="数据类型"
          rules={[{ required: true, message: '请选择要导出的数据类型' }]}
        >
          <Select
            mode="multiple"
            placeholder="选择数据类型"
            style={{ width: '100%' }}
          >
            <Option value="species">物种信息</Option>
            <Option value="audio">音频文件</Option>
            <Option value="image">图片文件</Option>
            <Option value="distribution">分布数据</Option>
            <Option value="collection">采集点数据</Option>
          </Select>
        </Form.Item>

        {/* 导出格式 */}
        <Form.Item
          name="format"
          label="导出格式"
          rules={[{ required: true, message: '请选择导出格式' }]}
        >
          <Select placeholder="选择导出格式">
            <Option value="xlsx">
              <Space>
                <FileExcelOutlined style={{ color: '#52c41a' }} />
                Excel (.xlsx)
              </Space>
            </Option>
            <Option value="csv">
              <Space>
                <FileTextOutlined style={{ color: '#1890ff' }} />
                CSV (.csv)
              </Space>
            </Option>
            <Option value="json">
              <Space>
                <FileTextOutlined style={{ color: '#fa8c16' }} />
                JSON (.json)
              </Space>
            </Option>
            <Option value="pdf">
              <Space>
                <FilePdfOutlined style={{ color: '#f5222d' }} />
                PDF (.pdf)
              </Space>
            </Option>
          </Select>
        </Form.Item>

        {/* 日期范围 */}
        <Form.Item
          name="dateRange"
          label="日期范围"
        >
          <RangePicker
            style={{ width: '100%' }}
            placeholder={['开始日期', '结束日期']}
          />
        </Form.Item>

        {/* 附加选项 */}
        <Form.Item label="附加选项">
          <Space direction="vertical">
            <Form.Item name="includeImages" valuePropName="checked" noStyle>
              <Checkbox>包含图片文件</Checkbox>
            </Form.Item>
            <Form.Item name="includeAudio" valuePropName="checked" noStyle>
              <Checkbox>包含音频文件</Checkbox>
            </Form.Item>
            <Form.Item name="includeMetadata" valuePropName="checked" noStyle>
              <Checkbox>包含元数据</Checkbox>
            </Form.Item>
          </Space>
        </Form.Item>

        {/* 导出进度 */}
        {exporting && (
          <div style={{ marginBottom: '16px' }}>
            <Text>导出进度:</Text>
            <Progress 
              percent={exportProgress} 
              status={exportProgress === 100 ? 'success' : 'active'}
              style={{ marginTop: '8px' }}
            />
          </div>
        )}

        {/* 操作按钮 */}
        <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
          <Space>
            <Button onClick={handleReset} disabled={exporting}>
              重置
            </Button>
            <Button onClick={onClose} disabled={exporting}>
              取消
            </Button>
            <Button 
              type="primary" 
              htmlType="submit"
              loading={exporting}
              icon={<DownloadOutlined />}
            >
              开始导出
            </Button>
          </Space>
        </Form.Item>
      </Form>

      {/* 导出说明 */}
      <div style={{ 
        marginTop: '24px', 
        padding: '12px', 
        background: '#f6ffed',
        border: '1px solid #b7eb8f',
        borderRadius: '6px'
      }}>
        <Text style={{ fontSize: '12px', color: '#52c41a' }}>
          <strong>导出说明:</strong>
          <br />
          • Excel格式适合数据分析和处理
          <br />
          • CSV格式适合导入其他系统
          <br />
          • JSON格式适合程序化处理
          <br />
          • PDF格式适合报告和展示
          <br />
          • 包含媒体文件时会生成压缩包
        </Text>
      </div>
    </Modal>
  );
};

export default DataExport;
