{"version": 3, "file": "coordinate.js", "sourceRoot": "", "sources": ["../../src/runtime/coordinate.ts"], "names": [], "mappings": ";;;;;;;;;;;AAAA,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,MAAM,aAAa,CAAC;AAGvD,OAAO,EAAE,UAAU,EAAE,MAAM,WAAW,CAAC;AAGvC,MAAM,UAAU,gBAAgB,CAC9B,MAAc,EACd,cAAsB,EACtB,OAAkB;IAElB,MAAM,CAAC,aAAa,CAAC,GAAG,UAAU,CAIhC,YAAY,EAAE,OAAO,CAAC,CAAC;IACzB,MAAM,EACJ,WAAW,EACX,UAAU,EACV,SAAS,EACT,QAAQ,EACR,UAAU,EACV,WAAW,GACZ,GAAG,MAAM,CAAC;IACX,MAAM,EAAE,WAAW,EAAE,gBAAgB,GAAG,EAAE,EAAE,GAAG,cAAc,CAAC;IAC9D,MAAM,SAAS,GAAG,eAAe,CAAC,gBAAgB,CAAC,CAAC;IAEpD,MAAM,aAAa,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,aAAa,CAAC;IAC1D,MAAM,OAAO,mCAGR,MAAM,KACT,CAAC,EAAE,SAAS,EACZ,CAAC,EAAE,QAAQ,EACX,KAAK,EAAE,UAAU,GAAG,SAAS,GAAG,UAAU,EAC1C,MAAM,EAAE,WAAW,GAAG,WAAW,GAAG,QAAQ,EAC5C,eAAe,EAAE,SAAS,CAAC,OAAO,CAAC,aAAa,CAAC,GAClD,CAAC;IAEF,MAAM,UAAU,GAAG,aAAa;QAC9B,CAAC,CAAC,aAAa;YACb,IAAI,YAAY,CAAC,OAAO,CAAC;QAC3B,CAAC,CAAC,IAAI,UAAU,CAAC,OAAO,CAAC,CAAC;IAC5B,OAAO,UAAwB,CAAC;AAClC,CAAC;AAED,MAAM,UAAU,oBAAoB,CAAC,IAAY,EAAE,OAAkB;IACnE,aAAa;IACb,MAAM,EAAE,UAAU,GAAG,EAAE,EAAE,WAAW,KAAc,IAAI,EAAb,IAAI,UAAK,IAAI,EAAhD,6BAAyC,CAAO,CAAC;IAEvD,kFAAkF;IAClF,4EAA4E;IAC5E,IAAI,WAAW;QAAE,OAAO,IAAI,CAAC;IAE7B,MAAM,EAAE,IAAI,EAAE,SAAS,GAAG,EAAE,KAAiB,UAAU,EAAtB,OAAO,UAAK,UAAU,EAAjD,qBAAoC,CAAa,CAAC;IACxD,IAAI,CAAC,IAAI;QAAE,uCAAY,IAAI,KAAE,WAAW,EAAE,SAAS,IAAG;IACtD,MAAM,CAAC,EAAE,gBAAgB,CAAC,GAAG,UAAU,CAIrC,YAAY,EAAE,OAAO,CAAC,CAAC;IACzB,MAAM,EAAE,SAAS,EAAE,WAAW,GAAG,KAAK,EAAE,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;IAC9E,IAAI,WAAW,EAAE;QACf,MAAM,IAAI,KAAK,CAAC,uBAAuB,IAAI,GAAG,CAAC,CAAC;KACjD;IACD,uCAAY,IAAI,KAAE,WAAW,EAAE,iBAAG,IAAI,IAAK,OAAO,GAAI,GAAG,SAAS,CAAC,IAAG;AACxE,CAAC;AAED,MAAM,UAAU,OAAO,CACrB,WAAkC,EAClC,IAAY;IAEZ,OAAO,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;AACpD,CAAC;AAED;;GAEG;AAEH,MAAM,UAAU,OAAO,CAAC,WAAkC;IACxD,OAAO,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;AAClD,CAAC;AAED,MAAM,UAAU,OAAO,CAAC,WAAkC;IACxD,OAAO,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;AAClD,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,WAAW,CAAC,WAAkC;IAC5D,OAAO,OAAO,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,CAAC;AAC5D,CAAC;AAED,MAAM,UAAU,UAAU,CAAC,WAAkC;IAC3D,OAAO,OAAO,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;AACrD,CAAC;AAED,MAAM,UAAU,OAAO,CAAC,WAAkC;IACxD,OAAO,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;AAClD,CAAC;AAED,MAAM,UAAU,SAAS,CAAC,WAAkC;IAC1D,OAAO,OAAO,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;AACpD,CAAC;AAED,MAAM,UAAU,QAAQ,CAAC,WAAkC;IACzD,OAAO,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;AACnD,CAAC;AAED,MAAM,UAAU,OAAO,CAAC,WAAkC;IACxD,OAAO,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;AAClD,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,UAAU,CAAC,WAAkC;IAC3D,OAAO,OAAO,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;AACrD,CAAC;AAED,SAAS,eAAe,CACtB,WAAkC;IAElC,IACE,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,WAAW,IAAI,CAAC,CAAC,IAAI,KAAK,aAAa,CAAC;QAE3E,OAAO,WAAW,CAAC;IACrB,OAAO,CAAC,GAAG,WAAW,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;AACjD,CAAC"}