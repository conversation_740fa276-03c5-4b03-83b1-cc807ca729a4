{"version": 3, "file": "curve.js", "sourceRoot": "", "sources": ["../../../src/shape/area/curve.ts"], "names": [], "mappings": ";;;;;;;;;;;AAAA,OAAO,EAAE,IAAI,EAAE,UAAU,EAAgB,MAAM,uBAAuB,CAAC;AACvE,OAAO,EAAE,MAAM,EAAE,MAAM,uBAAuB,CAAC;AAC/C,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AAE9D,OAAO,EAAE,iBAAiB,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAClE,OAAO,EAAE,UAAU,EAAE,eAAe,EAAE,YAAY,EAAE,MAAM,UAAU,CAAC;AACrE,OAAO,EAAE,SAAS,EAAE,MAAM,oBAAoB,CAAC;AAC/C,OAAO,EAAE,aAAa,EAAE,MAAM,2BAA2B,CAAC;AAE1D;;;;;;GAMG;AACH,SAAS,YAAY,CACnB,MAAiB,EACjB,OAA4B;IAE5B,MAAM,eAAe,GAAG,EAAE,CAAC;IAC3B,MAAM,eAAe,GAAG,EAAE,CAAC;IAC3B,MAAM,QAAQ,GAAG,EAAE,CAAC;IAEpB,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,8BAA8B;IAC7C,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,8BAA8B;IAE7C,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;IAC9B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;QAC5B,MAAM,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QACrB,MAAM,EAAE,GAAG,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;QAE3B,yCAAyC;QACzC,8BAA8B;QAC9B,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAAE,CAAC,GAAG,IAAI,CAAC;aACjD;YACH,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACzB,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACzB,uCAAuC;YACvC,uCAAuC;YACvC,yCAAyC;YACzC,kCAAkC;YAClC,IAAI,CAAC,IAAI,EAAE,EAAE;gBACX,CAAC,GAAG,KAAK,CAAC;gBACV,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;gBACxB,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC;aACrC;YACD,qCAAqC;YACrC,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;SACf;KACF;IACD,OAAO,CAAC,eAAe,CAAC,MAAM,CAAC,eAAe,CAAC,EAAE,QAAQ,CAAC,CAAC;AAC7D,CAAC;AAQD,MAAM,UAAU,GAAG,aAAa,CAAC,CAAC,CAAC,EAAE,EAAE;IACrC,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,YAAY,EAAE,GAAG,CAAC,CAAC,UAAU,CAAC;IACxE,MAAM,QAAQ,GAAG,CAAC,CAAC,aAAa,CAAC;IACjC,MAAM,CAAC,CAAC,CAAC;SACN,WAAW,CAAC,cAAc,EAAE,GAAG,EAAE,CAAC,QAAQ,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;SACrE,KAAK,CAAC,GAAG,EAAE,WAAW,CAAC;SACvB,IAAI,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;IAClC,MAAM,CAAC,CAAC,CAAC;SACN,WAAW,CAAC,WAAW,EAAE,GAAG,EAAE,CAAC,QAAQ,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;SAClE,KAAK,CAAC,GAAG,EAAE,QAAQ,CAAC;SACpB,IAAI,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;AACjC,CAAC,CAAC,CAAC;AAEH,MAAM,CAAC,MAAM,KAAK,GAAqB,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;IAC1D,MAAM,EACJ,KAAK,EACL,QAAQ,GAAG,KAAK,EAChB,OAAO,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,IAAI,EAClE,OAAO,EAAE,YAAY,GAAG,KAAK,KAE3B,OAAO,EADN,KAAK,UACN,OAAO,EANL,2CAML,CAAU,CAAC;IACZ,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC;IAEzC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE;QAC5B,MAAM,EAAE,KAAK,EAAE,YAAY,EAAE,GAAG,QAAQ,CAAC;QACzC,MAAM,EACJ,KAAK,GAAG,YAAY,EACpB,WAAW,EAAE,EAAE,EACf,OAAO,EAAE,EAAE,EACX,OAAO,EAAE,EAAE,GACZ,GAAG,KAAK,CAAC;QACV,MAAM,OAAO,GAAG,WAAW,CAAC,UAAU,CAAC,CAAC;QACxC,MAAM,SAAS,GAAG,YAAY,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;QAClD,MAAM,IAAI,GACR,QAAQ,IAAI,EAAE;YACZ,CAAC,CAAC,eAAe,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,CAAC;YAC3D,CAAC,CAAC,KAAK,CAAC;QAEZ,MAAM,UAAU,+DACX,QAAQ,KACX,MAAM,EAAE,IAAI,EACZ,IAAI,EAAE,IAAI,KACP,CAAC,SAAS,IAAI,EAAE,SAAS,EAAE,CAAC,GAC5B,KAAK,CACT,CAAC;QAEF,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,YAAY,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;QAE1C,MAAM,YAAY,GAAG,SAAS,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;QACtD,MAAM,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC;QAE5B,MAAM,WAAW,GAAG,CAAC,IAAI,EAAE,EAAE;YAC3B,OAAO,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;iBAC9C,KAAK,CAAC,GAAG,EAAE,IAAI,IAAI,EAAE,CAAC;iBACtB,IAAI,CAAC,UAAU,EAAE,UAAU,CAAC;iBAC5B,IAAI,EAAE,CAAC;QACZ,CAAC,CAAC;QAEF,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;YACxB;;eAEG;YACH,MAAM,QAAQ,GAAG,CAAC,MAAM,EAAE,EAAE;gBAC1B,MAAM,EAAE,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAC9C,MAAM,EAAE,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAC3C,OAAO,OAAO;oBACZ,CAAC,CAAC,IAAI,EAAE;yBACH,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;yBACzB,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;yBAC1B,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;yBAC1B,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;yBAC5D,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;oBACrB,CAAC,CAAC,IAAI,EAAE;yBACH,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;yBACzB,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;yBAC1B,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;yBAC1B,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;yBAC5D,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;YAC1B,CAAC,CAAC;YAEF,6CAA6C;YAC7C,IAAI,CAAC,OAAO,IAAI,CAAC,YAAY,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,EAAE;gBACnE,OAAO,WAAW,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;aAClC;YAED,+CAA+C;YAC/C,IAAI,OAAO,IAAI,CAAC,YAAY,EAAE;gBAC5B,OAAO,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;aACjC;YAED,iBAAiB;YACjB,sCAAsC;YACtC,8BAA8B;YAC9B,OAAO,MAAM,CAAC,IAAI,UAAU,EAAE,CAAC;iBAC5B,KAAK,CAAC,WAAW,EAAE,UAAU,CAAC;iBAC9B,KAAK,CAAC,cAAc,kCAAO,YAAY,GAAK,KAAK,EAAG;iBACpD,KAAK,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;iBAC9B,KAAK,CAAC,aAAa,EAAE,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;iBAC/C,IAAI,EAAE,CAAC;SACX;aAAM;YACL;;eAEG;YACH,MAAM,cAAc,GAAG,CAAC,MAAM,EAAE,EAAE;gBAChC,MAAM,MAAM,GAAG,UAAU,CAAC,SAAS,EAAa,CAAC;gBACjD,MAAM,EAAE,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAC9C,MAAM,EAAE,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAE3C,OAAO,UAAU,EAAE;qBAChB,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,iBAAiB,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;qBAC1D,WAAW,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,CAAC;qBAC9C,WAAW,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,CAAC;qBAC9C,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;qBAC5D,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;YACtB,CAAC,CAAC;YAEF,6CAA6C;YAC7C,IAAI,CAAC,OAAO,IAAI,CAAC,YAAY,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,EAAE;gBACnE,OAAO,WAAW,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC;aACxC;YAED,+CAA+C;YAC/C,IAAI,OAAO,IAAI,CAAC,YAAY,EAAE;gBAC5B,OAAO,WAAW,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;aACvC;YAED,iBAAiB;YACjB,sCAAsC;YACtC,8BAA8B;YAC9B,OAAO,MAAM,CAAC,IAAI,UAAU,EAAE,CAAC;iBAC5B,KAAK,CAAC,WAAW,EAAE,UAAU,CAAC;iBAC9B,KAAK,CAAC,cAAc,kCAAO,YAAY,GAAK,KAAK,EAAG;iBACpD,KAAK,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC;iBACpC,KAAK,CAAC,aAAa,EAAE,EAAE,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;iBACrD,IAAI,EAAE,CAAC;SACX;IACH,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF,KAAK,CAAC,KAAK,GAAG;IACZ,aAAa,EAAE,QAAQ;IACvB,qBAAqB,EAAE,QAAQ;IAC/B,sBAAsB,EAAE,UAAU;IAClC,oBAAoB,EAAE,SAAS;CAChC,CAAC"}