export declare function plotlib(): {
    readonly 'data.venn': import("../runtime").DataComponent<import("../data").VennOptions>;
    readonly 'mark.boxplot': import("../runtime").CompositeMarkComponent<import("../mark/boxplot").BoxPlotOptions>;
    readonly 'mark.gauge': import("../runtime").CompositeMarkComponent<import("../mark").GaugeOptions>;
    readonly 'mark.wordCloud': import("../runtime").CompositeMarkComponent<import("../mark").WordCloudOptions>;
    readonly 'mark.liquid': import("../runtime").CompositeMarkComponent<import("../mark").LiquidOptions>;
};
