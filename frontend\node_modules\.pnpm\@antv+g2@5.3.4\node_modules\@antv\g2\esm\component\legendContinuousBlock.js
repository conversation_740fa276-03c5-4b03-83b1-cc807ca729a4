import { LegendContinuous } from './legendContinuous';
export const LegendContinuousBlock = (options) => {
    return (...args) => LegendContinuous(Object.assign({}, { block: true }, options))(...args);
};
LegendContinuousBlock.props = Object.assign(Object.assign({}, LegendContinuous.props), { defaultPosition: 'top', defaultOrientation: 'horizontal' });
//# sourceMappingURL=legendContinuousBlock.js.map