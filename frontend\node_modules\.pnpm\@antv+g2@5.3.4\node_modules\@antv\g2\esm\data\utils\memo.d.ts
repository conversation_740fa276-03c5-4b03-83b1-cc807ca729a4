import { TransformComponent } from '../../runtime';
/**
 * Returns a sync function returning memoized transform of preprocessor and connector.
 * The memoized value will recompute only when the data reference or options has changed.
 */
export declare function useMemoPreprocessor<T>(Preprocessor: TransformComponent<T>): TransformComponent<T>;
/**
 * Returns a async function returning memoized transform and connector.
 * The memoized value will recompute only when the data reference or options has changed.
 */
export declare function useAsyncMemoPreprocessor<T>(Preprocessor: TransformComponent<T>): TransformComponent<T>;
/**
 * Returns a async function returning memoized connector transform.
 * The memoized value will recompute only when options has changed
 * and ignore data.
 */
export declare function useMemoConnector<T>(Connector: TransformComponent<T>): TransformComponent<T>;
