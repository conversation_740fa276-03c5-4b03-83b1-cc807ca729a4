{"version": 3, "sources": ["../browser/src/query-builder/QueryBuilderUtils.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,MAAM,OAAO,iBAAiB;IAC1B;;;OAGG;IACH,MAAM,CAAC,eAAe,CAAC,GAAQ;QAC3B,gEAAgE;QAChE,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAAE,OAAO,KAAK,CAAA;QAEpE,wCAAwC;QACxC,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA,CAAC,0CAA0C;QAC3F,IAAI,CAAC,SAAS,IAAI,CAAC,YAAY;YAAE,OAAO,KAAK,CAAA;QAE7C,6DAA6D;QAC7D,8CAA8C;QAC9C,+EAA+E;QAC/E,oBAAoB;QACpB,qCAAqC;QACrC,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAAE,OAAO,KAAK,CAAA;QAEpE,OAAO,IAAI,CAAA;IACf,CAAC;CACJ", "file": "QueryBuilderUtils.js", "sourcesContent": ["/**\n * Helper utility functions for QueryBuilder.\n */\nexport class QueryBuilderUtils {\n    /**\n     * Checks if given value is a string representation of alias property,\n     * e.g. \"post.category\" or \"post.id\".\n     */\n    static isAliasProperty(str: any): str is string {\n        // alias property must be a string and must have a dot separator\n        if (typeof str !== \"string\" || str.indexOf(\".\") === -1) return false\n\n        // extra alias and its property relation\n        const [aliasName, propertyName] = str.split(\".\") // todo: what about relations in embedded?\n        if (!aliasName || !propertyName) return false\n\n        // alias and property must be represented in a special format\n        // const aliasNameRegexp = /^[a-zA-Z0-9_-]+$/;\n        // if (!aliasNameRegexp.test(aliasName) || !aliasNameRegexp.test(propertyName))\n        //     return false;\n        // make sure string is not a subquery\n        if (str.indexOf(\"(\") !== -1 || str.indexOf(\")\") !== -1) return false\n\n        return true\n    }\n}\n"], "sourceRoot": ".."}