import React, { useEffect } from 'react';
import { Outlet, Link, useLocation } from 'react-router-dom';
import {
  Layout as AntLayout,
  Menu,
  Button,
  Space,
  Dropdown,
  Avatar,
  Typography
} from 'antd';
import {
  HomeOutlined,
  SearchOutlined,
  GlobalOutlined,
  DashboardOutlined,
  UserOutlined,
  LoginOutlined,
  LogoutOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { useAppSelector, useAppDispatch } from '../store/hooks';
import { logout, fetchUserProfile } from '../store/slices/authSlice';
import Breadcrumb from './Breadcrumb';
import ErrorBoundary from './ErrorBoundary';

const { Header, Content, Footer } = AntLayout;
const { Text } = Typography;

const Layout: React.FC = () => {
  const location = useLocation();
  const dispatch = useAppDispatch();
  const { user, isAuthenticated, token } = useAppSelector(state => state.auth);

  // 如果有token但没有用户信息，尝试获取用户信息
  useEffect(() => {
    if (token && !user && !isAuthenticated) {
      dispatch(fetchUserProfile());
    }
  }, [token, user, isAuthenticated, dispatch]);

  const menuItems = [
    {
      key: '/',
      icon: <HomeOutlined />,
      label: <Link to="/">首页</Link>,
    },
    {
      key: '/species',
      icon: <SearchOutlined />,
      label: <Link to="/species">物种数据库</Link>,
    },
    {
      key: '/map',
      icon: <GlobalOutlined />,
      label: <Link to="/map">交互式地图</Link>,
    },
    {
      key: '/dashboard',
      icon: <DashboardOutlined />,
      label: <Link to="/dashboard">数据仪表盘</Link>,
    },
  ];

  const handleLogout = () => {
    dispatch(logout());
  };

  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '设置',
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: handleLogout,
    },
  ];

  return (
    <AntLayout style={{ minHeight: '100vh' }}>
      <Header style={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'space-between',
        background: '#fff',
        borderBottom: '1px solid #f0f0f0',
        padding: '0 24px'
      }}>
        {/* Logo和标题 */}
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Link to="/" style={{ display: 'flex', alignItems: 'center', textDecoration: 'none' }}>
            <div style={{
              width: '40px',
              height: '40px',
              background: 'linear-gradient(135deg, #1890ff 0%, #096dd9 100%)',
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              marginRight: '12px',
              color: 'white',
              fontSize: '20px'
            }}>
              🐋
            </div>
            <Text strong style={{ fontSize: '18px', color: '#1890ff' }}>
              海洋生物声音平台
            </Text>
          </Link>
        </div>

        {/* 导航菜单 */}
        <Menu
          mode="horizontal"
          selectedKeys={[location.pathname]}
          items={menuItems}
          style={{ 
            border: 'none',
            background: 'transparent',
            flex: 1,
            justifyContent: 'center'
          }}
        />

        {/* 用户操作区域 */}
        <div>
          {isAuthenticated && user ? (
            <Dropdown menu={{ items: userMenuItems }} placement="bottomRight">
              <Button type="text" style={{ height: 'auto', padding: '4px 8px' }}>
                <Space>
                  <Avatar size="small" icon={<UserOutlined />} />
                  <span>{user.username}</span>
                </Space>
              </Button>
            </Dropdown>
          ) : (
            <Space>
              <Link to="/login">
                <Button type="primary" icon={<LoginOutlined />}>
                  登录
                </Button>
              </Link>
              <Link to="/register">
                <Button icon={<UserOutlined />}>
                  注册
                </Button>
              </Link>
            </Space>
          )}
        </div>
      </Header>

      <Content style={{ background: '#f5f5f5' }}>
        <Breadcrumb />
        <ErrorBoundary>
          <Outlet />
        </ErrorBoundary>
      </Content>

      <Footer style={{ 
        textAlign: 'center', 
        background: '#001529', 
        color: 'rgba(255, 255, 255, 0.65)' 
      }}>
        <div style={{ marginBottom: '16px' }}>
          <Space size="large">
            <Link to="/" style={{ color: 'rgba(255, 255, 255, 0.65)' }}>
              首页
            </Link>
            <Link to="/species" style={{ color: 'rgba(255, 255, 255, 0.65)' }}>
              物种数据库
            </Link>
            <Link to="/map" style={{ color: 'rgba(255, 255, 255, 0.65)' }}>
              交互式地图
            </Link>
            <a href="#" style={{ color: 'rgba(255, 255, 255, 0.65)' }}>
              关于我们
            </a>
            <a href="#" style={{ color: 'rgba(255, 255, 255, 0.65)' }}>
              联系我们
            </a>
          </Space>
        </div>
        <div>
          海洋生物声音平台 ©2024 Created by Marine Bio Team
        </div>
        <div style={{ marginTop: '8px', fontSize: '12px' }}>
          致力于海洋生物声学研究与科学普及
        </div>
      </Footer>
    </AntLayout>
  );
};

export default Layout;
