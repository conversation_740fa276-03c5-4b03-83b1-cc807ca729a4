{"version": 3, "file": "mark.js", "sourceRoot": "", "sources": ["../../src/runtime/mark.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,uBAAuB,CAAC;AAChD,OAAO,EAAE,OAAO,EAAE,MAAM,iBAAiB,CAAC;AAC1C,OAAO,EAAE,UAAU,EAAE,MAAM,WAAW,CAAC;AAYvC,OAAO,EACL,aAAa,EACb,kBAAkB,EAClB,cAAc,EACd,UAAU,EACV,iBAAiB,EACjB,eAAe,EACf,kBAAkB,EAClB,eAAe,EACf,eAAe,EACf,gBAAgB,EAChB,cAAc,GACf,MAAM,aAAa,CAAC;AAErB,MAAM,UAAgB,cAAc,CAClC,WAAmB,EACnB,YAAuB,EACvB,OAAkB;;QAElB,iEAAiE;QACjE,MAAM,CAAC,CAAC,EAAE,eAAe,CAAC,GAAG,MAAM,kBAAkB,CACnD,WAAW,EACX,YAAY,EACZ,OAAO,CACR,CAAC;QAEF,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,eAAe,CAAC;QAEzD,qDAAqD;QACrD,mDAAmD;QACnD,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,KAAK,EAAE;YACjC,OAAO,IAAI,CAAC;SACb;QAED,wEAAwE;QACxE,8EAA8E;QAC9E,MAAM,EAAE,QAAQ,EAAE,kBAAkB,EAAE,GAAG,YAAY,CAAC;QACtD,MAAM,YAAY,GAAG,OAAO,CAC1B,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAC5D,CAAC,MAAM,EAAE,EAAE,CACT,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,OAAO,CAAC,EAAE,EAAE,CAAC,iBAC7B,IAAI,EAAE,GAAG,IACN,OAAO,EACV,CAAC,EACL,CAAC,CAAC,GAAG,CAAC,EAAE,EAAE;;YACR,MAAM,MAAM,GAAG,MAAA,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,0CAAG,CAAC,CAAC,CAAC;YAC7C,MAAM,UAAU,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC;YACrE,IAAI,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,WAAW;gBAAE,OAAO,GAAG,CAAC;YACxC,OAAO,MAAM,CAAC;QAChB,CAAC,CACF,CAAC;QAEF,yEAAyE;QACzE,MAAM,QAAQ,GAAG,kBAAkB;aAChC,MAAM,CAAC,CAAC,UAAU,EAAE,EAAE;YACrB,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,UAAU,CAAC;YACtC,IAAI,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,IAAI,CAAC;gBAAE,OAAO,IAAI,CAAC;YACxD,IAAI,QAAQ;gBAAE,MAAM,IAAI,KAAK,CAAC,iCAAiC,IAAI,GAAG,CAAC,CAAC;YACxE,OAAO,KAAK,CAAC;QACf,CAAC,CAAC;aACD,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;YACtB,MAAM,EACJ,IAAI,EACJ,KAAK,EAAE,SAAS,EAChB,QAAQ,EACR,KAAK,EACL,YAAY,EACZ,OAAO,GACR,GAAG,UAAU,CAAC;YACf,MAAM,WAAW,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,EAAE,CACpD,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CACzB,CAAC;YACF,OAAO,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC9C,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;gBAC5C,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;gBAChD,MAAM,KAOF,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,EAPlB,EACJ,WAAW,GAAG,KAAK;gBACnB,yCAAyC;gBACzC,GAAG,GAAG,QAAQ,IAAI,OAAO;gBACzB,qCAAqC;gBACrC,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,OAExC,EADnB,YAAY,cANX,8BAOL,CAAuB,CAAC;gBACzB,6CAA6C;gBAC7C,MAAM,UAAU,GAAG,IAAI,KAAK,UAAU,CAAC;gBACvC,MAAM,UAAU,GAAG,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC;gBAClD,OAAO;oBACL,IAAI,EAAE,OAAO;oBACb,MAAM;oBACN,iDAAiD;oBACjD,gDAAgD;oBAChD,QAAQ,EAAE,WAAW,IAAI,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,GAAG;oBACjE,KAAK,gCACH,IAAI,EACJ,KAAK,EAAE,UAAU,IACd,YAAY,KACf,YAAY;wBACZ,OAAO,GACR;iBACF,CAAC;YACJ,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEL,OAAO,CAAC,eAAe,kCAAO,YAAY,KAAE,KAAK,EAAE,CAAC,EAAE,QAAQ,EAAE,OAAO,IAAG,CAAC;IAC7E,CAAC;CAAA;AAED,MAAM,UAAU,cAAc,CAAC,OAAkB;IAC/C,MAAM,CAAC,SAAS,CAAC,GAAG,UAAU,CAC5B,QAAQ,EACR,OAAO,CACR,CAAC;IACF,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE;QACtB,IAAI,MAAM,KAAK,SAAS;YAAE,OAAO,IAAI,CAAC;QACtC,IAAI,IAAI,KAAK,SAAS;YAAE,OAAO,IAAI,CAAC;QACpC,uCACK,MAAM,KACT,IAAI,EAAE,QAAQ,EACd,KAAK,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAC9B,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,IACtB;IACJ,CAAC,CAAC;AACJ,CAAC;AAED,SAAe,kBAAkB,CAC/B,IAAY,EACZ,KAAgB,EAChB,OAAkB;;QAElB,MAAM,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC;QAC5B,MAAM,CAAC,YAAY,CAAC,GAAG,UAAU,CAI/B,WAAW,EAAE,OAAO,CAAC,CAAC;QACxB,MAAM,EAAE,YAAY,GAAG,EAAE,EAAE,aAAa,GAAG,EAAE,EAAE,GAAG,KAAK,CAAC;QACxD,MAAM,EAAE,SAAS,GAAG,EAAE,EAAE,GAAG,IAAI,CAAC;QAChC,MAAM,UAAU,GAAG;YACjB,aAAa;YACb,kBAAkB;YAClB,UAAU;YACV,iBAAiB;YACjB,kBAAkB;YAClB,cAAc;YACd,eAAe;YACf,eAAe;YACf,eAAe;YACf,gBAAgB;YAChB,GAAG,YAAY,CAAC,GAAG,CAAC,YAAY,CAAC;YACjC,GAAG,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC;YAC9B,GAAG,aAAa,CAAC,GAAG,CAAC,YAAY,CAAC;YAClC,cAAc;SACf,CAAC;QACF,IAAI,KAAK,GAAG,EAAE,CAAC;QACf,IAAI,eAAe,GAAG,IAAI,CAAC;QAC3B,KAAK,MAAM,CAAC,IAAI,UAAU,EAAE;YAC1B,CAAC,KAAK,EAAE,eAAe,CAAC,GAAG,MAAM,CAAC,CAAC,KAAK,EAAE,eAAe,EAAE,OAAO,CAAC,CAAC;SACrE;QACD,OAAO,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC;IAClC,CAAC;CAAA;AAED,SAAS,OAAO,CAAC,MAA4B;IAC3C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,CAAC;IAC/B,IAAI,IAAI,KAAK,OAAO,IAAI,OAAO,KAAK,KAAK,QAAQ;QAAE,OAAO,KAAK,CAAC;IAChE,OAAO,IAAI,CAAC;AACd,CAAC"}