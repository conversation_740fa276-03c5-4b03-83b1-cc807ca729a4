{"version": 3, "file": "trail.js", "sourceRoot": "", "sources": ["../../../src/shape/line/trail.ts"], "names": [], "mappings": ";;;;;;;;;;;AAAA,OAAO,EAAE,IAAI,IAAI,MAAM,EAAE,MAAM,sBAAsB,CAAC;AAEtD,OAAO,EAAE,MAAM,EAAE,MAAM,uBAAuB,CAAC;AAC/C,OAAO,EAAE,UAAU,EAAE,MAAM,UAAU,CAAC;AACtC,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAW,MAAM,oBAAoB,CAAC;AAC9D,OAAO,EAAE,OAAO,EAAE,MAAM,oBAAoB,CAAC;AAC7C,OAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAEhC;;;;;;;GAOG;AACH,SAAS,MAAM,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;IAClC,MAAM,CAAC,GAAG,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IACtB,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IACnB,MAAM,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;IAC3B,MAAM,EAAE,GAAY,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;IACvE,MAAM,EAAE,GAAY,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;IACvE,MAAM,EAAE,GAAY,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACrE,MAAM,EAAE,GAAY,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACrE,MAAM,EAAE,GAAG,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IACvB,MAAM,EAAE,GAAG,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IACvB,MAAM,EAAE,GAAG,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IACvB,MAAM,EAAE,GAAG,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IACvB,MAAM,EAAE,GAAG,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IACvB,MAAM,EAAE,GAAG,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IACvB,MAAM,EAAE,GAAG,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IACvB,MAAM,EAAE,GAAG,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IACvB,MAAM,EAAE,GAAG,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IACvB,MAAM,EAAE,GAAG,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAEvB,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC;IACnB,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC;IACnB,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC;IACjC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC;IACjC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC;IACnB,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC;IACjC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC;IACjC,IAAI,CAAC,SAAS,EAAE,CAAC;AACnB,CAAC;AAID,0CAA0C;AAC1C,MAAM,CAAC,MAAM,KAAK,GAAqB,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;IAC1D,MAAM,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC;IAC7B,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE;QAC5B,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,GAAG,KAAK,CAAC;QACpC,MAAM,EAAE,KAAK,EAAE,YAAY,KAAc,QAAQ,EAAjB,IAAI,UAAK,QAAQ,EAA3C,SAAgC,CAAW,CAAC;QAClD,MAAM,IAAI,GAAG,MAAM,EAAE,CAAC;QACtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YACrC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACpB,MAAM,EAAE,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;YACzB,MAAM,EAAE,GAAG,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAC7B,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC;gBAAE,MAAM,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;SACjE;QACD,OAAO,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;aAC9C,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;aACtB,KAAK,CAAC,MAAM,EAAE,KAAK,IAAI,YAAY,CAAC;aACpC,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC;aAC3B,IAAI,CAAC,UAAU,EAAE,OAAO,CAAC;aACzB,IAAI,EAAE,CAAC;IACZ,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF,KAAK,CAAC,KAAK,mCACN,KAAK,CAAC,KAAK,KACd,aAAa,EAAE,MAAM,GACtB,CAAC"}