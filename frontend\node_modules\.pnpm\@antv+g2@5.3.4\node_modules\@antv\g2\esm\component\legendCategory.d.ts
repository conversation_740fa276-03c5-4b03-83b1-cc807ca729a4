import type { FlexLayout, GuideComponentComponent as GCC, GuideComponentOrientation as GCO, GuideComponentPosition as GCP } from '../runtime';
export type LegendCategoryOptions = {
    dx?: number;
    dy?: number;
    labelFormatter?: (d: any) => string;
    layout?: FlexLayout;
    orientation?: GCO;
    position?: GCP;
    title?: string | string[];
    [key: string]: any;
};
/**
 * Guide Component for ordinal color scale.
 */
export declare const LegendCategory: GCC<LegendCategoryOptions>;
