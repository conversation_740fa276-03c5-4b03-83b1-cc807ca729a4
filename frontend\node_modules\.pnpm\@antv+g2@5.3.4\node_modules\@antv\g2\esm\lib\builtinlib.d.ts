import { Event } from '../interaction/event';
export declare function builtinlib(): {
    readonly 'component.axisRadar': import("..").GuideComponentComponent<import("../component/axisRadar").AxisRadarOptions>;
    readonly 'component.axisLinear': import("..").GuideComponentComponent<import("..").AxisOptions>;
    readonly 'component.axisArc': import("..").GuideComponentComponent<import("..").AxisOptions>;
    readonly 'component.legendContinuousBlock': import("..").GuideComponentComponent<import("../component").LegendContinuousOptions>;
    readonly 'component.legendContinuousBlockSize': import("..").GuideComponentComponent<import("../component").LegendContinuousOptions>;
    readonly 'component.legendContinuousSize': import("..").GuideComponentComponent<import("../component").LegendContinuousOptions>;
    readonly 'interaction.event': typeof Event;
    readonly 'composition.mark': import("../runtime").CompositionComponent<import("../composition").MarkOptions>;
    readonly 'composition.view': import("../runtime").CompositionComponent<import("../composition").ViewOptions>;
    readonly 'shape.label.label': import("..").ShapeComponent<import("../shape").LabelShapeOptions>;
};
