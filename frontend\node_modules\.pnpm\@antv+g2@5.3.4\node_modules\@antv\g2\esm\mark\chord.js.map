{"version": 3, "file": "chord.js", "sourceRoot": "", "sources": ["../../src/mark/chord.ts"], "names": [], "mappings": ";;;;;;;;;;;AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AAGrC,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAC5C,OAAO,EAAE,UAAU,EAAE,cAAc,EAAE,MAAM,eAAe,CAAC;AAC3D,OAAO,EAAE,GAAG,EAAE,MAAM,aAAa,CAAC;AAElC,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,MAAM,SAAS,CAAC;AAEhD,MAAM,sBAAsB,GAAe;IACzC,CAAC,EAAE,CAAC;IACJ,SAAS,EAAE,IAAI;IACf,WAAW,EAAE,GAAG;IAChB,EAAE,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG;IACtB,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM;IAC7B,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM;IAC7B,YAAY,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC;IACvC,YAAY,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC;IACvC,MAAM,EAAE,IAAI,EAAE,iDAAiD;CAChE,CAAC;AAEF,MAAM,oBAAoB,GAAG;IAC3B,IAAI,EAAE,SAAS;IACf,IAAI,EAAE,KAAK;IACX,MAAM,EAAE,KAAK;IACb,MAAM,EAAE;QACN,KAAK,EAAE,SAAS;QAChB,CAAC,EAAE,GAAG;QACN,CAAC,EAAE,GAAG;KACP;IACD,KAAK,EAAE;QACL,CAAC,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE;QACvB,CAAC,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE;KACxB;IACD,KAAK,EAAE;QACL,OAAO,EAAE,CAAC;QACV,WAAW,EAAE,CAAC;QACd,SAAS,EAAE,CAAC;KACb;CACF,CAAC;AAEF,MAAM,oBAAoB,GAAG;IAC3B,IAAI,EAAE,SAAS;IACf,IAAI,EAAE,KAAK;IACX,MAAM,EAAE,KAAK;IACb,MAAM,EAAE;QACN,KAAK,EAAE,QAAQ;QACf,CAAC,EAAE,GAAG;QACN,CAAC,EAAE,GAAG;KACP;IACD,KAAK,EAAE;QACL,OAAO,EAAE,GAAG;QACZ,SAAS,EAAE,CAAC;KACb;CACF,CAAC;AAEF,MAAM,qBAAqB,GAAG;IAC5B,QAAQ,EAAE,SAAS;IACnB,QAAQ,EAAE,EAAE;CACb,CAAC;AAIF,MAAM,CAAC,MAAM,KAAK,GAAqB,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;IAC1D,MAAM,EACJ,IAAI,EACJ,MAAM,GAAG,EAAE,EACX,KAAK,EACL,KAAK,GAAG,EAAE,EACV,MAAM,GAAG,EAAE,EACX,UAAU,GAAG,EAAE,EACf,UAAU,GAAG,EAAE,EACf,OAAO,GAAG,EAAE,EACZ,OAAO,GAAG,EAAE,GACb,GAAG,OAAO,CAAC;IAEZ,iEAAiE;IACjE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,cAAc,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IAEtD,oCAAoC;IACpC,MAAM,UAAU,GAAG,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAC7C,MAAM,UAAU,GAAG,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAC7C,MAAM,EAAE,GAAG,EAAE,OAAO,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,GAAG,OAAO,EAAE,GAAG,UAAU,CAAC;IACpE,MAAM,EAAE,eAAe,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,EAAE,GAAG,UAAU,CAAC;IACzD,MAAM,EACJ,cAAc,GAAG,sBAAsB,CAAC,SAAS,EACjD,gBAAgB,GAAG,sBAAsB,CAAC,WAAW,KAEnD,MAAM,EADL,UAAU,UACX,MAAM,EAJJ,sCAIL,CAAS,CAAC;IAEX,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,GAAG,6DAC3C,sBAAsB,KACzB,EAAE,EAAE,KAAK,CAAC,OAAO,CAAC,EAClB,SAAS,EAAE,cAAc,EACzB,WAAW,EAAE,gBAAgB,KAC1B,UAAU,KACb,MAAM,EAAE,IAAI,IACZ,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;IAE5B,0CAA0C;IAC1C,MAAM,KAAoC,SAAS,CAAC,KAAK,EAAE,OAAO,CAAC,EAA7D,EAAE,IAAI,GAAG,OAAO,OAA6C,EAAxC,UAAU,cAA/B,QAAiC,CAA4B,CAAC;IAEpE,MAAM,WAAW,GAAG,UAAU,CAC5B,OAAO,EACP,MAAM,EACN;QACE,KAAK,EAAE,EAAE;QACT,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC;KAClD,EACD,IAAI,CACL,CAAC;IACF,MAAM,WAAW,GAAG,UAAU,CAAC,OAAO,EAAE,MAAM,EAAE;QAC9C,KAAK,EAAE,EAAE;QACT,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,MAAM,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC;KACzE,CAAC,CAAC;IAEH,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;IAElC,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IAE3C,OAAO;QACL,OAAO,CAAC,EAAE,EAAE,oBAAoB,EAAE;YAChC,IAAI,EAAE,QAAQ;YACd,MAAM,kCAAO,UAAU,KAAE,KAAK,EAAE,eAAe,GAAE;YACjD,MAAM,EAAE,UAAU;YAClB,KAAK,kBACH,IAAI,EAAE,eAAe,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,IACvC,SAAS,CAAC,KAAK,EAAE,MAAM,CAAC,CAC5B;YACD,OAAO,EAAE,WAAW;YACpB,OAAO,EAAE,cAAc,CAAC,OAAO,EAAE,MAAM,CAAC;SACzC,CAAC;QACF,OAAO,CAAC,EAAE,EAAE,oBAAoB,EAAE;YAChC,IAAI,EAAE,QAAQ;YACd,MAAM,kCAAO,UAAU,KAAE,KAAK,GAAE;YAChC,KAAK;YACL,KAAK,EAAE,SAAS,CAAC,KAAK,EAAE,MAAM,CAAC;YAC/B,UAAU,EAAE;gBACV,IAAI,EAAE,OAAO;gBACb,8CAA8C;gBAC9C,WAAW,EAAE,CAAC,UAAU,GAAG,EAAE,CAAC,GAAG,UAAU;gBAC3C,UAAU,EAAE,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC;gBACxB,QAAQ,EAAE,CAAC;aACZ;YACD,MAAM,EAAE;8DAED,qBAAqB,KACxB,IAAI,KACD,UAAU;gBAEf,GAAG,UAAU;aACd;YACD,OAAO,EAAE,WAAW;YACpB,OAAO,EAAE,cAAc,CAAC,OAAO,EAAE,MAAM,CAAC;YACxC,IAAI,EAAE,KAAK;SACZ,CAAC;KACc,CAAC;AACrB,CAAC,CAAC;AAEF,KAAK,CAAC,KAAK,GAAG,EAAE,CAAC"}