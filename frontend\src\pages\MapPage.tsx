import React, { useState } from 'react';
import { 
  Card, 
  Radio, 
  Space, 
  Typography, 
  Button, 
  Drawer,
  List,
  Avatar,
  Tag
} from 'antd';
import { 
  GlobalOutlined, 
  EnvironmentOutlined, 
  SoundOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;

const MapPage: React.FC = () => {
  const [mapMode, setMapMode] = useState<'distribution' | 'collection'>('distribution');
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [selectedSpecies, setSelectedSpecies] = useState<any>(null);

  // 模拟数据
  const mockCollectionPoints = [
    {
      id: 'point-1',
      species: '蓝鲸',
      location: '北太平洋',
      latitude: 35.6762,
      longitude: 139.6503,
      audioCount: 15,
      lastRecording: '2023-08-15'
    },
    {
      id: 'point-2',
      species: '座头鲸',
      location: '夏威夷海域',
      latitude: 21.3099,
      longitude: -157.8581,
      audioCount: 23,
      lastRecording: '2023-07-22'
    }
  ];

  const handleModeChange = (e: any) => {
    setMapMode(e.target.value);
  };

  const handlePointClick = (point: any) => {
    setSelectedSpecies(point);
    setDrawerVisible(true);
  };

  return (
    <div style={{ padding: '24px' }}>
      <Card style={{ marginBottom: '24px' }}>
        <div style={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          marginBottom: '16px'
        }}>
          <Title level={2} style={{ margin: 0 }}>
            交互式地图
          </Title>
          <Radio.Group value={mapMode} onChange={handleModeChange} size="large">
            <Radio.Button value="distribution">
              <Space>
                <GlobalOutlined />
                物种分布
              </Space>
            </Radio.Button>
            <Radio.Button value="collection">
              <Space>
                <EnvironmentOutlined />
                采集点
              </Space>
            </Radio.Button>
          </Radio.Group>
        </div>
        
        <Text type="secondary">
          {mapMode === 'distribution' 
            ? '查看海洋生物的地理分布范围，了解它们的栖息地分布情况'
            : '查看声音数据采集点位置，了解数据收集的地理分布'
          }
        </Text>
      </Card>

      {/* 地图容器 */}
      <Card>
        <div style={{
          width: '100%',
          height: '600px',
          background: '#f0f9ff',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          borderRadius: '8px',
          border: '2px dashed #d9d9d9',
          position: 'relative'
        }}>
          <div style={{ textAlign: 'center' }}>
            <GlobalOutlined style={{ fontSize: '48px', color: '#1890ff', marginBottom: '16px' }} />
            <div style={{ fontSize: '18px', color: '#666', marginBottom: '8px' }}>
              交互式地图组件
            </div>
            <Text type="secondary">
              {mapMode === 'distribution' ? '显示物种分布范围' : '显示音频采集点'}
            </Text>
          </div>

          {/* 模拟地图点击区域 */}
          {mapMode === 'collection' && (
            <div style={{ position: 'absolute', top: '20px', left: '20px' }}>
              <Space direction="vertical">
                {mockCollectionPoints.map((point, index) => (
                  <Button
                    key={point.id}
                    type="primary"
                    shape="circle"
                    icon={<EnvironmentOutlined />}
                    style={{
                      position: 'absolute',
                      top: `${100 + index * 80}px`,
                      left: `${200 + index * 150}px`
                    }}
                    onClick={() => handlePointClick(point)}
                  />
                ))}
              </Space>
            </div>
          )}
        </div>

        {/* 地图控制面板 */}
        <div style={{ 
          marginTop: '16px', 
          display: 'flex', 
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <Space>
            <Button icon={<InfoCircleOutlined />}>
              图例说明
            </Button>
            {mapMode === 'collection' && (
              <Text type="secondary">
                点击地图上的标记点查看详细信息
              </Text>
            )}
          </Space>
          
          <Space>
            <Button>重置视图</Button>
            <Button>全屏显示</Button>
          </Space>
        </div>
      </Card>

      {/* 采集点详情抽屉 */}
      <Drawer
        title="采集点详情"
        placement="right"
        onClose={() => setDrawerVisible(false)}
        open={drawerVisible}
        width={400}
      >
        {selectedSpecies && (
          <div>
            <div style={{ marginBottom: '24px' }}>
              <Avatar 
                size={64} 
                style={{ backgroundColor: '#1890ff', marginBottom: '16px' }}
              >
                {selectedSpecies.species.charAt(0)}
              </Avatar>
              <Title level={4}>{selectedSpecies.species}</Title>
              <Text type="secondary">{selectedSpecies.location}</Text>
            </div>

            <Card size="small" style={{ marginBottom: '16px' }}>
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1890ff' }}>
                  {selectedSpecies.audioCount}
                </div>
                <div style={{ color: '#666' }}>音频文件数量</div>
              </div>
            </Card>

            <List
              size="small"
              dataSource={[
                { label: '纬度', value: selectedSpecies.latitude },
                { label: '经度', value: selectedSpecies.longitude },
                { label: '最后录音时间', value: selectedSpecies.lastRecording }
              ]}
              renderItem={(item) => (
                <List.Item>
                  <Text strong>{item.label}:</Text>
                  <Text style={{ marginLeft: '8px' }}>{item.value}</Text>
                </List.Item>
              )}
            />

            <div style={{ marginTop: '24px' }}>
              <Space direction="vertical" style={{ width: '100%' }}>
                <Button type="primary" icon={<SoundOutlined />} block>
                  查看音频文件
                </Button>
                <Button icon={<InfoCircleOutlined />} block>
                  查看物种详情
                </Button>
              </Space>
            </div>
          </div>
        )}
      </Drawer>
    </div>
  );
};

export default MapPage;
