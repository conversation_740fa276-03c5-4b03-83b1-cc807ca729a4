{"version": 3, "file": "image.js", "sourceRoot": "", "sources": ["../../src/mark/image.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,UAAU,EAAE,mBAAmB,EAAE,MAAM,cAAc,CAAC;AAC/D,OAAO,EAAE,UAAU,EAAE,MAAM,UAAU,CAAC;AACtC,OAAO,EACL,oBAAoB,EACpB,iBAAiB,EACjB,gBAAgB,EAChB,gBAAgB,EAChB,SAAS,EACT,UAAU,GACX,MAAM,SAAS,CAAC;AAEjB,MAAM,KAAK,GAAG;IACZ,KAAK,EAAE,UAAU;CAClB,CAAC;AAIF,MAAM,CAAC,MAAM,KAAK,GAAqB,CAAC,OAAO,EAAE,EAAE;IACjD,MAAM,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC;IAC9B,IAAI,SAAS;QAAE,OAAO,UAAkB,CAAC;IACzC,OAAO,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,EAAE;QACzC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC;QAC7B,MAAM,MAAM,GAAG,gBAAgB,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QACvD,MAAM,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,EAAE;YAChC,MAAM,CAAC,GAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAc,CAAC;QACrD,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF,KAAK,CAAC,KAAK,GAAG;IACZ,YAAY,EAAE,OAAO;IACrB,iBAAiB,EAAE,OAAO;IAC1B,SAAS,EAAE,KAAK;IAChB,KAAK;IACL,QAAQ,EAAE;QACR,GAAG,oBAAoB,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;QACvD,EAAE,IAAI,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE;QAC7B,EAAE,IAAI,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE;QAC7B,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE;QAClC,EAAE,IAAI,EAAE,MAAM,EAAE;KACjB;IACD,YAAY,EAAE;QACZ,GAAG,gBAAgB,EAAE;QACrB,EAAE,IAAI,EAAE,UAAU,EAAE;QACpB,EAAE,IAAI,EAAE,mBAAmB,EAAE;KAC9B;IACD,aAAa,EAAE,CAAC,GAAG,iBAAiB,EAAE,EAAE,GAAG,SAAS,EAAE,CAAC;CACxD,CAAC"}