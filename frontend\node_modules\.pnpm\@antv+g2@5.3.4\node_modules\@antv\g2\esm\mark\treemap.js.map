{"version": 3, "file": "treemap.js", "sourceRoot": "", "sources": ["../../src/mark/treemap.ts"], "names": [], "mappings": ";;;;;;;;;;;AAAA,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,YAAY,CAAC;AAChD,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAG5C,OAAO,EAAE,YAAY,EAAE,MAAM,eAAe,CAAC;AAC7C,OAAO,EAAE,iBAAiB,EAAE,MAAM,4BAA4B,CAAC;AAI/D,WAAW;AACX,MAAM,0BAA0B,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;IACrD,IAAI,EAAE,iBAAiB;IACvB,KAAK,EAAE,GAAG,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAC/B,IAAI,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC;IACrB,KAAK,EAAE,KAAK;IACZ,iBAAiB,EAAE,IAAI;IACvB,OAAO,EAAE,CAAC;IACV,YAAY,EAAE,CAAC;IACf,YAAY,EAAE,CAAC;IACf,UAAU,EAAE,CAAC;IACb,YAAY,EAAE,CAAC;IACf,aAAa,EAAE,CAAC;IAChB,WAAW,EAAE,CAAC;IACd,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK;IACjC,KAAK,EAAE,CAAC;CACT,CAAC,CAAC;AAEH,MAAM,mBAAmB,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;IAC9C,IAAI,EAAE,MAAM;IACZ,IAAI,EAAE,KAAK;IACX,MAAM,EAAE;QACN,CAAC,EAAE,GAAG;QACN,CAAC,EAAE,GAAG;QACN,GAAG,EAAE,IAAI;QACT,KAAK,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;KACxB;IACD,KAAK,EAAE;QACL,CAAC,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QACxC,CAAC,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;KAC1C;IACD,KAAK,EAAE;QACL,MAAM,EAAE,MAAM;KACf;IACD,KAAK,EAAE;QACL,MAAM,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE;QACxB,QAAQ,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE;KACzB;CACF,CAAC,CAAC;AAEH,MAAM,qBAAqB,GAAG;IAC5B,QAAQ,EAAE,EAAE;IACZ,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;IACzB,QAAQ,EAAE,QAAQ;IAClB,IAAI,EAAE,MAAM;IACZ,YAAY,EAAE,MAAM;IACpB,QAAQ,EAAE,IAAI;IACd,QAAQ,EAAE,CAAC;IACX,aAAa,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IACjC,cAAc,EAAE,IAAI;CACrB,CAAC;AAEF,MAAM,uBAAuB,GAAG;IAC9B,KAAK,EAAE,CAAC,CAAC,EAAE,EAAE,eAAC,OAAA,MAAA,MAAA,CAAC,CAAC,IAAI,0CAAE,IAAI,mDAAG,GAAG,CAAC,CAAA,EAAA;IACjC,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC;CAC5B,CAAC;AAEF,MAAM,6BAA6B,GAAG;IACpC,KAAK,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;IAC1B,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC;CAC5B,CAAC;AAEF,MAAM,CAAC,MAAM,OAAO,GAAuB,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;IAC9D,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC;IAExD,MAAM,EACJ,IAAI,EACJ,MAAM,GAAG,EAAE,EACX,KAAK,EACL,KAAK,GAAG,EAAE,EACV,MAAM,GAAG,EAAE,EACX,MAAM,GAAG,EAAE,EACX,OAAO,GAAG,EAAE,KAEV,OAAO,EADN,UAAU,UACX,OAAO,EATL,mEASL,CAAU,CAAC;IAEZ,MAAM,gBAAgB,GACpB,GAAG,CAAC,WAAW,EAAE,CAAC,aAAa,EAAE,kBAAkB,CAAC,CAAC;QACrD,GAAG,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,CAAC,EAAE,aAAa,EAAE,kBAAkB,CAAC,CAAC,CAAC;IAEpE,SAAS;IACT,MAAM,aAAa,GAAG,OAAO,CAC3B,EAAE,EACF,0BAA0B,CAAC,KAAK,EAAE,MAAM,CAAC,EACzC,MAAM,EACN;QACE,KAAK,EAAE,gBAAgB;YACrB,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;gBACJ,OAAO,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC;YACvB,CAAC;YACH,CAAC,CAAC,MAAM,CAAC,KAAK;KACjB,CACF,CAAC;IAEF,OAAO;IACP,MAAM,CAAC,eAAe,EAAE,kBAAkB,CAAC,GAAG,iBAAiB,CAC7D,IAAI,EACJ,aAAa,EACb,MAAM,CACP,CAAC;IAEF,QAAQ;IACR,MAAM,UAAU,GAAG,SAAS,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IAE7C,OAAO,OAAO,CACZ,EAAE,EACF,mBAAmB,CAAC,KAAK,EAAE,MAAM,CAAC,gCAEhC,IAAI,EAAE,eAAe,EACrB,KAAK;QACL,KAAK,EACL,MAAM,EAAE;0DAED,qBAAqB,GACrB,UAAU,GACV,CAAC,gBAAgB,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;YAEhD,GAAG,MAAM;SACV,IACE,UAAU,KACb,MAAM,EACN,OAAO,EAAE,YAAY,CAAC,OAAO,EAAE,uBAAuB,CAAC,EACvD,IAAI,EAAE,KAAK,KAEb,gBAAgB;QACd,CAAC,CAAC;YACE,WAAW,kCACN,UAAU,CAAC,WAAW,KACzB,gBAAgB,EAAE,gBAAgB;oBAChC,CAAC,iCACM,gBAAgB,KACnB,UAAU,EAAE,kBAAkB,EAC9B,MAAM,EAAE,aAAa,IAEzB,CAAC,CAAC,SAAS,GACd;YACD,MAAM,kBACJ,KAAK,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IACvB,MAAM,CACV;YACD,OAAO,EAAE,YAAY,CAAC,OAAO,EAAE,6BAA6B,CAAC;SAC9D;QACH,CAAC,CAAC,EAAE,CACP,CAAC;AACJ,CAAC,CAAC;AAEF,OAAO,CAAC,KAAK,GAAG,EAAE,CAAC"}