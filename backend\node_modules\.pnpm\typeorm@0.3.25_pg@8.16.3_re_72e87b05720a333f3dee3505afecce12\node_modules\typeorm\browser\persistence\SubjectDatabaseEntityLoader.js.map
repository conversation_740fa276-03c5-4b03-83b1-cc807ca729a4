{"version": 3, "sources": ["../browser/src/persistence/SubjectDatabaseEntityLoader.ts"], "names": [], "mappings": "AAKA,OAAO,EAAE,QAAQ,EAAE,MAAM,kBAAkB,CAAA;AAE3C;;;;;GAKG;AACH,MAAM,OAAO,2BAA2B;IACpC,wEAAwE;IACxE,cAAc;IACd,wEAAwE;IAExE,YACc,WAAwB,EACxB,QAAmB;QADnB,gBAAW,GAAX,WAAW,CAAa;QACxB,aAAQ,GAAR,QAAQ,CAAW;IAC9B,CAAC;IAEJ,wEAAwE;IACxE,iBAAiB;IACjB,wEAAwE;IAExE;;;;;OAKG;IACH,KAAK,CAAC,IAAI,CACN,aAA4D;QAE5D,+FAA+F;QAC/F,8FAA8F;QAC9F,MAAM,QAAQ,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC,GAAG,CAC5C,KAAK,EAAE,YAAY,EAAE,EAAE;YACnB,qDAAqD;YACrD,MAAM,MAAM,GAAoB,EAAE,CAAA;YAClC,MAAM,WAAW,GAAc,EAAE,CAAA;YACjC,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;gBACtC,gEAAgE;gBAChE,IAAI,OAAO,CAAC,cAAc,IAAI,CAAC,OAAO,CAAC,UAAU;oBAAE,OAAM;gBAEzD,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAA;gBAC/B,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YAC7B,CAAC,CAAC,CAAA;YAEF,yGAAyG;YACzG,IAAI,CAAC,MAAM,CAAC,MAAM;gBAAE,OAAM;YAE1B,MAAM,yBAAyB,GAAa,EAAE,CAAA;YAE9C,kDAAkD;YAClD,+EAA+E;YAC/E,sFAAsF;YACtF,sFAAsF;YACtF,6EAA6E;YAC7E,IACI,aAAa,KAAK,MAAM;gBACxB,aAAa,KAAK,aAAa;gBAC/B,aAAa,KAAK,SAAS,EAC7B,CAAC;gBACC,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;oBACtC,uEAAuE;oBACvE,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;wBAC5C,MAAM,KAAK,GAAG,QAAQ,CAAC,cAAc,CACjC,OAAO,CAAC,sBAAuB,CAClC,CAAA;wBACD,IAAI,KAAK,KAAK,SAAS;4BAAE,OAAM;wBAE/B,IACI,yBAAyB,CAAC,OAAO,CAC7B,QAAQ,CAAC,YAAY,CACxB,KAAK,CAAC,CAAC;4BAER,yBAAyB,CAAC,IAAI,CAC1B,QAAQ,CAAC,YAAY,CACxB,CAAA;oBACT,CAAC,CAAC,CAAA;gBACN,CAAC,CAAC,CAAA;YACN,CAAC;iBAAM,CAAC;gBACJ,SAAS;gBAET,uBAAuB;gBACvB,qFAAqF;gBACrF,yBAAyB,CAAC,IAAI,CAC1B,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,mBAAmB,CAAC,GAAG,CACxD,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,YAAY,CACtC,CACJ,CAAA;YACL,CAAC;YAED,MAAM,WAAW,GAAyB;gBACtC,kBAAkB,EAAE,KAAK;gBACzB,eAAe,EAAE;oBACb,SAAS,EAAE,yBAAyB;oBACpC,eAAe,EAAE,IAAI;iBACxB;gBACD,4FAA4F;gBAC5F,WAAW,EAAE,IAAI;aACpB,CAAA;YAED,2CAA2C;YAC3C,IAAI,QAAQ,GAAU,EAAE,CAAA;YACxB,IACI,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI;gBAC/C,SAAS,EACX,CAAC;gBACC,MAAM,SAAS,GACX,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,aAAa,CAClC,YAAY,CAAC,MAAM,CACY,CAAA;gBACvC,QAAQ,GAAG,MAAM,SAAS,CAAC,SAAS,CAAC,MAAM,EAAE,WAAW,CAAC,CAAA;YAC7D,CAAC;iBAAM,CAAC;gBACJ,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO;qBACpC,aAAa,CAAgB,YAAY,CAAC,MAAM,CAAC;qBACjD,kBAAkB,EAAE;qBACpB,cAAc,CAAC,WAAW,CAAC;qBAC3B,UAAU,CAAC,MAAM,CAAC;qBAClB,OAAO,EAAE,CAAA;YAClB,CAAC;YAED,mEAAmE;YACnE,qEAAqE;YACrE,gFAAgF;YAChF,6FAA6F;YAC7F,QAAQ,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;gBACxB,MAAM,QAAQ,GACV,WAAW,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;gBAClD,WAAW,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;oBAC5B,IAAI,OAAO,CAAC,cAAc;wBAAE,OAAM;oBAClC,IAAI,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,EAAE,QAAQ,CAAC;wBACjD,OAAO,CAAC,cAAc,GAAG,MAAM,CAAA;gBACvC,CAAC,CAAC,CAAA;YACN,CAAC,CAAC,CAAA;YAEF,uEAAuE;YACvE,KAAK,MAAM,OAAO,IAAI,WAAW,EAAE,CAAC;gBAChC,OAAO,CAAC,oBAAoB,GAAG,IAAI,CAAA;YACvC,CAAC;QACL,CAAC,CACJ,CAAA;QAED,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;IAC/B,CAAC;IAED,wEAAwE;IACxE,oBAAoB;IACpB,wEAAwE;IAExE;;OAEG;IACO,oBAAoB;QAI1B,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,cAAc,EAAE,EAAE;YACnD,IAAI,KAAK,GAAG,MAAM,CAAC,IAAI,CACnB,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,KAAK,cAAc,CAAC,QAAQ,CAAC,MAAM,CAC7D,CAAA;YACD,IAAI,CAAC,KAAK,EAAE,CAAC;gBACT,KAAK,GAAG,EAAE,MAAM,EAAE,cAAc,CAAC,QAAQ,CAAC,MAAM,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAA;gBAChE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YACtB,CAAC;YACD,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;YACnC,OAAO,MAAM,CAAA;QACjB,CAAC,EAAE,EAA0D,CAAC,CAAA;IAClE,CAAC;CACJ", "file": "SubjectDatabaseEntityLoader.js", "sourcesContent": ["import { Subject } from \"./Subject\"\nimport { ObjectLiteral } from \"../common/ObjectLiteral\"\nimport { QueryRunner } from \"../query-runner/QueryRunner\"\nimport { FindManyOptions } from \"../find-options/FindManyOptions\"\nimport { MongoRepository } from \"../repository/MongoRepository\"\nimport { OrmUtils } from \"../util/OrmUtils\"\n\n/**\n * Loads database entities for all operate subjects which do not have database entity set.\n * All entities that we load database entities for are marked as updated or inserted.\n * To understand which of them really needs to be inserted or updated we need to load\n * their original representations from the database.\n */\nexport class SubjectDatabaseEntityLoader {\n    // ---------------------------------------------------------------------\n    // Constructor\n    // ---------------------------------------------------------------------\n\n    constructor(\n        protected queryRunner: QueryRunner,\n        protected subjects: Subject[],\n    ) {}\n\n    // ---------------------------------------------------------------------\n    // Public Methods\n    // ---------------------------------------------------------------------\n\n    /**\n     * Loads database entities for all subjects.\n     *\n     * loadAllRelations flag is used to load all relation ids of the object, no matter if they present in subject entity or not.\n     * This option is used for deletion.\n     */\n    async load(\n        operationType: \"save\" | \"remove\" | \"soft-remove\" | \"recover\",\n    ): Promise<void> {\n        // we are grouping subjects by target to perform more optimized queries using WHERE IN operator\n        // go through the groups and perform loading of database entities of each subject in the group\n        const promises = this.groupByEntityTargets().map(\n            async (subjectGroup) => {\n                // prepare entity ids of the subjects we need to load\n                const allIds: ObjectLiteral[] = []\n                const allSubjects: Subject[] = []\n                subjectGroup.subjects.forEach((subject) => {\n                    // we don't load if subject already has a database entity loaded\n                    if (subject.databaseEntity || !subject.identifier) return\n\n                    allIds.push(subject.identifier)\n                    allSubjects.push(subject)\n                })\n\n                // if there no ids found (means all entities are new and have generated ids) - then nothing to load there\n                if (!allIds.length) return\n\n                const loadRelationPropertyPaths: string[] = []\n\n                // for the save, soft-remove and recover operation\n                // extract all property paths of the relations we need to load relation ids for\n                // this is for optimization purpose - this way we don't load relation ids for entities\n                // whose relations are undefined, and since they are undefined its really pointless to\n                // load something for them, since undefined properties are skipped by the orm\n                if (\n                    operationType === \"save\" ||\n                    operationType === \"soft-remove\" ||\n                    operationType === \"recover\"\n                ) {\n                    subjectGroup.subjects.forEach((subject) => {\n                        // gets all relation property paths that exist in the persisted entity.\n                        subject.metadata.relations.forEach((relation) => {\n                            const value = relation.getEntityValue(\n                                subject.entityWithFulfilledIds!,\n                            )\n                            if (value === undefined) return\n\n                            if (\n                                loadRelationPropertyPaths.indexOf(\n                                    relation.propertyPath,\n                                ) === -1\n                            )\n                                loadRelationPropertyPaths.push(\n                                    relation.propertyPath,\n                                )\n                        })\n                    })\n                } else {\n                    // remove\n\n                    // for remove operation\n                    // we only need to load junction relation ids since only they are removed by cascades\n                    loadRelationPropertyPaths.push(\n                        ...subjectGroup.subjects[0].metadata.manyToManyRelations.map(\n                            (relation) => relation.propertyPath,\n                        ),\n                    )\n                }\n\n                const findOptions: FindManyOptions<any> = {\n                    loadEagerRelations: false,\n                    loadRelationIds: {\n                        relations: loadRelationPropertyPaths,\n                        disableMixedMap: true,\n                    },\n                    // the soft-deleted entities should be included in the loaded entities for recover operation\n                    withDeleted: true,\n                }\n\n                // load database entities for all given ids\n                let entities: any[] = []\n                if (\n                    this.queryRunner.connection.driver.options.type ===\n                    \"mongodb\"\n                ) {\n                    const mongoRepo =\n                        this.queryRunner.manager.getRepository<ObjectLiteral>(\n                            subjectGroup.target,\n                        ) as MongoRepository<ObjectLiteral>\n                    entities = await mongoRepo.findByIds(allIds, findOptions)\n                } else {\n                    entities = await this.queryRunner.manager\n                        .getRepository<ObjectLiteral>(subjectGroup.target)\n                        .createQueryBuilder()\n                        .setFindOptions(findOptions)\n                        .whereInIds(allIds)\n                        .getMany()\n                }\n\n                // Now when we have entities we need to find subject of each entity\n                // and insert that entity into database entity of the found subjects.\n                // A single entity can be applied to many subjects as there might be duplicates.\n                // This will likely result in the same row being updated multiple times during a transaction.\n                entities.forEach((entity) => {\n                    const entityId =\n                        allSubjects[0].metadata.getEntityIdMap(entity)\n                    allSubjects.forEach((subject) => {\n                        if (subject.databaseEntity) return\n                        if (OrmUtils.compareIds(subject.identifier, entityId))\n                            subject.databaseEntity = entity\n                    })\n                })\n\n                // this way we tell what subjects we tried to load database entities of\n                for (const subject of allSubjects) {\n                    subject.databaseEntityLoaded = true\n                }\n            },\n        )\n\n        await Promise.all(promises)\n    }\n\n    // ---------------------------------------------------------------------\n    // Protected Methods\n    // ---------------------------------------------------------------------\n\n    /**\n     * Groups given Subject objects into groups separated by entity targets.\n     */\n    protected groupByEntityTargets(): {\n        target: Function | string\n        subjects: Subject[]\n    }[] {\n        return this.subjects.reduce((groups, operatedEntity) => {\n            let group = groups.find(\n                (group) => group.target === operatedEntity.metadata.target,\n            )\n            if (!group) {\n                group = { target: operatedEntity.metadata.target, subjects: [] }\n                groups.push(group)\n            }\n            group.subjects.push(operatedEntity)\n            return groups\n        }, [] as { target: Function | string; subjects: Subject[] }[])\n    }\n}\n"], "sourceRoot": ".."}