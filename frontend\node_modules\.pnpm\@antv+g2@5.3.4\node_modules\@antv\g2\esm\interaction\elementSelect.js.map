{"version": 3, "file": "elementSelect.js", "sourceRoot": "", "sources": ["../../src/interaction/elementSelect.ts"], "names": [], "mappings": ";;;;;;;;;;;AACA,OAAO,EAAE,KAAK,EAAE,MAAM,uBAAuB,CAAC;AAC9C,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AACrC,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAC5C,OAAO,EAAE,gBAAgB,EAAE,MAAM,4BAA4B,CAAC;AAC9D,OAAO,EACL,aAAa,EACb,aAAa,EACb,gBAAgB,EAChB,UAAU,EACV,gBAAgB,EAChB,cAAc,EACd,eAAe,EACf,UAAU,EACV,mBAAmB,EACnB,UAAU,EACV,wBAAwB,EACxB,qBAAqB,EACrB,cAAc,GACf,MAAM,SAAS,CAAC;AAEjB;;GAEG;AACH,MAAM,UAAU,aAAa,CAC3B,IAAmB,EACnB,EACE,QAAQ,EAAE,UAAU,EAAE,6DAA6D;AACnF,KAAK,EAAE,6CAA6C;AACpD,QAAQ,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,kCAAkC;AACvD,cAAc,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,0CAA0C;AACrE,IAAI,GAAG,KAAK,EAAE,mBAAmB;AACjC,MAAM,GAAG,KAAK,EAAE,uBAAuB;AACvC,oBAAoB,EAAE,+BAA+B;AACrD,UAAU,EACV,UAAU,GAAG,KAAK,EAClB,KAAK,EACL,OAAO,EACP,KAAK,GAAG,EAAE,EACV,MAAM,GAAG,KAAK,EACd,eAAe,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,GACjD;;IAEtB,MAAM,QAAQ,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;IAClC,MAAM,UAAU,GAAuB,IAAI,GAAG,CAAC,QAAQ,CAAC,CAAC;IACzD,MAAM,WAAW,GAAG,wBAAwB,CAAC;QAC3C,UAAU;QACV,IAAI;QACJ,UAAU;QACV,KAAK;KACN,CAAC,CAAC;IACH,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAC3C,MAAM,WAAW,GAAG,KAAK,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;IAEpD,MAAM,OAAO,GAAG,aAAa,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;IAE/C,MAAM,CAAC,UAAU,EAAE,UAAU,CAAC,GAAG,UAAU,iBACzC,IAAI;QACJ,QAAQ;QACR,OAAO;QACP,UAAU,IACP,SAAS,CAAC,KAAK,CAAC,QAAQ,EAAE,MAAM,CAAC,EACpC,CAAC;IAEH,MAAM,CAAC,gBAAgB,EAAE,gBAAgB,CAAC,GAAG,gBAAgB,iBAC3D,QAAQ,EAAE,IAAI,CAAC,aAAa,EAC5B,UAAU;QACV,UAAU;QACV,KAAK;QACL,OAAO,IACJ,SAAS,CAAC,KAAK,CAAC,QAAQ,EAAE,YAAY,CAAC,EAC1C,CAAC;IAEH,MAAM,YAAY,GAAG,OAAO,CAAC,KAAK,EAAE;QAClC,QAAQ,oBACH,CAAC,CAAA,MAAA,KAAK,CAAC,QAAQ,0CAAE,MAAM,KAAI;YAC5B,qCAAqC;YACrC,SAAS,EAAE,CAAC,GAAG,MAAM,EAAE,EAAE;gBACvB,MAAM,KAAK,GAAG,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;gBAC/C,MAAM,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC;gBACrB,OAAO,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;YACzD,CAAC;SACF,CAAC,CACH;KACF,CAAC,CAAC;IAEH,MAAM,QAAQ,GAAG,cAAc,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;IAExD,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,QAAQ,EAAE,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC;IACjE,IAAI,iBAAiB,GAAG,CAAC,MAAM,CAAC,CAAC,yDAAyD;IAC1F,IAAI,YAAY,GAAG,IAAI,CAAC,CAAC,oCAAoC;IAE7D,MAAM,KAAK,GAAG,CAAC,WAAW,GAAG,IAAI,EAAE,EAAE;QACnC,KAAK,MAAM,CAAC,IAAI,QAAQ,EAAE;YACxB,WAAW,CAAC,CAAC,EAAE,UAAU,EAAE,YAAY,CAAC,CAAC;YACzC,UAAU,CAAC,CAAC,CAAC,CAAC;YACd,gBAAgB,CAAC,CAAC,CAAC,CAAC;SACrB;QACD,IAAI,WAAW;YAAE,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC;QACzE,OAAO;IACT,CAAC,CAAC;IAEF,MAAM,YAAY,GAAG,CAAC,EACpB,KAAK,EACL,OAAO,EACP,WAAW,GAAG,IAAI,EAClB,MAAM,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,IAAI,EACrB,OAAO,GAAG,QAAQ,EAClB,QAAQ,GAAG,QAAQ,GACpB,EAAE,EAAE;QACH,MAAM,gBAAgB,GAAG,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QACjD,4CAA4C;QAC5C,IAAI,QAAQ,CAAC,OAAO,EAAE,UAAU,CAAC;YAAE,KAAK,EAAE,CAAC;aACtC;YACH,MAAM,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;YAC3B,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC9B,MAAM,QAAQ,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC;YAChC,KAAK,MAAM,CAAC,IAAI,gBAAgB,EAAE;gBAChC,IAAI,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;oBAAE,WAAW,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;qBAC3C;oBACH,WAAW,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;oBAC7B,UAAU,CAAC,CAAC,CAAC,CAAC;iBACf;gBACD,IAAI,CAAC,KAAK,OAAO;oBAAE,gBAAgB,CAAC,CAAC,CAAC,CAAC;aACxC;YACD,UAAU,CAAC,KAAK,CAAC,CAAC;YAClB,gBAAgB,CAAC,OAAO,CAAC,CAAC;YAE1B,IAAI,CAAC,WAAW;gBAAE,OAAO;YACzB,OAAO,CAAC,IAAI,CAAC,gBAAgB,kCACxB,KAAK,KACR,WAAW,EACX,IAAI,EAAE;oBACJ,IAAI,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,GAAG,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;iBAC5C,IACD,CAAC;SACJ;IACH,CAAC,CAAC;IAEF,MAAM,cAAc,GAAG,CAAC,EACtB,KAAK,EACL,OAAO,EACP,WAAW,GAAG,IAAI,EAClB,MAAM,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,IAAI,EACrB,OAAO,GAAG,QAAQ,EAClB,QAAQ,GAAG,QAAQ,GACpB,EAAE,EAAE;QACH,MAAM,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;QAC3B,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC9B,MAAM,QAAQ,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC;QAChC,MAAM,gBAAgB,GAAG,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAEjD,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,UAAU,CAAC,EAAE;YAClC,MAAM,gBAAgB,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC;YACpE,KAAK,MAAM,CAAC,IAAI,gBAAgB,EAAE;gBAChC,IAAI,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;oBAAE,WAAW,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;qBAC3C,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,UAAU,CAAC;oBAAE,WAAW,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;aACjE;YACD,wCAAwC;YACxC,IAAI,CAAC,gBAAgB,IAAI,IAAI;gBAAE,UAAU,CAAC,KAAK,CAAC,CAAC;YACjD,gBAAgB,CAAC,OAAO,CAAC,CAAC;SAC3B;aAAM;YACL,+DAA+D;YAC/D,oBAAoB;YACpB,MAAM,WAAW,GAAG,QAAQ,CAAC,IAAI,CAC/B,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,EAAE,UAAU,CAAC,CACnD,CAAC;YACF,IAAI,CAAC,WAAW;gBAAE,OAAO,KAAK,EAAE,CAAC;YACjC,wEAAwE;YACxE,wBAAwB;YACxB,KAAK,MAAM,CAAC,IAAI,KAAK,EAAE;gBACrB,WAAW,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;gBAC7B,UAAU,CAAC,CAAC,CAAC,CAAC;gBACd,gBAAgB,CAAC,CAAC,CAAC,CAAC;aACrB;SACF;QACD,IAAI,CAAC,WAAW;YAAE,OAAO;QACzB,OAAO,CAAC,IAAI,CAAC,gBAAgB,kCACxB,KAAK,KACR,WAAW,EACX,IAAI,EAAE;gBACJ,IAAI,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC;aACjE,IACD,CAAC;IACL,CAAC,CAAC;IAEF,MAAM,qBAAqB,GAAG,CAAC,OAAsB,EAAW,EAAE;QAChE,IAAI,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC;YAAE,OAAO,IAAI,CAAC;QAEzC,KAAK,MAAM,KAAK,IAAI,UAAU,EAAE;YAC9B,MAAM,KAAK,GAAG,gBAAgB,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,KAAK,OAAO,CAAC,CAAC;YAC9D,IAAI,KAAK;gBAAE,OAAO,IAAI,CAAC;SACxB;QAED,OAAO,KAAK,CAAC;IACf,CAAC,CAAC;IAEF,MAAM,cAAc,GAAG,CAAC,OAAsB,EAAiB,EAAE;QAC/D,IAAI,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC;YAAE,OAAO,OAAO,CAAC;QAC5C,KAAK,MAAM,KAAK,IAAI,UAAU,EAAE;YAC9B,IAAI,KAAK,GAAyB,IAAI,CAAC;YACvC,gBAAgB,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE,EAAE;gBAC7B,IAAI,EAAE,KAAK,OAAO;oBAAE,KAAK,GAAG,KAAK,CAAC;YACpC,CAAC,CAAC,CAAC;YACH,IAAI,KAAK;gBAAE,OAAO,KAAK,CAAC;SACzB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC,CAAC;IAEF,MAAM,KAAK,GAAG,CAAC,KAAK,EAAE,EAAE;QACtB,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,GAAG,IAAI,EAAE,GAAG,KAAK,CAAC;QAEtD,MAAM,MAAM,GAAG,CAAC,iBAAiB,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,cAAc,CAAC;QAClE,IAAI,EAAE,GAAG,OAAO,CAAC;QACjB,MAAM,cAAc,GAAG,qBAAqB,CAAC,OAAO,CAAC,CAAC;QAEtD,IAAI,CAAC,MAAM,IAAI,cAAc,EAAE;YAC7B,kCAAkC;YAClC,gDAAgD;YAChD,IAAI,CAAC,cAAc;gBAAE,OAAO,KAAK,EAAE,CAAC;YACpC,OAAO,MAAM,CAAC;gBACZ,KAAK;gBACL,OAAO,EAAE,cAAc,CAAC,EAAE,CAAC;gBAC3B,WAAW;gBACX,OAAO,EAAE,QAAQ;aAClB,CAAC,CAAC;SACJ;aAAM;YACL,+DAA+D;YAC/D,oCAAoC;YACpC,EAAE,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;YAExB,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;gBAAE,OAAO,KAAK,EAAE,CAAC;YAExC,OAAO,MAAM,CAAC;gBACZ,KAAK;gBACL,OAAO,EAAE,EAAE;gBACX,WAAW;gBACX,MAAM,EAAE,eAAe;gBACvB,OAAO,EAAE,cAAc;gBACvB,QAAQ,EAAE,WAAW;aACtB,CAAC,CAAC;SACJ;IACH,CAAC,CAAC;IAEF,+CAA+C;IAC/C,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,oBAAoB,CAAC;QACjD,CAAC,CAAC,oBAAoB;QACtB,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC;IAC3B,MAAM,aAAa,GAAG,CAAC,KAAK,EAAE,EAAE;QAC9B,IAAI,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACjD,YAAY,GAAG,KAAK,CAAC,IAAI,CAAC;YAC1B,iBAAiB,GAAG,IAAI,CAAC;SAC1B;IACH,CAAC,CAAC;IACF,MAAM,WAAW,GAAG,CAAC,KAAK,EAAE,EAAE;QAC5B,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,EAAE;YAC/B,YAAY,GAAG,IAAI,CAAC;YACpB,iBAAiB,GAAG,KAAK,CAAC;SAC3B;IACH,CAAC,CAAC;IAEF,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;IACtC,IAAI,oBAAoB,EAAE;QACxB,8DAA8D;QAC9D,iBAAiB,GAAG,KAAK,CAAC;QAC1B,QAAQ,CAAC,gBAAgB,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;QACpD,QAAQ,CAAC,gBAAgB,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;KACjD;IAED,MAAM,QAAQ,GAAG,CAAC,CAAC,EAAE,EAAE;QACrB,MAAM,EAAE,WAAW,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;QAChC,IAAI,WAAW;YAAE,OAAO;QACxB,MAAM,YAAY,GAAG,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;QAC5E,KAAK,MAAM,CAAC,IAAI,YAAY,EAAE;YAC5B,MAAM,OAAO,GAAG,mBAAmB,CAAC,QAAQ,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;YACxD,KAAK,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,CAAC,CAAC;SAChD;IACH,CAAC,CAAC;IAEF,MAAM,UAAU,GAAG,GAAG,EAAE;QACtB,KAAK,CAAC,KAAK,CAAC,CAAC;IACf,CAAC,CAAC;IAEF,OAAO,CAAC,EAAE,CAAC,gBAAgB,EAAE,QAAQ,CAAC,CAAC;IACvC,OAAO,CAAC,EAAE,CAAC,kBAAkB,EAAE,UAAU,CAAC,CAAC;IAE3C,OAAO,GAAG,EAAE;QACV,KAAK,MAAM,CAAC,IAAI,QAAQ;YAAE,UAAU,CAAC,CAAC,CAAC,CAAC;QACxC,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACzC,IAAI,oBAAoB,EAAE;YACxB,QAAQ,CAAC,mBAAmB,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;YACvD,QAAQ,CAAC,mBAAmB,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;SACpD;QACD,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,QAAQ,CAAC,CAAC;QACxC,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE,UAAU,CAAC,CAAC;IAC9C,CAAC,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,aAAa,CAAC,EAM7B;QAN6B,EAC5B,WAAW,EACX,iBAAiB,EACjB,UAAU,GAAG,KAAK,EAClB,IAAI,GAAG,KAAK,OAEb,EADI,IAAI,cALqB,0DAM7B,CADQ;IAEP,OAAO,CAAC,OAAO,EAAE,CAAC,EAAE,OAAO,EAAE,EAAE;QAC7B,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC;QAC7C,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC;QACnC,MAAM,QAAQ,GAAG,cAAc,CAAC,SAAS,CAAC,CAAC;QAC3C,OAAO,aAAa,CAAC,QAAQ,kBAC3B,QAAQ,EAAE,gBAAgB,EAC1B,KAAK,EAAE,aAAa,CAAC,IAAI,CAAC,EAC1B,QAAQ,EAAE,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,EACrD,cAAc,EAAE,iBAAiB;gBAC/B,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC;gBACzB,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,EACpB,UAAU;YACV,KAAK,EACL,KAAK,EAAE,UAAU,CAAC,OAAO,EAAE;gBACzB,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,SAAS,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;gBAClE,YAAY;aACb,CAAC,EACF,UAAU;YACV,IAAI;YACJ,OAAO,IACJ,IAAI,EACP,CAAC;IACL,CAAC,CAAC;AACJ,CAAC;AAED,aAAa,CAAC,KAAK,GAAG;IACpB,iBAAiB,EAAE,IAAI;CACxB,CAAC"}