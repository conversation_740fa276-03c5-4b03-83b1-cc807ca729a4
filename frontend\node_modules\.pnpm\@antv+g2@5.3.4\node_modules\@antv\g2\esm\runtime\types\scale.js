export const ContinuousScale = {
    linear: 'linear',
    identity: 'identity',
    log: 'log',
    pow: 'pow',
    sqrt: 'sqrt',
    sequential: 'sequential',
};
export const DistributionScale = {
    threshold: 'threshold',
    quantize: 'quantize',
    quantile: 'quantile',
};
export const DiscreteScale = {
    ordinal: 'ordinal',
    band: 'band',
    point: 'point',
};
export const ConstantScale = {
    constant: 'constant',
};
//# sourceMappingURL=scale.js.map