{"version": 3, "sources": ["../browser/src/metadata/ForeignKeyMetadata.ts"], "names": [], "mappings": "AAOA;;GAEG;AACH,MAAM,OAAO,kBAAkB;IAmE3B,wEAAwE;IACxE,cAAc;IACd,wEAAwE;IAExE,YAAY,OAUX;QAlED;;WAEG;QACH,YAAO,GAAqB,EAAE,CAAA;QAE9B;;WAEG;QACH,sBAAiB,GAAqB,EAAE,CAAA;QA6BxC;;WAEG;QACH,gBAAW,GAAa,EAAE,CAAA;QAE1B;;WAEG;QACH,0BAAqB,GAAa,EAAE,CAAA;QAsBhC,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,CAAA;QAC5C,IAAI,CAAC,wBAAwB,GAAG,OAAO,CAAC,wBAAwB,CAAA;QAChE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAA;QAC9B,IAAI,CAAC,iBAAiB,GAAG,OAAO,CAAC,iBAAiB,CAAA;QAClD,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,WAAW,CAAA;QAC/C,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,WAAW,CAAA;QAC/C,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAA;QACpC,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,IAAI,CAAA;QAC7B,IAAI,OAAO,CAAC,cAAc;YAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,CAAA;IAClE,CAAC;IAED,wEAAwE;IACxE,iBAAiB;IACjB,wEAAwE;IAExE;;;OAGG;IACH,KAAK,CAAC,cAAuC;QACzC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,YAAY,CAAC,CAAA;QACpE,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CACnD,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,YAAY,CAClC,CAAA;QACD,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,wBAAwB,CAAC,SAAS,CAAA;QAClE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS;YACtB,CAAC,CAAC,IAAI,CAAC,SAAS;YAChB,CAAC,CAAC,cAAc,CAAC,cAAc,CACzB,IAAI,CAAC,cAAc,CAAC,SAAS,EAC7B,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,wBAAwB,CAAC,SAAS,EACvC,IAAI,CAAC,qBAAqB,CAC7B,CAAA;IACX,CAAC;CACJ", "file": "ForeignKeyMetadata.js", "sourcesContent": ["import { ColumnMetadata } from \"./ColumnMetadata\"\nimport { EntityMetadata } from \"./EntityMetadata\"\nimport { NamingStrategyInterface } from \"../naming-strategy/NamingStrategyInterface\"\nimport { DeferrableType } from \"./types/DeferrableType\"\nimport { OnDeleteType } from \"./types/OnDeleteType\"\nimport { OnUpdateType } from \"./types/OnUpdateType\"\n\n/**\n * Contains all information about entity's foreign key.\n */\nexport class ForeignKeyMetadata {\n    // -------------------------------------------------------------------------\n    // Public Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Entity metadata where this foreign key is.\n     */\n    entityMetadata: EntityMetadata\n\n    /**\n     * Entity metadata which this foreign key references.\n     */\n    referencedEntityMetadata: EntityMetadata\n\n    /**\n     * Array of columns of this foreign key.\n     */\n    columns: ColumnMetadata[] = []\n\n    /**\n     * Array of referenced columns.\n     */\n    referencedColumns: ColumnMetadata[] = []\n\n    /**\n     * What to do with a relation on deletion of the row containing a foreign key.\n     */\n    onDelete?: OnDeleteType\n\n    /**\n     * What to do with a relation on update of the row containing a foreign key.\n     */\n    onUpdate?: OnUpdateType\n\n    /**\n     * When to check the constraints of a foreign key.\n     */\n    deferrable?: DeferrableType\n\n    /**\n     * Gets the table name to which this foreign key is referenced.\n     */\n    referencedTablePath: string\n\n    /**\n     * Gets foreign key name.\n     * If unique constraint name was given by a user then it stores givenName.\n     * If unique constraint name was not given then its generated.\n     */\n    name: string\n\n    /**\n     * Gets array of column names.\n     */\n    columnNames: string[] = []\n\n    /**\n     * Gets array of referenced column names.\n     */\n    referencedColumnNames: string[] = []\n\n    /**\n     * User specified unique constraint name.\n     */\n    givenName?: string\n\n    // ---------------------------------------------------------------------\n    // Constructor\n    // ---------------------------------------------------------------------\n\n    constructor(options: {\n        entityMetadata: EntityMetadata\n        referencedEntityMetadata: EntityMetadata\n        namingStrategy?: NamingStrategyInterface\n        columns: ColumnMetadata[]\n        referencedColumns: ColumnMetadata[]\n        onDelete?: OnDeleteType\n        onUpdate?: OnUpdateType\n        deferrable?: DeferrableType\n        name?: string\n    }) {\n        this.entityMetadata = options.entityMetadata\n        this.referencedEntityMetadata = options.referencedEntityMetadata\n        this.columns = options.columns\n        this.referencedColumns = options.referencedColumns\n        this.onDelete = options.onDelete || \"NO ACTION\"\n        this.onUpdate = options.onUpdate || \"NO ACTION\"\n        this.deferrable = options.deferrable\n        this.givenName = options.name\n        if (options.namingStrategy) this.build(options.namingStrategy)\n    }\n\n    // ---------------------------------------------------------------------\n    // Public Methods\n    // ---------------------------------------------------------------------\n\n    /**\n     * Builds some depend foreign key properties.\n     * Must be called after all entity metadatas and their columns are built.\n     */\n    build(namingStrategy: NamingStrategyInterface) {\n        this.columnNames = this.columns.map((column) => column.databaseName)\n        this.referencedColumnNames = this.referencedColumns.map(\n            (column) => column.databaseName,\n        )\n        this.referencedTablePath = this.referencedEntityMetadata.tablePath\n        this.name = this.givenName\n            ? this.givenName\n            : namingStrategy.foreignKeyName(\n                  this.entityMetadata.tableName,\n                  this.columnNames,\n                  this.referencedEntityMetadata.tableName,\n                  this.referencedColumnNames,\n              )\n    }\n}\n"], "sourceRoot": ".."}