{"version": 3, "file": "gauge.js", "sourceRoot": "", "sources": ["../../src/mark/gauge.ts"], "names": [], "mappings": ";;;;;;;;;;;AAAA,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AAE/C,OAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAChC,OAAO,EAAE,kBAAkB,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAIzE,OAAO,EAAE,mBAAmB,EAAE,MAAM,qBAAqB,CAAC;AAC1D,OAAO,EAAE,MAAM,EAAE,MAAM,eAAe,CAAC;AACvC,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,MAAM,gBAAgB,CAAC;AACvD,OAAO,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AAC5C,OAAO,EAAE,UAAU,EAAE,MAAM,UAAU,CAAC;AAEtC,MAAM,cAAc,GAAqB,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;IAC5D,MAAM,EAAE,KAAK,EAAE,MAAM,KAAe,OAAO,EAAjB,KAAK,UAAK,OAAO,EAArC,mBAA2B,CAAU,CAAC;IAC5C,MAAM,YAAY,GAAG,SAAS,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;IACjD,MAAM,QAAQ,GAAG,SAAS,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IACzC,MAAM,EAAE,KAAK,EAAE,YAAY,KAAyB,YAAY,EAAhC,eAAe,UAAK,YAAY,EAA1D,SAA2C,CAAe,CAAC;IACjE,MAAM,EAAE,KAAK,EAAE,QAAQ,KAAqB,QAAQ,EAAxB,WAAW,UAAK,QAAQ,EAA9C,SAAmC,CAAW,CAAC;IACrD,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;IACtC,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;QACvB,iBAAiB;QACjB,MAAM,cAAc,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/D,sBAAsB;QACtB,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,WAAW,CAAC,GAAG,mBAAmB,CAC7D,UAAU,EACV,OAAO,CACI,CAAC;QACd,MAAM,aAAa,GAAG,UAAU,CAAC,KAAK,EAAE,CAAC;QACzC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC;QAChC,MAAM,kBAAkB,GAAG,MAAM,CAAC;YAChC,UAAU;YACV,QAAQ;YACR,WAAW;YACX,WAAW,EAAE,MAAM;SACpB,CAAC,CAAC;QACH,kBAAkB,CAAC,IAAI,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;QACvC,aAAa,CAAC,MAAM,CAAC;YACnB,eAAe,EAAE,kBAAkB;SACpC,CAAC,CAAC;QACH,MAAM,SAAS,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAClE,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,SAAS,CAAC,SAAsB,CAAC,CAAC;QACjD,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,UAAU,CAAC,SAAS,EAAE,CAAC;QACxC,MAAM,YAAY,iCAChB,EAAE,EAAE,CAAC,EACL,EAAE,EAAE,CAAC,EACL,EAAE,EAAE,EAAE,EACN,EAAE,EAAE,EAAE,EACN,MAAM,IACH,eAAe,GACf,KAAK,CACT,CAAC;QACF,MAAM,QAAQ,iCACZ,EAAE;YACF,EAAE;YACF,MAAM,IACH,WAAW,GACX,KAAK,CACT,CAAC;QACF,MAAM,cAAc,GAAG,MAAM,CAAC,IAAI,KAAK,EAAE,CAAC,CAAC;QAC3C,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;YAC1B,OAAO,YAAY,KAAK,UAAU;gBAChC,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,EAAE,CACzB,YAAY,CAAC,SAAS,EAAE,KAAK,EAAE,aAAa,EAAE,KAAK,CAAC,CACrD;gBACH,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,IAAI,EAAE,CAAC;SACzE;QACD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YACtB,OAAO,QAAQ,KAAK,UAAU;gBAC5B,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,EAAE,CACzB,QAAQ,CAAC,SAAS,EAAE,KAAK,EAAE,aAAa,EAAE,KAAK,CAAC,CACjD;gBACH,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC,IAAI,EAAE,CAAC;SACvE;QACD,OAAO,cAAc,CAAC,IAAI,EAAE,CAAC;IAC/B,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,eAAe,GAAG;IACtB,UAAU,EAAE;QACV,IAAI,EAAE,QAAQ;QACd,WAAW,EAAE,GAAG;QAChB,WAAW,EAAE,CAAC;QACd,UAAU,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE;QAChC,QAAQ,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE;KAC7B;IACD,IAAI,EAAE;QACJ,CAAC,EAAE,KAAK;KACT;IACD,MAAM,EAAE,KAAK;IACb,OAAO,EAAE,KAAK;IACd,MAAM,EAAE;QACN,CAAC,EAAE,GAAG;QACN,CAAC,EAAE,GAAG;QACN,KAAK,EAAE,OAAO;KACf;IACD,KAAK,EAAE;QACL,KAAK,EAAE;YACL,KAAK,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;SAC9B;KACF;CACF,CAAC;AAEF,MAAM,yBAAyB,GAAG;IAChC,KAAK,EAAE;QACL,KAAK,EAAE,cAAc;QACrB,SAAS,EAAE,CAAC;QACZ,cAAc,EAAE,OAAO;QACvB,IAAI,EAAE,EAAE;QACR,OAAO,EAAE,MAAM;QACf,MAAM,EAAE,GAAG;KACZ;CACF,CAAC;AAEF,MAAM,oBAAoB,GAAG;IAC3B,IAAI,EAAE,MAAM;IACZ,KAAK,EAAE;QACL,CAAC,EAAE,KAAK;QACR,CAAC,EAAE,KAAK;QACR,SAAS,EAAE,QAAQ;QACnB,YAAY,EAAE,QAAQ;QACtB,QAAQ,EAAE,EAAE;QACZ,UAAU,EAAE,GAAG;QACf,IAAI,EAAE,MAAM;KACb;IACD,OAAO,EAAE,KAAK;CACf,CAAC;AAYF,SAAS,YAAY,CAAC,IAAe;IACnC,IAAI,QAAQ,CAAC,IAAI,CAAC,EAAE;QAClB,wBAAwB;QACxB,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;QAC/C,OAAO;YACL,OAAO;YACP,MAAM,EAAE,OAAO;YACf,KAAK,EAAE,CAAC;SACT,CAAC;KACH;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,aAAa,CAAC,IAAe,EAAE,KAAK;IAC3C,MAAM,EACJ,IAAI,GAAG,OAAO,EACd,MAAM,EACN,KAAK,EACL,OAAO,EACP,UAAU,GAAG,EAAE,GAChB,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;IACvB,MAAM,OAAO,GAAG,OAAO,IAAI,MAAM,CAAC;IAClC,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;IACnC,MAAM,QAAQ,mBACZ,CAAC,EAAE;YACD,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC;SACpB,IACE,KAAK,CACT,CAAC;IACF,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;QACtB,OAAO;YACL,UAAU,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC;YACtD,SAAS,EAAE;gBACT,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE;gBACxC,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,GAAG,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE;aACjD;YACD,MAAM,EAAE,OAAO;YACf,KAAK,EAAE,MAAM;YACb,KAAK,EAAE,QAAQ;SAChB,CAAC;KACH;IACD,OAAO;QACL,UAAU,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC;QACtD,SAAS,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;YACnC,CAAC,EAAE,IAAI;YACP,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrC,KAAK,EAAE,CAAC;SACT,CAAC,CAAC;QACH,MAAM,EAAE,OAAO;QACf,KAAK,EAAE,MAAM;QACb,KAAK,EAAE,QAAQ;KAChB,CAAC;AACJ,CAAC;AAED,SAAS,cAAc,CAAC,SAAS,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE;IAClD,MAAM,EAAE,OAAO,EAAE,GAAG,SAAS,CAAC;IAC9B,OAAO,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;AAC9D,CAAC;AAID,MAAM,CAAC,MAAM,KAAK,GAAqB,CAAC,OAAO,EAAE,EAAE;IACjD,MAAM,EACJ,IAAI,GAAG,EAAE,EACT,KAAK,GAAG,EAAE,EACV,KAAK,GAAG,EAAE,EACV,OAAO,GAAG,EAAE,EACZ,SAAS,GAAG,EAAE,KAEZ,OAAO,EADN,UAAU,UACX,OAAO,EAPL,kDAOL,CAAU,CAAC;IACZ,MAAM,EACJ,UAAU,EACV,SAAS,EACT,MAAM,EACN,KAAK,EACL,KAAK,EAAE,QAAQ,GAChB,GAAG,aAAa,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC/B,MAAM,KAA4B,SAAS,CAAC,KAAK,EAAE,MAAM,CAAC,EAApD,EAAE,OAAO,OAA2C,EAAtC,SAAS,cAAvB,WAAyB,CAA2B,CAAC;IAC3D,gBAAgB;IAChB,MAAM,cAAc,GAAG,kBAAkB,CAAC,KAAK,EAAE,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC;IAErE,MAAM,QAAQ,GAAG,SAAS,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IACzC,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;IAE7B,OAAO;QACL,OAAO,CAAC,EAAE,EAAE,eAAe,kBACzB,IAAI,EAAE,UAAU,EAChB,SAAS,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,EAC/B,IAAI,EAAE,SAAS,EACf,KAAK,EAAE,QAAQ,EACf,KAAK,EAAE,KAAK,KAAK,OAAO,CAAC,CAAC,iCAAM,QAAQ,KAAE,KAAK,EAAE,UAAU,IAAG,CAAC,CAAC,QAAQ,EACxE,OAAO,EACL,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,IAChE,UAAU,EACb;QACF,OAAO,CAAC,EAAE,EAAE,eAAe,EAAE,yBAAyB,kBACpD,IAAI,EAAE,OAAO,EACb,IAAI,EAAE,UAAU,EAChB,KAAK,EAAE,QAAQ,EACf,KAAK,EAAE,cAAc,EACrB,OAAO,EACL,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,OAAO,IACtE,UAAU,EACb;QACF,OAAO,CAAC,EAAE,EAAE,oBAAoB,EAAE;YAChC,KAAK,kBACH,IAAI,EAAE,cAAc,CAAC,SAAS,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,IAC/C,SAAS,CACb;YACD,OAAO;YACP,OAAO,EACL,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO;SACrE,CAAC;KACH,CAAC;AACJ,CAAC,CAAC;AAEF,KAAK,CAAC,KAAK,GAAG,EAAE,CAAC"}