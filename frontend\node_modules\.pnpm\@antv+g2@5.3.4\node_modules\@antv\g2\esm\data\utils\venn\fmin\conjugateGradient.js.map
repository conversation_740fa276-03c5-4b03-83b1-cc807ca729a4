{"version": 3, "file": "conjugateGradient.js", "sourceRoot": "", "sources": ["../../../../../src/data/utils/venn/fmin/conjugateGradient.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,SAAS,CAAC;AAC/D,OAAO,EAAE,eAAe,EAAE,MAAM,cAAc,CAAC;AAE/C,MAAM,UAAU,iBAAiB,CAAC,CAAC,EAAE,OAAO,EAAE,MAAM;IAClD,yEAAyE;IACzE,UAAU;IACV,IAAI,OAAO,GAAG,EAAE,CAAC,EAAE,OAAO,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,OAAO,EAAE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;IACtE,IAAI,IAAI,GAAG,EAAE,CAAC,EAAE,OAAO,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,OAAO,EAAE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;IACnE,MAAM,EAAE,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;IAC3B,IAAI,IAAI,CAAC;IACT,IAAI,CAAC,GAAG,CAAC,CAAC;IAEV,MAAM,GAAG,MAAM,IAAI,EAAE,CAAC;IACtB,MAAM,aAAa,GAAG,MAAM,CAAC,aAAa,IAAI,OAAO,CAAC,MAAM,GAAG,EAAE,CAAC;IAElE,OAAO,CAAC,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;IAC3C,MAAM,EAAE,GAAG,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;IACnC,KAAK,CAAC,EAAE,EAAE,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;IAE/B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,EAAE,EAAE,CAAC,EAAE;QACtC,CAAC,GAAG,eAAe,CAAC,CAAC,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QAE7C,+BAA+B;QAC/B,IAAI,MAAM,CAAC,OAAO,EAAE;YAClB,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC;gBAClB,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE;gBACpB,EAAE,EAAE,OAAO,CAAC,EAAE;gBACd,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE;gBAChC,KAAK,EAAE,CAAC;aACT,CAAC,CAAC;SACJ;QAED,IAAI,CAAC,CAAC,EAAE;YACN,wDAAwD;YACxD,qCAAqC;YACrC,KAAK,CAAC,EAAE,EAAE,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;SAChC;aAAM;YACL,iDAAiD;YACjD,WAAW,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;YAEtD,MAAM,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;YACtD,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;YAE5D,WAAW,CAAC,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;YAE9C,IAAI,GAAG,OAAO,CAAC;YACf,OAAO,GAAG,IAAI,CAAC;YACf,IAAI,GAAG,IAAI,CAAC;SACb;QAED,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,IAAI,EAAE;YAClC,MAAM;SACP;KACF;IAED,IAAI,MAAM,CAAC,OAAO,EAAE;QAClB,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC;YAClB,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE;YACpB,EAAE,EAAE,OAAO,CAAC,EAAE;YACd,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE;YAChC,KAAK,EAAE,CAAC;SACT,CAAC,CAAC;KACJ;IAED,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,mDAAmD;AACnD,wCAAwC;AACxC,MAAM,UAAU,sBAAsB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,OAAa;IAC3D,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC;IACpB,MAAM,EAAE,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC;IACrB,IAAI,KAAK,CAAC;IACV,IAAI,KAAK,CAAC;IACV,IAAI,KAAK,CAAC;IAEV,cAAc;IACd,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACf,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IAC7B,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC;IACpB,KAAK,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAElB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;QACjC,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACf,KAAK,GAAG,KAAK,GAAG,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC3B,IAAI,OAAO,EAAE;YACX,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;SAC5D;QAED,cAAc;QACd,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;QAE/B,gBAAgB;QAChB,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QACjC,KAAK,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAClB,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK;YAAE,MAAM;QAErC,uBAAuB;QACvB,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,GAAG,KAAK,EAAE,CAAC,CAAC,CAAC;QACvC,KAAK,GAAG,KAAK,CAAC;KACf;IACD,IAAI,OAAO,EAAE;QACX,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;KAC5D;IACD,OAAO,CAAC,CAAC;AACX,CAAC"}