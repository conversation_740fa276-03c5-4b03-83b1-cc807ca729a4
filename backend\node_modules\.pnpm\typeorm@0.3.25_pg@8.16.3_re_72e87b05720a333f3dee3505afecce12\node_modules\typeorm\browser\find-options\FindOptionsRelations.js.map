{"version": 3, "sources": ["../browser/src/find-options/FindOptionsRelations.ts"], "names": [], "mappings": "", "file": "FindOptionsRelations.js", "sourcesContent": ["import { ObjectId } from \"../driver/mongodb/typings\"\n\n/**\n * A single property handler for FindOptionsRelations.\n */\nexport type FindOptionsRelationsProperty<Property> = Property extends Promise<\n    infer I\n>\n    ? FindOptionsRelationsProperty<NonNullable<I>> | boolean\n    : Property extends Array<infer I>\n    ? FindOptionsRelationsProperty<NonNullable<I>> | boolean\n    : Property extends string\n    ? never\n    : Property extends number\n    ? never\n    : Property extends boolean\n    ? never\n    : Property extends Function\n    ? never\n    : Property extends Buffer\n    ? never\n    : Property extends Date\n    ? never\n    : Property extends ObjectId\n    ? never\n    : Property extends object\n    ? FindOptionsRelations<Property> | boolean\n    : boolean\n\n/**\n * Relations find options.\n */\nexport type FindOptionsRelations<Entity> = {\n    [P in keyof Entity]?: P extends \"toString\"\n        ? unknown\n        : FindOptionsRelationsProperty<NonNullable<Entity[P]>>\n}\n\n/**\n * Relation names to be selected by \"relation\" defined as string.\n * Old relation mechanism in TypeORM.\n *\n * @deprecated will be removed in the next version, use FindOptionsRelation type notation instead\n */\nexport type FindOptionsRelationByString = string[]\n"], "sourceRoot": ".."}