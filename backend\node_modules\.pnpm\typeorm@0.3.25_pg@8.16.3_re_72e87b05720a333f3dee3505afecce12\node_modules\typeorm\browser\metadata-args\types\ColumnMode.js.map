{"version": 3, "sources": ["../browser/src/metadata-args/types/ColumnMode.ts"], "names": [], "mappings": "", "file": "ColumnMode.js", "sourcesContent": ["/**\n * Kinda type of the column. Not a type in the database, but locally used type to determine what kind of column\n * we are working with.\n * For example, \"primary\" means that it will be a primary column, or \"createDate\" means that it will create a create\n * date column.\n */\nexport type ColumnMode =\n    | \"regular\"\n    | \"virtual\"\n    | \"virtual-property\"\n    | \"createDate\"\n    | \"updateDate\"\n    | \"deleteDate\"\n    | \"version\"\n    | \"treeChildrenCount\"\n    | \"treeLevel\"\n    | \"objectId\"\n    | \"array\"\n"], "sourceRoot": "../.."}