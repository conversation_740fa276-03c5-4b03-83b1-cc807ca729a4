{"version": 3, "file": "link.js", "sourceRoot": "", "sources": ["../../src/mark/link.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,cAAc,EAAE,cAAc,EAAE,MAAM,cAAc,CAAC;AAC9D,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,UAAU,CAAC;AACnE,OAAO,EACL,oBAAoB,EACpB,iBAAiB,EACjB,gBAAgB,EAChB,gBAAgB,EAChB,SAAS,GACV,MAAM,SAAS,CAAC;AAEjB,MAAM,KAAK,GAAG;IACZ,IAAI,EAAE,SAAS;IACf,GAAG,EAAE,OAAO;IACZ,MAAM,EAAE,UAAU;IAClB,GAAG,EAAE,OAAO;CACb,CAAC;AAIF;;GAEG;AACH,MAAM,CAAC,MAAM,IAAI,GAAoB,CAAC,OAAO,EAAE,EAAE;IAC/C,OAAO,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,EAAE;QACzC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC;QACrD,MAAM,MAAM,GAAG,gBAAgB,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QACvD,MAAM,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;YACzB,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAY;YACpD,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAY;SACvD,CAAC,CAAC;QACH,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF,IAAI,CAAC,KAAK,GAAG;IACX,YAAY,EAAE,MAAM;IACpB,iBAAiB,EAAE,OAAO;IAC1B,SAAS,EAAE,KAAK;IAChB,KAAK;IACL,QAAQ,EAAE;QACR,GAAG,oBAAoB,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;QACvD,EAAE,IAAI,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE;QAC7B,EAAE,IAAI,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE;KAC9B;IACD,YAAY,EAAE;QACZ,GAAG,gBAAgB,EAAE;QACrB,EAAE,IAAI,EAAE,cAAc,EAAE;QACxB,EAAE,IAAI,EAAE,cAAc,EAAE;KACzB;IACD,aAAa,EAAE,CAAC,GAAG,iBAAiB,EAAE,EAAE,GAAG,SAAS,EAAE,CAAC;CACxD,CAAC"}