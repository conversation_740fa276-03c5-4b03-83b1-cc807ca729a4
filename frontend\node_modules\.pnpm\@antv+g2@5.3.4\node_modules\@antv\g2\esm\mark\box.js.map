{"version": 3, "file": "box.js", "sourceRoot": "", "sources": ["../../src/mark/box.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,UAAU,CAAC;AAC/C,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAC1C,OAAO,EACL,oBAAoB,EACpB,iBAAiB,EACjB,gBAAgB,EAChB,SAAS,GACV,MAAM,SAAS,CAAC;AAEjB,MAAM,KAAK,GAAG;IACZ,GAAG,EAAE,QAAQ;IACb,MAAM,EAAE,SAAS;CAClB,CAAC;AAIF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA6BG;AACH,MAAM,CAAC,MAAM,GAAG,GAAmB,GAAG,EAAE;IACtC,OAAO,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,EAAE;QACzC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC;QAExE,2BAA2B;QAC3B,2DAA2D;QAC3D,MAAM,MAAM,GAAG,KAAK,CAAC,CAAS,CAAC;QAC/B,MAAM,MAAM,GAAG,KAAK,CAAC,MAAc,CAAC;QAEpC,MAAM,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,EAAE;YAChC,MAAM,UAAU,GAAG,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7D,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAA,CAAC,aAAD,CAAC,uBAAD,CAAC,CAAG,CAAC,CAAC,CAAA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvE,MAAM,KAAK,GAAG,UAAU,GAAG,KAAK,CAAC;YACjC,MAAM,MAAM,GAAG,CAAC,CAAC,CAAA,CAAC,aAAD,CAAC,uBAAD,CAAC,CAAG,CAAC,CAAC,CAAA,IAAI,CAAC,CAAC,GAAG,UAAU,CAAC;YAE3C,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,GAAG,KAAK,GAAG,CAAC,CAAC;YACrC,MAAM,CAAC,GAAG,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,IAAI,CAAC,GAAG;gBAClC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACL,CAAC,EAAE,CAAC,CAAC,CAAC;gBACN,CAAC,EAAE,CAAC,CAAC,CAAC;gBACN,CAAC,EAAE,CAAC,CAAC,CAAC;gBACN,CAAC,EAAE,CAAC,CAAC,CAAC;aACP,CAAC;YAEF,MAAM,GAAG,GAAG;gBACV,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,IAAI,CAAC;gBACrB,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,IAAI,CAAC;gBACrB,CAAC,CAAC,EAAE,IAAI,CAAC;gBACT,CAAC,CAAC,EAAE,EAAE,CAAC;gBACP,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,EAAE,CAAC;gBACnB,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,EAAE,CAAC;gBACnB,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,EAAE,CAAC;gBACnB,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,EAAE,CAAC;gBACnB,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,MAAM,CAAC;gBACvB,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,MAAM,CAAC;gBACvB,CAAC,CAAC,EAAE,EAAE,CAAC;gBACP,CAAC,CAAC,EAAE,GAAG,CAAC;gBACR,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,GAAG,CAAC;gBACpB,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,GAAG,CAAC;aACrB,CAAC;YAEF,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAc,CAAC;QACxD,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF,GAAG,CAAC,KAAK,GAAG;IACV,YAAY,EAAE,KAAK;IACnB,iBAAiB,EAAE,OAAO;IAC1B,SAAS,EAAE,KAAK;IAChB,KAAK;IACL,QAAQ,EAAE;QACR,GAAG,oBAAoB,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;QACvD,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE;QAC5C,EAAE,IAAI,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE;QAC7B,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE;KAClC;IACD,YAAY,EAAE,CAAC,GAAG,gBAAgB,EAAE,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC;IAC3D,aAAa,EAAE,CAAC,GAAG,iBAAiB,EAAE,EAAE,GAAG,SAAS,EAAE,CAAC;IACvD,WAAW,EAAE;QACX,YAAY,EAAE,IAAI;KACnB;CACF,CAAC"}