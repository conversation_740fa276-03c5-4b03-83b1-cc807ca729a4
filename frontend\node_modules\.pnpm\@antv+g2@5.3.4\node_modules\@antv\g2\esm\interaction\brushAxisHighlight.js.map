{"version": 3, "file": "brushAxisHighlight.js", "sourceRoot": "", "sources": ["../../src/interaction/brushAxisHighlight.ts"], "names": [], "mappings": ";;;;;;;;;;;AAAA,OAAO,EAAE,IAAI,EAAE,MAAM,SAAS,CAAC;AAC/B,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAC5C,OAAO,EAAE,QAAQ,EAAY,MAAM,gBAAgB,CAAC;AACpD,OAAO,EAAE,KAAK,EAAE,MAAM,kBAAkB,CAAC;AACzC,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AACjD,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AACjD,OAAO,EACL,gBAAgB,EAChB,aAAa,EACb,aAAa,EACb,QAAQ,EACR,cAAc,EACd,UAAU,GACX,MAAM,SAAS,CAAC;AAEjB,MAAM,CAAC,MAAM,eAAe,GAAG,MAAM,CAAC;AAEtC,MAAM,CAAC,MAAM,oBAAoB,GAAG,WAAW,CAAC;AAEhD,MAAM,CAAC,MAAM,oBAAoB,GAAG,iBAAiB,CAAC;AAEtD,MAAM,CAAC,MAAM,wBAAwB,GAAG,eAAe,CAAC;AAExD,SAAS,MAAM,CAAC,SAAS;IACvB,OAAO,SAAS,CAAC,sBAAsB,CAAC,eAAe,CAAC,CAAC;AAC3D,CAAC;AAED,SAAS,MAAM,CAAC,IAAI;IAClB,OAAO,IAAI,CAAC,sBAAsB,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9D,CAAC;AAED,SAAS,WAAW,CAAC,IAAI;IACvB,OAAO,IAAI,CAAC,sBAAsB,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9D,CAAC;AAED,8DAA8D;AAC9D,6BAA6B;AAC7B,SAAS,YAAY,CAAC,IAAI;IACxB,OAAO,WAAW,CAAC,IAAI,CAAC,CAAC,cAAc,EAAE,CAAC;AAC5C,CAAC;AAED,2BAA2B;AAC3B,SAAS,aAAa,CAAC,IAAI,EAAE,EAAqC;QAArC,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,OAAY,EAAP,KAAK,cAAnC,+BAAqC,CAAF;IAC9D,MAAM,MAAM,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;IAClC,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;IAC9B,MAAM,CAAC,KAAK,CAAC,GAAG,QAAQ,CAAC,cAAc,EAAE,CAAC,GAAG,CAAC;IAC9C,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC;IAChC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC;IAChC,MAAM,IAAI,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;IAC/B,OAAO;QACL,WAAW,EAAE,YAAY;QACzB,OAAO,EAAE,IAAI,IAAI,CAAC;YAChB,SAAS,EAAE,wBAAwB;YACnC,KAAK;gBACH,2DAA2D;gBAC3D,+CAA+C;gBAC/C,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,EAC9B,SAAS,EAAE,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,OAAO,CAC/D,CAAC,CACF,KAAK,IAAI,GAAG,EACb,MAAM,EAAE,IAAI,GAAG,IAAI,IAChB,KAAK,CACT;SACF,CAAC;QACF,MAAM,EAAE,KAAK;YACX,CAAC,CAAC,0CAA0C;gBAC1C,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,EAAE,QAAQ,EAAE,EAAE,CAAC;YAChD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;gBAChB,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,OAAO,CAAC;gBAC1B,CAAC;gBACD,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC;gBACzB,EAAE;aACH;KACN,CAAC;AACJ,CAAC;AAED,6BAA6B;AAC7B,SAAS,eAAe,CAAC,IAAI,EAAE,EAA6C;QAA7C,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,GAAG,KAAK,OAAY,EAAP,KAAK,cAA3C,+BAA6C,CAAF;IACxE,MAAM,MAAM,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;IAClC,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;IAC9B,MAAM,CAAC,EAAE,KAAK,CAAC,GAAG,QAAQ,CAAC,cAAc,EAAE,CAAC,GAAG,CAAC;IAChD,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC;IAChC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC;IAChC,MAAM,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;IACzB,OAAO;QACL,WAAW,EAAE,YAAY;QACzB,OAAO,EAAE,IAAI,IAAI,CAAC;YAChB,SAAS,EAAE,wBAAwB;YACnC,KAAK,kBACH,KAAK,EAAE,IAAI,GAAG,IAAI;gBAClB,2DAA2D;gBAC3D,+CAA+C;gBAC/C,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,EAC/B,SAAS,EAAE,aAAa,IAAI,KAAK,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,GAAG,IAC5D,KAAK,CACT;SACF,CAAC;QACF,MAAM,EAAE,KAAK;YACX,CAAC,CAAC,0CAA0C;gBAC1C,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,QAAQ,EAAE,EAAE,EAAE,QAAQ,CAAC;YAChD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;gBAChB,CAAC;gBACD,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,OAAO,CAAC;gBAC1B,EAAE;gBACF,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC;aAC1B;KACN,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,kBAAkB,CAChC,IAAI,EACJ,EAaC;QAbD,EACE,IAAI,EAAE,MAAM,EAAE,0BAA0B;IACxC,QAAQ,EAAE,UAAU,EAAE,8BAA8B;IACpD,MAAM,EAAE,QAAQ,EAAE,qCAAqC;IACvD,UAAU,EAAE,YAAY,EAAE,+BAA+B;IACzD,KAAK,EAAE,4BAA4B;IACnC,OAAO,EAAE,yBAAyB;IAClC,OAAO,EAAE,yBAAyB;IAClC,OAAO,GAAG,KAAK,EACf,KAAK,GAAG,EAAE,EACV,OAAO,EACP,UAAU,OAEX,EADI,IAAI,cAZT,wHAaC,CADQ,CAAC,QAAQ;;IAGlB,MAAM,QAAQ,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;IAClC,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;IAC1B,MAAM,OAAO,GAAG,aAAa,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;IAC/C,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG,QAAQ,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IAC3D,MAAM,UAAU,GAAG,IAAI,GAAG,EAAE,CAAC;IAC7B,MAAM,UAAU,GAAG,SAAS,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IAE3C,2DAA2D;IAC3D,MAAM,OAAO,GAAG,CAAC,MAAM,EAAE,EAAE,CACzB,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CACvD,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE;QACvB,OAAO,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC;IACpD,CAAC,CAAC,CACH,CAAC;IAEJ,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;IAEnD,MAAM,QAAQ,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAErE,MAAM,WAAW,GAAG,IAAI,GAAG,EAAsB,CAAC;IAElD,MAAM,eAAe,GAAG,GAAG,EAAE;QAC3B,WAAW,CAAC,KAAK,EAAE,CAAC;QACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACpC,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACxB,MAAM,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC,UAAU,EAAE,CAAC;YACtC,WAAW,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;SACtC;IACH,CAAC,CAAC;IAEF,eAAe,EAAE,CAAC;IAElB,qCAAqC;IACrC,MAAM,aAAa,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE;QAChC,MAAM,gBAAgB,GAAG,EAAE,CAAC;QAC5B,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;YAC9B,MAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC;YACjC,IAAI,OAAO,CAAC,MAAM,CAAC,EAAE;gBACnB,QAAQ,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;gBAC5B,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;aAChC;;gBAAM,QAAQ,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;SACtC;QAED,WAAW,CAAC,GAAG,CAAC,CAAC,EAAE,WAAW,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,CAAC;QAErD,IAAI,CAAC,IAAI;YAAE,OAAO;QAElB,eAAe;QACf,MAAM,SAAS,GAAG,GAAG,EAAE;YACrB,IAAI,CAAC,KAAK;gBAAE,OAAO,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC;YACpD,MAAM,CAAC,GAAG,EAAE,CAAC;YACb,KAAK,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,IAAI,WAAW,EAAE;gBACzC,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;gBAC5B,MAAM,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC,UAAU,EAAE,CAAC;gBACpC,IAAI,IAAI,KAAK,GAAG;oBAAE,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;;oBAC3B,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;aACpB;YACD,OAAO,CAAC,CAAC;QACX,CAAC,CAAC;QACF,OAAO,CAAC,IAAI,CAAC,qBAAqB,EAAE;YAClC,WAAW,EAAE,IAAI;YACjB,IAAI,EAAE;gBACJ,SAAS,EAAE,SAAS,EAAE;aACvB;SACF,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,MAAM,YAAY,GAAG,CAAC,IAAI,EAAE,EAAE;QAC5B,KAAK,MAAM,OAAO,IAAI,QAAQ;YAAE,WAAW,CAAC,OAAO,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;QAC3E,eAAe,EAAE,CAAC;QAClB,IAAI,CAAC,IAAI;YAAE,OAAO;QAClB,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC;IAC1D,CAAC,CAAC;IAEF,MAAM,WAAW,GAAG,CAAC,QAAQ,EAAE,CAAC,EAAE,EAAE;QAClC,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QACxB,MAAM,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC,UAAU,EAAE,CAAC;QACpC,MAAM,MAAM,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE;YAChC,MAAM,IAAI,GAAG,CAAC,CAAC,QAAQ,CAAC;YACxB,OAAO,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;QACH,OAAO,QAAQ,CAAC,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;IAC3C,CAAC,CAAC;IAEF,8DAA8D;IAC9D,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5E,MAAM,QAAQ,GAAG,EAAE,CAAC;IACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACpC,MAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QACrB,MAAM,WAAW,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,aAAa,CAAC;QACzE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,MAAM,EAAE,GAAG,WAAW,CAAC,IAAI,EAAE;YACzD,OAAO;YACP,OAAO;YACP,KAAK;YACL,MAAM,EAAE,GAAG;YACX,IAAI,EAAE,aAAa,EAAE,uBAAuB;SAC7C,CAAC,CAAC;QACH,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAErC,MAAM,YAAY,GAAG,KAAK,CAAC,OAAO,kCAC7B,UAAU,KACb,OAAO;YACP,WAAW;YACX,UAAU,CAAC,IAAI;gBACb,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBACxB,IAAI,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC,MAAM,KAAK,CAAC;oBAAE,YAAY,CAAC,IAAI,CAAC,CAAC;;oBACjE,aAAa,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;YAC9B,CAAC;YACD,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI;gBACxB,UAAU,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;gBAC3C,aAAa,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;YACzB,CAAC,IACD,CAAC;QACH,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;KAC7B;IAED,MAAM,QAAQ,GAAG,CAAC,QAAa,EAAE,EAAE,EAAE;QACnC,MAAM,EAAE,WAAW,EAAE,GAAG,KAAK,CAAC;QAC9B,IAAI,WAAW;YAAE,OAAO;QACxB,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;IAC3C,CAAC,CAAC;IAEF,MAAM,OAAO,GAAG,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE;QACtC,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,MAAM,CAAC;QACxB,MAAM,SAAS,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnE,MAAM,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QACtC,MAAM,EAAE,GAAG,UAAU,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;QAC1D,IAAI,YAAY,CAAC,IAAI,CAAC;YAAE,OAAO,CAAC,CAAC,EAAE,CAAC,QAAQ,EAAE,EAAE,EAAE,QAAQ,CAAC,CAAC;QAC5D,OAAO,CAAC,CAAC,QAAQ,EAAE,CAAC,EAAE,QAAQ,EAAE,EAAE,CAAC,CAAC;IACtC,CAAC,CAAC;IAEF,MAAM,UAAU,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE;QACpC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,UAAU,CAAC,UAAU,EAAE,CAAC;QAClD,MAAM,MAAM,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;QAC7B,IAAI,YAAY,CAAC,IAAI,CAAC;YAAE,MAAM,CAAC,MAAM,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC;;YACxD,MAAM,CAAC,MAAM,CAAC,EAAE,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QAC3C,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC;IAEF,MAAM,WAAW,GAAG,CAAC,KAAK,EAAE,EAAE;QAC5B,MAAM,EAAE,WAAW,EAAE,GAAG,KAAK,CAAC;QAC9B,IAAI,WAAW;YAAE,OAAO;QACxB,MAAM,EAAE,SAAS,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC;QACjC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACxC,MAAM,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YAC5B,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YAC5B,MAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACrB,IAAI,MAAM,EAAE;gBACV,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;gBACxB,OAAO,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC;aACtD;iBAAM;gBACL,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aACvB;SACF;IACH,CAAC,CAAC;IAEF,OAAO,CAAC,EAAE,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC;IACzC,OAAO,CAAC,EAAE,CAAC,qBAAqB,EAAE,WAAW,CAAC,CAAC;IAE/C,OAAO,GAAG,EAAE;QACV,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;QACrC,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC;QAC1C,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE,WAAW,CAAC,CAAC;IAClD,CAAC,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,kBAAkB,CAAC,OAAO;IACxC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,OAAO,EAAE,EAAE;QAC5B,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG,MAAM,CAAC;QACzD,MAAM,QAAQ,GAAG,cAAc,CAAC,SAAS,CAAC,CAAC;QAC3C,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;QAC5C,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC;QAC5B,OAAO,kBAAkB,CAAC,SAAS,kBACjC,QAAQ,EAAE,gBAAgB,EAC1B,IAAI,EAAE,MAAM,EACZ,OAAO,EAAE,EAAE,EACX,OAAO,EAAE,EAAE,EACX,MAAM,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,EAC5C,UAAU,EAAE,CAAC,IAAI,EAAE,EAAE;gBACnB,MAAM,EACJ,QAAQ,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAClB,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,GACjB,GAAG,IAAI,CAAC,UAAU,CAAC;gBACpB,mDAAmD;gBACnD,qCAAqC;gBACrC,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;YAChC,CAAC,EACD,KAAK,EAAE,aAAa,CAAC,IAAI,CAAC,EAC1B,KAAK,EAAE,UAAU,CAAC,WAAW,EAAE;gBAC7B,QAAQ;gBACR,CAAC,UAAU,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC;aAC/B,CAAC,EACF,UAAU;YACV,OAAO,IACJ,OAAO,EACV,CAAC;IACL,CAAC,CAAC;AACJ,CAAC"}