{"version": 3, "file": "color.js", "sourceRoot": "", "sources": ["../../../src/shape/interval/color.ts"], "names": [], "mappings": ";;;;;;;;;;;AAAA,OAAO,EAAE,GAAG,EAAE,MAAM,uBAAuB,CAAC;AAE5C,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AACvE,OAAO,EAAE,MAAM,EAAE,MAAM,uBAAuB,CAAC;AAC/C,OAAO,EAAE,GAAG,EAAE,MAAM,oBAAoB,CAAC;AACzC,OAAO,EAAE,KAAK,EAAE,MAAM,oBAAoB,CAAC;AAC3C,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,OAAO,EAAE,YAAY,EAAE,MAAM,UAAU,CAAC;AAqB3E,uCAAuC;AACvC,MAAM,UAAU,IAAI,CAClB,QAAQ,EACR,MAAM,EACN,KAAK,EACL,UAAU,EACV,QAA6B,EAAE;IAE/B,MAAM,EACJ,KAAK,GAAG,CAAC,EACT,MAAM,GAAG,CAAC,EACV,SAAS,GAAG,KAAK,EACjB,QAAQ,GAAG,KAAK,EAChB,UAAU,GAAG,KAAK,EAClB,WAAW,GAAG,KAAK,EACnB,gBAAgB,GAAG,MAAM,EACzB,iBAAiB,GAAG,MAAM,EAC1B,aAAa,GAAG,MAAM,EACtB,cAAc,GAAG,MAAM,EACvB,QAAQ,GAAG,CAAC,QAAQ,EACpB,QAAQ,GAAG,QAAQ,EACnB,SAAS,GAAG,CAAC,QAAQ,KAEnB,KAAK,EADJ,IAAI,UACL,KAAK,EAfH,0LAeL,CAAQ,CAAC;IACV,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;QAChD,MAAM,OAAO,GAAG,CAAC,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QAE1C,MAAM,CAAC,EAAE,EAAE,AAAD,EAAG,EAAE,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;QACtD,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC;QAClB,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QACpC,yCAAyC;QACzC,MAAM,IAAI,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;QACvC,MAAM,IAAI,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;QAEzC,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACjC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACnC,MAAM,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;QAChC,MAAM,MAAM,GAAG,IAAI,GAAG,QAAQ,CAAC;QAC/B,MAAM,UAAU,GAAG,QAAQ,GAAG,CAAC,SAAS,GAAG,UAAU,CAAC,CAAC;QACvD,MAAM,WAAW,GAAG,SAAS,GAAG,CAAC,QAAQ,GAAG,WAAW,CAAC,CAAC;QAEzD,MAAM,UAAU,GAAG,OAAO;YACxB,CAAC,CAAC,KAAK,CAAC,UAAU,EAAE,SAAS,EAAE,QAAQ,CAAC;YACxC,CAAC,CAAC,KAAK,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAC1C,MAAM,WAAW,GAAG,OAAO;YACzB,CAAC,CAAC,KAAK,CAAC,WAAW,EAAE,QAAQ,EAAE,QAAQ,CAAC;YACxC,CAAC,CAAC,KAAK,CAAC,WAAW,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;QAC5C,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,UAAU,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;QACzE,MAAM,MAAM,GAAG,OAAO;YACpB,CAAC,CAAC,MAAM,GAAG,CAAC,WAAW,GAAG,WAAW,CAAC,GAAG,CAAC;YAC1C,CAAC,CAAC,MAAM,GAAG,CAAC,WAAW,GAAG,WAAW,CAAC,CAAC;QAEzC,OAAO,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;aAC9C,KAAK,CAAC,GAAG,EAAE,MAAM,CAAC;aAClB,KAAK,CAAC,GAAG,EAAE,MAAM,CAAC;aAClB,KAAK,CAAC,OAAO,EAAE,UAAU,CAAC;aAC1B,KAAK,CAAC,QAAQ,EAAE,WAAW,CAAC;aAC5B,KAAK,CAAC,QAAQ,EAAE;YACf,aAAa;YACb,cAAc;YACd,iBAAiB;YACjB,gBAAgB;SACjB,CAAC;aACD,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;aACtB,IAAI,EAAE,CAAC;KACX;IAED,mCAAmC;IACnC,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,KAAK,CAAC;IACxB,MAAM,MAAM,GAAG,UAAU,CAAC,SAAS,EAAa,CAAC;IACjD,MAAM,SAAS,GAAG,YAAY,CAAC,UAAU,EAAE,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IAC5D,MAAM,IAAI,GAAG,GAAG,EAAE;SACf,YAAY,CAAC,MAAgB,CAAC;SAC9B,QAAQ,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC;IAErC,OAAO,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;SAC9C,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;SAC3B,KAAK,CAAC,WAAW,EAAE,aAAa,MAAM,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC;SAC3D,KAAK,CAAC,QAAQ,EAAE,MAAM,CAAC;SACvB,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC;SACrB,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;SACtB,IAAI,EAAE,CAAC;AACZ,CAAC;AAED;;;;;GAKG;AACH,MAAM,CAAC,MAAM,KAAK,GAAqB,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;IAC1D,oDAAoD;IACpD,MAAM,EACJ,cAAc,EACd,gBAAgB,GAAG,MAAM,EACzB,KAAK,GAAG,IAAI,EACZ,IAAI,GAAG,IAAI,KAET,OAAO,EADN,KAAK,UACN,OAAO,EANL,uDAML,CAAU,CAAC;IAEZ,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC;IAEzC,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE;QACjC,MAAM,EACJ,KAAK,EAAE,YAAY,EACnB,MAAM,EAAE,aAAa,GAAG,CAAC,KAEvB,QAAQ,EADP,YAAY,UACb,QAAQ,EAJN,mBAIL,CAAW,CAAC;QAEb,MAAM,gBAAgB,GAAG,YAAY,CAAC,SAAS,IAAI,CAAC,CAAC;QACrD,MAAM,EACJ,MAAM,EACN,MAAM,GAAG,aAAa,EACtB,aAAa,GAAG,MAAM,EACtB,cAAc,GAAG,MAAM,EACvB,iBAAiB,GAAG,MAAM,EAC1B,gBAAgB,GAAG,MAAM,EACzB,WAAW,GAAG,CAAC,EACf,kBAAkB,GAAG,WAAW,EAChC,mBAAmB,GAAG,WAAW,EACjC,sBAAsB,GAAG,WAAW,EACpC,qBAAqB,GAAG,WAAW,EACnC,SAAS,GAAG,cAAc,KAAK,QAAQ,IAAI,MAAM,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,EACxE,KAAK,GAAG,CAAC,EACT,SAAS,GAAG,KAAK,EACjB,UAAU,GAAG,KAAK,EAClB,WAAW,GAAG,KAAK,EACnB,QAAQ,GAAG,KAAK,EAChB,QAAQ,EACR,QAAQ,EACR,SAAS,KAEP,KAAK,EADJ,IAAI,UACL,KAAK,EAtBH,gUAsBL,CAAQ,CAAC;QACV,MAAM,EAAE,KAAK,GAAG,YAAY,EAAE,OAAO,EAAE,GAAG,KAAK,CAAC;QAEhD,4DAA4D;QAC5D,yBAAyB;QACzB,MAAM,iBAAiB,GAAG;YACxB,KAAK,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,kBAAkB;YAC1C,KAAK,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,mBAAmB;YAC5C,IAAI,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,sBAAsB;YACjD,IAAI,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,qBAAqB;SAChD,CAAC;QACF,MAAM,WAAW,GAAG;YAClB,eAAe;YACf,gBAAgB;YAChB,mBAAmB;YACnB,kBAAkB;SACnB,CAAC;QACF,wCAAwC;QACxC,IAAI,WAAW,CAAC,UAAU,CAAC,EAAE;YAC3B,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC,CAAC;SACvC;QACD,MAAM,aAAa,iCACjB,MAAM,IACH,MAAM,CAAC,WAAW,CACnB,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CACrD,KACD,KAAK;YACL,SAAS;YACT,UAAU;YACV,WAAW;YACX,QAAQ;YACR,QAAQ;YACR,QAAQ;YACR,SAAS,GACV,CAAC;QAEF,OAAO,CACL,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,aAAa,CAAC,CAAC;aAC7D,IAAI,CAAC,UAAU,EAAE,YAAY,CAAC;aAC9B,KAAK,CAAC,MAAM,EAAE,aAAa,CAAC;aAC5B,KAAK,CAAC,cAAc,EAAE,KAAK,CAAC;aAC5B,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC;aACrC,KAAK,CAAC,WAAW,EAAE,SAAS,CAAC;aAC7B,KAAK,CAAC,QAAQ,EAAE,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC;YACvD,mCAAmC;aAClC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;aACtB,IAAI,EAAE,CACV,CAAC;IACJ,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF,sEAAsE;AACtE,KAAK,CAAC,KAAK,GAAG;IACZ,qBAAqB,EAAE,UAAU;IACjC,sBAAsB,EAAE,UAAU;IAClC,oBAAoB,EAAE,SAAS;CAChC,CAAC"}