/**
 * @param x center x
 * @param y center y
 * @param radius
 */
declare function circle(x: number, y: number, r: number): string;
/**
 * @param x center x
 * @param y center y
 * @param radius
 */
declare function rect(x: number, y: number, r: number): string;
/**
 * @param x center x
 * @param y center y
 * @param radius
 */
declare function diamond(x: number, y: number, r: number): string;
/**
 * @param x center x
 * @param y center y
 * @param radius
 */
declare function triangle(x: number, y: number, r: number): string;
/**
 * @param x center x
 * @param y center y
 * @param radius
 */
declare function pin(x: number, y: number, radius: number): string;
export declare const LiquidShapesPath: {
    pin: typeof pin;
    rect: typeof rect;
    circle: typeof circle;
    diamond: typeof diamond;
    triangle: typeof triangle;
};
export {};
