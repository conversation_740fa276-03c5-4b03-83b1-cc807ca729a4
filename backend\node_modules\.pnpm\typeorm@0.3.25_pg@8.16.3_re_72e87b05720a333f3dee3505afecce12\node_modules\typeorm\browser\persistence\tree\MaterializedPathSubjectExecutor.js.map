{"version": 3, "sources": ["../browser/src/persistence/tree/MaterializedPathSubjectExecutor.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,QAAQ,EAAE,MAAM,qBAAqB,CAAA;AAG9C,OAAO,EAAE,cAAc,EAAE,MAAM,+BAA+B,CAAA;AAC9D,OAAO,EAAE,QAAQ,EAAE,MAAM,8BAA8B,CAAA;AAEvD;;GAEG;AACH,MAAM,OAAO,+BAA+B;IACxC,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAsB,WAAwB;QAAxB,gBAAW,GAAX,WAAW,CAAa;IAAG,CAAC;IAElD,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,KAAK,CAAC,MAAM,CAAC,OAAgB;QACzB,IAAI,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,kBAAmB,CAAC,cAAc,CAC5D,OAAO,CAAC,MAAO,CAClB,CAAA,CAAC,oCAAoC;QACtC,IAAI,CAAC,MAAM,IAAI,OAAO,CAAC,aAAa,IAAI,OAAO,CAAC,aAAa,CAAC,MAAM;YAChE,sCAAsC;YACtC,MAAM,GAAG,OAAO,CAAC,aAAa,CAAC,gBAAgB;gBAC3C,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,gBAAgB;gBACxC,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAA;QAEtC,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;QAExD,IAAI,UAAU,GAAW,EAAE,CAAA;QAC3B,IAAI,QAAQ,EAAE,CAAC;YACX,UAAU,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAA;QAC5D,CAAC;QAED,MAAM,gBAAgB,GAAG,OAAO,CAAC,QAAQ;aACpC,kBAAmB,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE;YAChD,OAAO,UAAU,CAAC,gBAAiB,CAAC,cAAc,CAC9C,OAAO,CAAC,gBAAiB,CAC5B,CAAA;QACL,CAAC,CAAC;aACD,IAAI,CAAC,GAAG,CAAC,CAAA;QAEd,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO;aACzB,kBAAkB,EAAE;aACpB,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC;aAC/B,GAAG,CAAC;YACD,CAAC,OAAO,CAAC,QAAQ,CAAC,sBAAuB,CAAC,YAAY,CAAC,EACnD,UAAU,GAAG,gBAAgB,GAAG,GAAG;SACnC,CAAC;aACR,KAAK,CAAC,OAAO,CAAC,UAAW,CAAC;aAC1B,OAAO,EAAE,CAAA;IAClB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAM,CAAC,OAAgB;QACzB,IAAI,SAAS,GAAG,OAAO,CAAC,QAAQ,CAAC,kBAAmB,CAAC,cAAc,CAC/D,OAAO,CAAC,MAAO,CAClB,CAAA,CAAC,oCAAoC;QACtC,IAAI,CAAC,SAAS,IAAI,OAAO,CAAC,aAAa,IAAI,OAAO,CAAC,aAAa,CAAC,MAAM;YACnE,sCAAsC;YACtC,SAAS,GAAG,OAAO,CAAC,aAAa,CAAC,MAAM,CAAA;QAE5C,IAAI,MAAM,GAAG,OAAO,CAAC,cAAc,CAAA,CAAC,oCAAoC;QACxE,IAAI,CAAC,MAAM,IAAI,SAAS;YACpB,sCAAsC;YACtC,MAAM,GAAG,OAAO,CAAC,QAAQ;iBACpB,oBAAqB,CAAC,cAAc,CAAC,SAAS,CAAC;iBAC/C,IAAI,CAAC,CAAC,KAAU,EAAE,EAAE;gBACjB,OAAO,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,UAAW,CAAC,CAAC,KAAK,CAC5C,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,KAAK,CACzC,CAAA;YACL,CAAC,CAAC,CAAA;QAEV,MAAM,SAAS,GAAG,OAAO,CAAC,QAAQ,CAAC,kBAAmB,CAAC,cAAc,CACjE,MAAO,CACV,CAAA;QACD,MAAM,WAAW,GAAG,IAAI,CAAC,kCAAkC,CACvD,OAAO,EACP,SAAS,CACZ,CAAA;QACD,MAAM,WAAW,GAAG,IAAI,CAAC,kCAAkC,CACvD,OAAO,EACP,SAAS,CACZ,CAAA;QAED,+CAA+C;QAC/C,IAAI,QAAQ,CAAC,UAAU,CAAC,WAAW,EAAE,WAAW,CAAC,EAAE,CAAC;YAChD,OAAM;QACV,CAAC;QAED,IAAI,aAAa,GAAW,EAAE,CAAA;QAC9B,IAAI,WAAW,EAAE,CAAC;YACd,aAAa,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,WAAW,CAAC,CAAA;QAClE,CAAC;QAED,IAAI,aAAa,GAAW,EAAE,CAAA;QAC9B,IAAI,WAAW,EAAE,CAAC;YACd,aAAa;gBACT,CAAC,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,IAAI,EAAE,CAAA;QAC9D,CAAC;QAED,MAAM,UAAU,GAAG,OAAO,CAAC,QAAQ;aAC9B,kBAAmB,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE;YAChD,OAAO,UAAU,CAAC,gBAAiB,CAAC,cAAc,CAAC,MAAO,CAAC,CAAA;QAC/D,CAAC,CAAC;aACD,IAAI,CAAC,GAAG,CAAC,CAAA;QAEd,MAAM,YAAY,GACd,OAAO,CAAC,QAAQ,CAAC,sBAAuB,CAAC,YAAY,CAAA;QACzD,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO;aACzB,kBAAkB,EAAE;aACpB,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC;aAC/B,GAAG,CAAC;YACD,CAAC,YAAY,CAAC,EAAE,GAAG,EAAE,CACjB,WAAW,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAChD,YAAY,CACf,MAAM,aAAa,GAAG,UAAU,QAAQ,aAAa,GAAG,UAAU,KAAK;SACxE,CAAC;aACR,KAAK,CAAC,GAAG,YAAY,aAAa,EAAE;YACjC,IAAI,EAAE,GAAG,aAAa,GAAG,UAAU,IAAI;SAC1C,CAAC;aACD,OAAO,EAAE,CAAA;IAClB,CAAC;IAEO,kCAAkC,CACtC,OAAgB,EAChB,MAAiC;QAEjC,IAAI,CAAC,MAAM;YAAE,OAAO,SAAS,CAAA;QAC7B,OAAO,cAAc,CAAC,WAAW,CAC7B,MAAM,EACN,OAAO,CAAC,QAAQ;aACX,kBAAmB,CAAC,WAAW,CAAC,GAAG,CAChC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,gBAAgB,CACtC;aACA,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,IAAI,CAAqB,EACjD,EAAE,SAAS,EAAE,IAAI,EAAE,CACtB,CAAA;IACL,CAAC;IAEO,aAAa,CACjB,OAAgB,EAChB,EAAiB;QAEjB,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAA;QACjC,MAAM,UAAU,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAC1D,QAAQ,CAAC,iBAAiB,CAAC,EAAE,CAAC,CACjC,CAAA;QACD,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO;aAC1B,kBAAkB,EAAE;aACpB,MAAM,CACH,OAAO,CAAC,QAAQ,CAAC,UAAU;YACvB,GAAG;YACH,OAAO,CAAC,QAAQ,CAAC,sBAAuB,CAAC,YAAY,EACzD,MAAM,CACT;aACA,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC;aAC1D,KAAK,CACF,IAAI,QAAQ,CAAC,CAAC,EAAE,EAAE,EAAE;YAChB,KAAK,MAAM,IAAI,IAAI,UAAU,EAAE,CAAC;gBAC5B,EAAE,CAAC,OAAO,CAAC,IAAI,QAAQ,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;YACpD,CAAC;QACL,CAAC,CAAC,CACL;aACA,SAAS,EAAE;aACX,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;IACzD,CAAC;CACJ", "file": "MaterializedPathSubjectExecutor.js", "sourcesContent": ["import { Subject } from \"../Subject\"\nimport { QueryRunner } from \"../../query-runner/QueryRunner\"\nimport { OrmUtils } from \"../../util/OrmUtils\"\nimport { ObjectLiteral } from \"../../common/ObjectLiteral\"\nimport { ColumnMetadata } from \"../../metadata/ColumnMetadata\"\nimport { EntityMetadata } from \"../../metadata/EntityMetadata\"\nimport { Brackets } from \"../../query-builder/Brackets\"\n\n/**\n * Executes subject operations for materialized-path tree entities.\n */\nexport class MaterializedPathSubjectExecutor {\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(protected queryRunner: QueryRunner) {}\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Executes operations when subject is being inserted.\n     */\n    async insert(subject: Subject): Promise<void> {\n        let parent = subject.metadata.treeParentRelation!.getEntityValue(\n            subject.entity!,\n        ) // if entity was attached via parent\n        if (!parent && subject.parentSubject && subject.parentSubject.entity)\n            // if entity was attached via children\n            parent = subject.parentSubject.insertedValueSet\n                ? subject.parentSubject.insertedValueSet\n                : subject.parentSubject.entity\n\n        const parentId = subject.metadata.getEntityIdMap(parent)\n\n        let parentPath: string = \"\"\n        if (parentId) {\n            parentPath = await this.getEntityPath(subject, parentId)\n        }\n\n        const insertedEntityId = subject.metadata\n            .treeParentRelation!.joinColumns.map((joinColumn) => {\n                return joinColumn.referencedColumn!.getEntityValue(\n                    subject.insertedValueSet!,\n                )\n            })\n            .join(\"_\")\n\n        await this.queryRunner.manager\n            .createQueryBuilder()\n            .update(subject.metadata.target)\n            .set({\n                [subject.metadata.materializedPathColumn!.propertyPath]:\n                    parentPath + insertedEntityId + \".\",\n            } as any)\n            .where(subject.identifier!)\n            .execute()\n    }\n\n    /**\n     * Executes operations when subject is being updated.\n     */\n    async update(subject: Subject): Promise<void> {\n        let newParent = subject.metadata.treeParentRelation!.getEntityValue(\n            subject.entity!,\n        ) // if entity was attached via parent\n        if (!newParent && subject.parentSubject && subject.parentSubject.entity)\n            // if entity was attached via children\n            newParent = subject.parentSubject.entity\n\n        let entity = subject.databaseEntity // if entity was attached via parent\n        if (!entity && newParent)\n            // if entity was attached via children\n            entity = subject.metadata\n                .treeChildrenRelation!.getEntityValue(newParent)\n                .find((child: any) => {\n                    return Object.entries(subject.identifier!).every(\n                        ([key, value]) => child[key] === value,\n                    )\n                })\n\n        const oldParent = subject.metadata.treeParentRelation!.getEntityValue(\n            entity!,\n        )\n        const oldParentId = this.getEntityParentReferencedColumnMap(\n            subject,\n            oldParent,\n        )\n        const newParentId = this.getEntityParentReferencedColumnMap(\n            subject,\n            newParent,\n        )\n\n        // Exit if the new and old parents are the same\n        if (OrmUtils.compareIds(oldParentId, newParentId)) {\n            return\n        }\n\n        let newParentPath: string = \"\"\n        if (newParentId) {\n            newParentPath = await this.getEntityPath(subject, newParentId)\n        }\n\n        let oldParentPath: string = \"\"\n        if (oldParentId) {\n            oldParentPath =\n                (await this.getEntityPath(subject, oldParentId)) || \"\"\n        }\n\n        const entityPath = subject.metadata\n            .treeParentRelation!.joinColumns.map((joinColumn) => {\n                return joinColumn.referencedColumn!.getEntityValue(entity!)\n            })\n            .join(\"_\")\n\n        const propertyPath =\n            subject.metadata.materializedPathColumn!.propertyPath\n        await this.queryRunner.manager\n            .createQueryBuilder()\n            .update(subject.metadata.target)\n            .set({\n                [propertyPath]: () =>\n                    `REPLACE(${this.queryRunner.connection.driver.escape(\n                        propertyPath,\n                    )}, '${oldParentPath}${entityPath}.', '${newParentPath}${entityPath}.')`,\n            } as any)\n            .where(`${propertyPath} LIKE :path`, {\n                path: `${oldParentPath}${entityPath}.%`,\n            })\n            .execute()\n    }\n\n    private getEntityParentReferencedColumnMap(\n        subject: Subject,\n        entity: ObjectLiteral | undefined,\n    ): ObjectLiteral | undefined {\n        if (!entity) return undefined\n        return EntityMetadata.getValueMap(\n            entity,\n            subject.metadata\n                .treeParentRelation!.joinColumns.map(\n                    (column) => column.referencedColumn,\n                )\n                .filter((v) => v != null) as ColumnMetadata[],\n            { skipNulls: true },\n        )\n    }\n\n    private getEntityPath(\n        subject: Subject,\n        id: ObjectLiteral,\n    ): Promise<string> {\n        const metadata = subject.metadata\n        const normalized = (Array.isArray(id) ? id : [id]).map((id) =>\n            metadata.ensureEntityIdMap(id),\n        )\n        return this.queryRunner.manager\n            .createQueryBuilder()\n            .select(\n                subject.metadata.targetName +\n                    \".\" +\n                    subject.metadata.materializedPathColumn!.propertyPath,\n                \"path\",\n            )\n            .from(subject.metadata.target, subject.metadata.targetName)\n            .where(\n                new Brackets((qb) => {\n                    for (const data of normalized) {\n                        qb.orWhere(new Brackets((qb) => qb.where(data)))\n                    }\n                }),\n            )\n            .getRawOne()\n            .then((result) => (result ? result[\"path\"] : \"\"))\n    }\n}\n"], "sourceRoot": "../.."}