{"version": 3, "file": "scrollbarFilter.js", "sourceRoot": "", "sources": ["../../src/interaction/scrollbarFilter.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;AAE9C,MAAM,CAAC,MAAM,oBAAoB,GAAG,cAAc,CAAC;AAEnD,MAAM,UAAU,eAAe,CAAC,UAAe,EAAE;IAC/C,OAAO,CAAC,OAAO,EAAE,CAAC,EAAE,OAAO,EAAE,EAAE;QAC7B,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC;QACpC,MAAM,UAAU,GAAG,SAAS,CAAC,sBAAsB,CAAC,oBAAoB,CAAC,CAAC;QAC1E,IAAI,CAAC,UAAU,CAAC,MAAM;YAAE,OAAO,GAAG,EAAE,GAAE,CAAC,CAAC;QACxC,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC;QACvB,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC;QAEvC,wDAAwD;QACxD,MAAM,UAAU,GAAG;YACjB,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC,MAAM,CAAC;YAClC,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC,MAAM,CAAC;SACnC,CAAC;QAEF,uCAAuC;QACvC,MAAM,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,UAAU,EAAE,CAAC,cAAc,EAAE,CAAC,CAAC;QAC9D,MAAM,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,UAAU,EAAE,CAAC,cAAc,EAAE,CAAC,CAAC;QAE9D,MAAM,WAAW,GAAG,YAAY,iCAC3B,OAAO,KACV,UAAU,EACV,SAAS,EAAE,oBAAoB,EAC/B,MAAM,EAAE,WAAW,EACnB,QAAQ,EAAE,IAAI,EACd,QAAQ,EAAE,CAAC,SAAS,EAAE,MAAM,EAAE,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAC9D,aAAa,EAAE,CAAC,SAAS,EAAE,EAAE;gBAC3B,MAAM,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC;gBAClD,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC;oBAAE,OAAO,MAAM,CAAC;YACrC,CAAC,IACD,CAAC;QACH,OAAO,WAAW,CAAC,OAAO,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;IAC1C,CAAC,CAAC;AACJ,CAAC"}