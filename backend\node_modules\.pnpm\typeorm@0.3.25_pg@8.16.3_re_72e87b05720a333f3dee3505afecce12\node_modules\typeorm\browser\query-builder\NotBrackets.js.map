{"version": 3, "sources": ["../browser/src/query-builder/NotBrackets.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAA;AAErC;;;GAGG;AACH,MAAM,OAAO,WAAY,SAAQ,QAAQ;IAAzC;;QACa,mBAAa,GAAG,MAAM,CAAC,GAAG,CAAC,aAAa,CAAC,CAAA;IACtD,CAAC;CAAA", "file": "NotBrackets.js", "sourcesContent": ["import { Brackets } from \"./Brackets\"\n\n/**\n * Syntax sugar.\n * Allows to use negate brackets in WHERE expressions for better syntax.\n */\nexport class NotBrackets extends Brackets {\n    readonly \"@instanceof\" = Symbol.for(\"NotBrackets\")\n}\n"], "sourceRoot": ".."}