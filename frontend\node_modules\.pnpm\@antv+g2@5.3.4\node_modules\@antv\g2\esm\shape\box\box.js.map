{"version": 3, "file": "box.js", "sourceRoot": "", "sources": ["../../../src/shape/box/box.ts"], "names": [], "mappings": ";;;;;;;;;;;AAAA,OAAO,EAAE,IAAI,IAAI,MAAM,EAAE,MAAM,sBAAsB,CAAC;AAEtD,OAAO,EAAE,UAAU,EAAE,MAAM,UAAU,CAAC;AACtC,OAAO,EAAE,MAAM,EAAE,MAAM,uBAAuB,CAAC;AAE/C,OAAO,EAAE,OAAO,EAAE,MAAM,wBAAwB,CAAC;AACjD,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAItD,SAAS,OAAO,CAAC,MAAiB,EAAE,UAAsB;IACxD,MAAM,IAAI,GAAG,MAAM,EAAE,CAAC;IAEtB,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;QACxB,IAAI,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,IAAI,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAE1B,IAAI,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,IAAI,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAE1B,IAAI,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,IAAI,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,IAAI,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,IAAI,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,IAAI,CAAC,SAAS,EAAE,CAAC;QAEjB,IAAI,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,IAAI,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAE1B,IAAI,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;QAC3B,IAAI,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;QAE3B,IAAI,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;QAC3B,IAAI,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;KAC5B;SAAM;QACL,uBAAuB;QACvB,MAAM,MAAM,GAAG,UAAU,CAAC,SAAS,EAAE,CAAC;QACtC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC;QACtB,MAAM,UAAU,GAAG,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;QACjD,MAAM,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;QAE/C,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3C,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QACzC,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7C,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;QAC1C,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;QAE3C,IAAI,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;QACjD,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;QAEvD,IAAI,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,IAAI,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAE1B,IAAI,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC,CAAC,SAAS;QACzD,IAAI,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;QACpC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC,CAAC,SAAS;QAC/D,IAAI,CAAC,SAAS,EAAE,CAAC;QAEjB,IAAI,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,YAAY,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC,CAAC,SAAS;QAC7D,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,YAAY,EAAE,QAAQ,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC,CAAC,SAAS;QAEnE,IAAI,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;QAC3B,IAAI,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;QAE3B,IAAI,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;QAC3B,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC,CAAC,WAAW;QAC5D,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC,CAAC,WAAW;KACnE;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,MAAM,CAAC,MAAM,GAAG,GAAmB,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;IACtD,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC;IACzC,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE;QACjC,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,KAAK,CAAC;QACnC,MAAM,EACJ,KAAK,EAAE,YAAY,EACnB,IAAI,GAAG,YAAY,EACnB,MAAM,GAAG,YAAY,KAEnB,QAAQ,EADP,IAAI,UACL,QAAQ,EALN,2BAKL,CAAW,CAAC;QAEb,MAAM,IAAI,GAAG,OAAO,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;QAEzC,OAAO,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;aAC9C,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;aACtB,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC;aAC3B,KAAK,CAAC,QAAQ,EAAE,MAAM,CAAC;aACvB,KAAK,CAAC,MAAM,EAAE,KAAK,IAAI,IAAI,CAAC;aAC5B,KAAK,CAAC,WAAW,EAAE,SAAS,CAAC;aAC7B,IAAI,CAAC,UAAU,EAAE,OAAO,CAAC;aACzB,IAAI,EAAE,CAAC;IACZ,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF,GAAG,CAAC,KAAK,GAAG;IACV,aAAa,EAAE,OAAO;IACtB,qBAAqB,EAAE,QAAQ;IAC/B,sBAAsB,EAAE,UAAU;IAClC,oBAAoB,EAAE,SAAS;CAChC,CAAC"}