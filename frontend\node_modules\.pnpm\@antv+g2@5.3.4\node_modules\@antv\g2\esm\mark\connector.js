import { ConnectorShape } from '../shape';
import { baseAnnotationChannels, basePostInference, basePreInference, } from './utils';
import { Link } from './link';
const shape = {
    connector: ConnectorShape,
};
export const Connector = (...args) => {
    return Link(...args);
};
Connector.props = {
    defaultShape: 'connector',
    defaultLabelShape: 'label',
    composite: false,
    shape,
    channels: [
        ...baseAnnotationChannels({ shapes: Object.keys(shape) }),
        { name: 'x', required: true },
        { name: 'y', required: true },
    ],
    preInference: [...basePreInference()],
    postInference: [...basePostInference()],
};
//# sourceMappingURL=connector.js.map