{"version": 3, "sources": ["../browser/src/platform/BrowserPlatformTools.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AACH,OAAO,EAAE,MAAM,EAAE,MAAM,QAAQ,CAAC;AAEhC,MAAM,OAAO,aAAa;IAOtB;;OAEG;IACH,MAAM,CAAC,iBAAiB;QACpB,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;YAChC,OAAO,MAAM,CAAA;QACjB,CAAC;aAAM,CAAC;YACJ,uCAAuC;YACvC,OAAO,MAAM,CAAA;QACjB,CAAC;IACL,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,IAAI,CAAC,IAAY;QACpB,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS;YACvB,MAAM,IAAI,KAAK,CAAC,gGAAgG,IAAI,KAAK,CAAC,CAAC;QAE/H,OAAO,EAAE,CAAC;IACd,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,aAAa,CAAC,OAAe;QAChC,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS;YACvB,MAAM,IAAI,KAAK,CAAC,uGAAuG,OAAO,KAAK,CAAC,CAAC;QAEzI,OAAO,EAAE,CAAC;IACd,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,WAAW,CAAC,OAAe;QAC9B,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS;YACvB,MAAM,IAAI,KAAK,CAAC,qGAAqG,OAAO,KAAK,CAAC,CAAC;QAEvI,OAAO,EAAE,CAAC;IACd,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,WAAW,CAAC,OAAe;QAC9B,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS;YACvB,MAAM,IAAI,KAAK,CAAC,qGAAqG,OAAO,KAAK,CAAC,CAAC;QAEvI,OAAO,EAAE,CAAC;IACd,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,SAAS,CAAC,OAAe;QAC5B,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS;YACvB,MAAM,IAAI,KAAK,CAAC,sGAAsG,OAAO,KAAK,CAAC,CAAC;QAExI,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,MAAM,CAAC,MAAM,CAAC,OAAe;QACzB,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS;YACvB,MAAM,IAAI,KAAK,CAAC,8GAA8G,OAAO,OAAO,CAAC,CAAC;IACtJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,cAAc,CAAC,IAAY;QAC9B,+BAA+B;QAC/B,sIAAsI;QACtI,OAAO,SAAS,CAAC;IACrB,CAAC;IAED,MAAM,CAAC,YAAY,CAAC,QAAgB;QAChC,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS;YACvB,MAAM,IAAI,KAAK,CAAC,wGAAwG,QAAQ,KAAK,CAAC,CAAC;QAC3I,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,cAAc,CAAC,QAAgB,EAAE,IAAS;QAC7C,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS;YACvB,MAAM,IAAI,KAAK,CAAC,0GAA0G,QAAQ,KAAK,CAAC,CAAC;IACjJ,CAAC;IAED,MAAM,CAAC,SAAS,CAAC,IAAY,EAAE,IAAS;QACpC,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS;YACvB,MAAM,IAAI,KAAK,CAAC,qGAAqG,IAAI,KAAK,CAAC,CAAC;QACpI,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,YAAY,CAAC,GAAW;QAC3B,OAAO,GAAG,CAAC;IACf,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,OAAO,CAAC,MAAc,EAAE,IAAS;QACpC,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG,EAAE,IAAI,CAAC,CAAC;IACrC,CAAC;IAED,MAAM,CAAC,QAAQ,CAAC,MAAc,EAAE,KAAU;QACtC,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,EAAE,KAAK,CAAC,CAAC;IACvC,CAAC;IAED,MAAM,CAAC,OAAO,CAAC,MAAc,EAAE,OAAY;QACvC,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG,EAAE,OAAO,CAAC,CAAC;IACxC,CAAC;IAED,MAAM,CAAC,GAAG,CAAC,OAAe;QACtB,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IACzB,CAAC;IAED,MAAM,CAAC,IAAI,CAAC,OAAe;QACvB,OAAO,OAAO,CAAC;IACnB,CAAC;;AA/HD;;GAEG;AACI,kBAAI,GAAqB,SAAS,CAAC;AA+H9C;;;GAGG;AACH,MAAM,OAAO,YAAY;CAAG;AAE5B,MAAM,OAAO,QAAQ;CAAG;AAExB,MAAM,OAAO,QAAQ;CAAG;AAYxB,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;IAChC,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC;AAC3B,CAAC;AACD,uCAAuC;AACvC,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE,CAAC;IAClE,MAAM,CAAC,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC;AAC9C,CAAC", "file": "BrowserPlatformTools.js", "sourcesContent": ["/**\n * <PERSON><PERSON><PERSON>'s implementation of the platform-specific tools.\n *\n * This file gonna replace PlatformTools for browser environment.\n * For node.js environment this class is not getting packaged.\n * Don't use methods of this class in the code, use PlatformTools methods instead.\n */\nimport { <PERSON><PERSON><PERSON> } from \"buffer\";\n\nexport class PlatformTools {\n\n    /**\n     * Type of the currently running platform.\n     */\n    static type: \"browser\"|\"node\" = \"browser\";\n\n    /**\n     * Gets global variable where global stuff can be stored.\n     */\n    static getGlobalVariable(): any {\n        if (typeof window !== \"undefined\") {\n            return window\n        } else {\n            // NativeScript uses global, not window\n            return global\n        }\n    }\n\n    /**\n     * Loads (\"require\"-s) given file or package.\n     * This operation only supports on node platform\n     */\n    static load(name: string): any {\n        if (this.type === \"browser\")\n            throw new Error(`This option/function is not supported in the browser environment. Failed operation: require(\"${name}\").`);\n\n        return \"\";\n    }\n\n    /**\n     * Normalizes given path. Does \"path.normalize\".\n     */\n    static pathNormalize(pathStr: string): string {\n        if (this.type === \"browser\")\n            throw new Error(`This option/function is not supported in the browser environment. Failed operation: path.normalize(\"${pathStr}\").`);\n\n        return \"\";\n    }\n\n    /**\n     * Gets file extension. Does \"path.extname\".\n     */\n    static pathExtname(pathStr: string): string {\n        if (this.type === \"browser\")\n            throw new Error(`This option/function is not supported in the browser environment. Failed operation: path.extname(\"${pathStr}\").`);\n\n        return \"\";\n    }\n\n    /**\n     * Resolved given path. Does \"path.resolve\".\n     */\n    static pathResolve(pathStr: string): string {\n        if (this.type === \"browser\")\n            throw new Error(`This option/function is not supported in the browser environment. Failed operation: path.resolve(\"${pathStr}\").`);\n\n        return \"\";\n    }\n\n    /**\n     * Synchronously checks if file exist. Does \"fs.existsSync\".\n     */\n    static fileExist(pathStr: string): boolean {\n        if (this.type === \"browser\")\n            throw new Error(`This option/function is not supported in the browser environment. Failed operation: fs.existsSync(\"${pathStr}\").`);\n\n        return false;\n    }\n\n    static dotenv(pathStr: string): void {\n        if (this.type === \"browser\")\n            throw new Error(`This option/function is not supported in the browser environment. Failed operation: dotenv.config({ path: \"${pathStr}\" }).`);\n    }\n\n    /**\n     * Gets environment variable.\n     */\n    static getEnvVariable(name: string): any {\n        // if (this.type === \"browser\")\n        //     throw new Error(`This option/function is not supported in the browser environment. Failed operation: process.env[\"${name}\"].`);\n        return undefined;\n    }\n\n    static readFileSync(filename: string): any {\n        if (this.type === \"browser\")\n            throw new Error(`This option/function is not supported in the browser environment. Failed operation: fs.readFileSync(\"${filename}\").`);\n        return null;\n    }\n\n    static appendFileSync(filename: string, data: any) {\n        if (this.type === \"browser\")\n            throw new Error(`This option/function is not supported in the browser environment. Failed operation: fs.appendFileSync(\"${filename}\").`);\n    }\n\n    static writeFile(path: string, data: any): Promise<void> {\n        if (this.type === \"browser\")\n            throw new Error(`This option/function is not supported in the browser environment. Failed operation: fs.writeFile(\"${path}\").`);\n        return Promise.reject(null);\n    }\n\n    /**\n     * Highlights sql string to be printed in the console.\n     */\n    static highlightSql(sql: string) {\n        return sql;\n    }\n\n    /**\n     * Logging functions needed by AdvancedConsoleLogger (but here without ANSI colors)\n     */\n    static logInfo(prefix: string, info: any) {\n        console.info(prefix + \" \", info);\n    }\n\n    static logError(prefix: string, error: any) {\n        console.error(prefix + \" \", error);\n    }\n\n    static logWarn(prefix: string, warning: any) {\n        console.warn(prefix + \" \", warning);\n    }\n\n    static log(message: string) {\n        console.log(message);\n    }\n\n    static warn(message: string) {\n        return message;\n    }\n}\n\n/**\n * These classes are needed for stream operations or\n * in the mongodb driver. Both aren't supported in the browser.\n */\nexport class EventEmitter {}\n\nexport class Readable {}\n\nexport class Writable {}\n\nexport interface ReadStream {}\n\n/**\n * This loads a polyfill to enable Buffers in the browser.\n */\ninterface Window {\n    Buffer: any;\n}\n\ndeclare var window: Window;\nif (typeof window !== \"undefined\") {\n    window.Buffer = Buffer;\n}\n// NativeScript uses global, not window\nif (typeof global !== \"undefined\" && typeof require !== \"undefined\") {\n    global.Buffer = require(\"buffer/\").Buffer;\n}\n"], "sourceRoot": ".."}