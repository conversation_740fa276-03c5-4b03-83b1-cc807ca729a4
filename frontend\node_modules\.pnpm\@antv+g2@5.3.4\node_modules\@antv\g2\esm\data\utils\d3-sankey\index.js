/**
 * Sankey layout, fork from https://github.com/d3/d3-sankey/tree/master/src (todo, optimize algorithm)
 *
 * Major changes:
 *
 * 1. modify some set and map traverse
 * 2. fix some error caused by [empty] array
 * 3. support `nodeDepth` through align method
 */
import { Sankey as sankey } from './sankey';
export { center, left, right, justify } from './align';
export { sankey };
//# sourceMappingURL=index.js.map