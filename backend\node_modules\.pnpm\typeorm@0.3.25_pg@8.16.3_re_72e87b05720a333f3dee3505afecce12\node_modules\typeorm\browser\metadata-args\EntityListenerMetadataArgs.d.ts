import { EventListenerType } from "../metadata/types/EventListenerTypes";
/**
 * Arguments for EntityListenerMetadata class.
 */
export interface EntityListenerMetadataArgs {
    /**
     * Class to which listener is applied.
     */
    readonly target: Function;
    /**
     * Class's property name to which listener is applied.
     */
    readonly propertyName: string;
    /**
     * The type of the listener.
     */
    readonly type: EventListenerType;
}
