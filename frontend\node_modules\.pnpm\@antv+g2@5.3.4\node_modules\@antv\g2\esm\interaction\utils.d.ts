import { DisplayObject, AABB } from '@antv/g';
import type { PathArray } from '@antv/util';
import { G2ViewDescriptor } from '../runtime';
/**
 * Given root of chart returns elements to be manipulated
 */
export declare function selectG2Elements(root: DisplayObject): DisplayObject[];
export declare function selectFacetG2Elements(target: any, viewInstances: any): DisplayObject[];
export declare function selectFacetViews(target: any, viewInstances: any): any;
export declare function selectPlotArea(root: DisplayObject): DisplayObject;
export declare function bboxOf(element: DisplayObject): AABB;
export declare function mousePosition(target: any, event: any): number[];
/**
 * @todo Pass bbox rather than calc it here.
 */
export declare function brushMousePosition(target: any, event: any): number[];
export declare function boundsOfBrushArea(target: any): any[];
export declare function createColorKey(view: any): (element: any) => any;
export declare function createXKey(view: any): (element: any) => any;
export declare function createDatumof(view: G2ViewDescriptor | G2ViewDescriptor[]): (element: any) => any;
export declare function createUseState(style: Record<string, any>, elements: Element[]): (valueof?: (d: any, element: any) => any, setAttribute?: (element: DisplayObject<any, any>, k: string, v: string) => void) => {
    setState: (element: any, ...states: any[]) => void;
    updateState: (element: any, ...states: any[]) => void;
    removeState: (element: any, ...states: any[]) => void;
    hasState: (element: any, state: any) => boolean;
};
export declare function useState(style: Record<string, any> | undefined, valueof?: (d: any, element: any) => any, setAttribute?: (element: DisplayObject<any, any>, k: string, v: string) => void): {
    setState: (element: any, ...states: any[]) => void;
    updateState: (element: any, ...states: any[]) => void;
    removeState: (element: any, ...states: any[]) => void;
    hasState: (element: any, state: any) => boolean;
};
export declare function mergeState(options: any, states: any): {};
export declare function createValueof(elements: any, datum: any): (d: any, e: any) => any;
export declare function renderLink({ link, valueof, coordinate, ...style }: {
    [x: string]: any;
    link?: boolean;
    valueof?: (d: any, element: any) => any;
    coordinate: any;
}): (() => void)[] | readonly [(elements: any) => void, (element: any) => void];
export declare function offsetTransform(element: any, offset: any, coordinate: any): any;
export declare function renderBackground({ document, background, scale, coordinate, valueof, ...rest }: {
    [x: string]: any;
    document: any;
    background: any;
    scale: any;
    coordinate: any;
    valueof: any;
}): (() => void)[] | readonly [(element: any) => void, (element: any) => void, (element: any) => boolean];
export declare function setCursor(root: any, cursor: any): void;
export declare function restoreCursor(root: any): void;
export declare function selectElementByData(elements: any, data: any, datum: any): any;
export declare function getPointsR(point: number[], nextPoint: number[]): number;
export declare function getPointsPath(points: number[][], isClose?: boolean): PathArray;
export declare function getElements(plot: any): any;
export declare function getThetaPath(center: number[], points: number[][], isBig?: number): PathArray;
export declare function maybeRoot(node: any, rootOf: any): any;
export declare const VALID_FIND_BY_X_MARKS: string[];
/**
 * @description Create function that can find element by event.
 * @returns Element find function.
 */
export declare function createFindElementByEvent({ elementsof, root, coordinate, scale, validFindByXMarks, }: {
    elementsof: any;
    root: any;
    coordinate: any;
    scale: any;
    validFindByXMarks?: string[];
}): (event: any) => any;
