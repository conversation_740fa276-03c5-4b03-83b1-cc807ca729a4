{"version": 3, "sources": ["../browser/src/metadata/EntityMetadata.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,4BAA4B,EAAE,MAAM,uCAAuC,CAAA;AAIpF,OAAO,EAAE,QAAQ,EAAE,MAAM,kBAAkB,CAAA;AAe3C,OAAO,EAAE,2BAA2B,EAAE,MAAM,sCAAsC,CAAA;AAClF,OAAO,EAAE,WAAW,EAAE,MAAM,qBAAqB,CAAA;AACjD,OAAO,EAAE,OAAO,EAAE,MAAM,qBAAqB,CAAA;AAE7C;;GAEG;AACH,MAAM,OAAO,cAAc;IA8evB,wEAAwE;IACxE,cAAc;IACd,wEAAwE;IAExE,YAAY,OAOX;QAxfQ,mBAAa,GAAG,MAAM,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAA;QA+BrD;;WAEG;QACH,yBAAoB,GAAqB,EAAE,CAAA;QAE3C;;;;WAIG;QACH,oBAAe,GAAe,EAAE,CAAA;QAEhC;;WAEG;QACH,cAAS,GAAc,SAAS,CAAA;QAiChC;;WAEG;QACH,iBAAY,GAAa,KAAK,CAAA;QA8B9B;;WAEG;QACH,gBAAW,GAAY,IAAI,CAAA;QA4B3B;;WAEG;QACH,4BAAuB,GAAY,KAAK,CAAA;QAExC;;;;;WAKG;QACH,eAAU,GAAY,KAAK,CAAA;QAE3B;;;WAGG;QACH,6BAAwB,GAAY,IAAI,CAAA;QAYxC;;;WAGG;QACH,sBAAiB,GAAY,KAAK,CAAA;QAElC;;WAEG;QACH,2BAAsB,GAAY,KAAK,CAAA;QAEvC;;WAEG;QACH,4BAAuB,GAAY,KAAK,CAAA;QAQxC;;WAEG;QACH,eAAU,GAAqB,EAAE,CAAA;QAEjC;;WAEG;QACH,YAAO,GAAqB,EAAE,CAAA;QAE9B;;WAEG;QACH,oBAAe,GAAqB,EAAE,CAAA;QAEtC;;WAEG;QACH,sBAAiB,GAAqB,EAAE,CAAA;QAExC;;WAEG;QACH,sBAAiB,GAAqB,EAAE,CAAA;QAExC;;;WAGG;QACH,iBAAY,GAAqB,EAAE,CAAA;QAEnC;;;WAGG;QACH,mBAAc,GAAqB,EAAE,CAAA;QAErC;;WAEG;QACH,qBAAgB,GAAqB,EAAE,CAAA;QAuDvC;;WAEG;QACH,mBAAc,GAAqB,EAAE,CAAA;QAErC;;WAEG;QACH,iBAAY,GAAuB,EAAE,CAAA;QAErC;;WAEG;QACH,cAAS,GAAuB,EAAE,CAAA;QAElC;;WAEG;QACH,mBAAc,GAAuB,EAAE,CAAA;QAEvC;;WAEG;QACH,kBAAa,GAAuB,EAAE,CAAA;QAEtC;;WAEG;QACH,sBAAiB,GAAuB,EAAE,CAAA;QAE1C;;WAEG;QACH,2BAAsB,GAAuB,EAAE,CAAA;QAE/C;;WAEG;QACH,uBAAkB,GAAuB,EAAE,CAAA;QAE3C;;WAEG;QACH,uBAAkB,GAAuB,EAAE,CAAA;QAE3C;;WAEG;QACH,wBAAmB,GAAuB,EAAE,CAAA;QAE5C;;WAEG;QACH,6BAAwB,GAAuB,EAAE,CAAA;QAEjD;;WAEG;QACH,6BAAwB,GAAuB,EAAE,CAAA;QAYjD;;WAEG;QACH,gBAAW,GAAyB,EAAE,CAAA;QAEtC;;WAEG;QACH,mBAAc,GAA4B,EAAE,CAAA;QAE5C;;WAEG;QACH,gBAAW,GAAyB,EAAE,CAAA;QAEtC;;WAEG;QACH,cAAS,GAAuB,EAAE,CAAA;QAElC;;WAEG;QACH,iBAAY,GAAuB,EAAE,CAAA;QAErC;;WAEG;QACH,eAAU,GAAoB,EAAE,CAAA;QAEhC;;WAEG;QACH,YAAO,GAAoB,EAAE,CAAA;QAE7B;;WAEG;QACH,YAAO,GAAqB,EAAE,CAAA;QAE9B;;WAEG;QACH,eAAU,GAAqB,EAAE,CAAA;QAEjC;;WAEG;QACH,WAAM,GAAoB,EAAE,CAAA;QAE5B;;WAEG;QACH,eAAU,GAAwB,EAAE,CAAA;QAEpC;;WAEG;QACH,iBAAY,GAA6B,EAAE,CAAA;QAE3C;;WAEG;QACH,cAAS,GAA6B,EAAE,CAAA;QAExC;;WAEG;QACH,uBAAkB,GAA6B,EAAE,CAAA;QAEjD;;WAEG;QACH,0BAAqB,GAA6B,EAAE,CAAA;QAEpD;;WAEG;QACH,yBAAoB,GAA6B,EAAE,CAAA;QAEnD;;WAEG;QACH,0BAAqB,GAA6B,EAAE,CAAA;QAEpD;;WAEG;QACH,yBAAoB,GAA6B,EAAE,CAAA;QAEnD;;WAEG;QACH,0BAAqB,GAA6B,EAAE,CAAA;QAEpD;;WAEG;QACH,8BAAyB,GAA6B,EAAE,CAAA;QAExD;;WAEG;QACH,2BAAsB,GAA6B,EAAE,CAAA;QAErD;;WAEG;QACH,yBAAoB,GAA6B,EAAE,CAAA;QAEnD;;WAEG;QACH,6BAAwB,GAA6B,EAAE,CAAA;QAEvD;;WAEG;QACH,0BAAqB,GAA6B,EAAE,CAAA;QA4BhD,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAA;QACpC,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,eAAe,IAAI,EAAE,CAAA;QACpD,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,CAAA;QACpD,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAA;QACtE,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,SAAS;YAChC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO;YAC3B,CAAC,CAAC,SAAS,CAAA;QACf,IAAI,CAAC,2BAA2B,GAAG,OAAO,CAAC,2BAA4B,CAAA;QACvE,IAAI,CAAC,iBAAiB,GAAG,OAAO,CAAC,IAAI,CAAA;QACrC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAA;QAC3C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAA;QAC5C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAA;QACnD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAA;QACvD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAA;IACrD,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,MAAM,CACF,WAAyB,EACzB,OAAwD;QAExD,MAAM,IAAI,GAAG,OAAO,IAAI,OAAO,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAA;QAC5D,iFAAiF;QACjF,IAAI,GAAQ,CAAA;QACZ,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,UAAU,IAAI,CAAC,IAAI,EAAE,CAAC;YAC7C,IAAI,CAAC,OAAO,EAAE,gBAAgB,IAAI,IAAI,CAAC,wBAAwB,EAAE,CAAC;gBAC9D,GAAG,GAAG,IAAU,IAAI,CAAC,MAAO,EAAE,CAAA;YAClC,CAAC;iBAAM,CAAC;gBACJ,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAA;YAC9C,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,6CAA6C;YAC7C,GAAG,GAAG,EAAE,CAAA;QACZ,CAAC;QAED,0BAA0B;QAC1B,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;YACnC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,UAAU,CAAA;QAC3D,CAAC;QAED,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE,CACpC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,QAAQ,EACR,GAAG,EACH,WAAW,CACd,CACJ,CAAA;QACD,OAAO,GAAG,CAAA;IACd,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAqB;QACvB,IAAI,CAAC,MAAM;YAAE,OAAO,KAAK,CAAA;QAEzB,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,aAAa,EAAE,EAAE;YAC/C,MAAM,KAAK,GAAG,aAAa,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;YAClD,OAAO,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,EAAE,CAAA;QAChE,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;;OAGG;IACH,iBAAiB,CAAC,MAAqB;QACnC,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,aAAa,EAAE,EAAE;YAC/C,MAAM,KAAK,GAAG,aAAa,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;YAClD,OAAO,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,CAAA;QAChD,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;;;;OAKG;IACH,iBAAiB,CAAC,EAAO;QACrB,IAAI,WAAW,CAAC,QAAQ,CAAC,EAAE,CAAC;YAAE,OAAO,EAAE,CAAA;QAEvC,IAAI,IAAI,CAAC,sBAAsB;YAC3B,MAAM,IAAI,4BAA4B,CAAC,IAAI,EAAE,EAAE,CAAC,CAAA;QAEpD,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,EAAE,CAAC,CAAA;IACpD,CAAC;IAED;;;;;OAKG;IACH,cAAc,CACV,MAAiC;QAEjC,IAAI,CAAC,MAAM;YAAE,OAAO,SAAS,CAAA;QAE7B,OAAO,cAAc,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,cAAc,EAAE;YAC3D,SAAS,EAAE,IAAI;SAClB,CAAC,CAAA;IACN,CAAC;IAED;;;;;OAKG;IACH,mBAAmB,CACf,MAAiC;QAEjC,IAAI,CAAC,MAAM;YAAE,OAAO,MAAM,CAAA;QAE1B,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;QACzC,IAAI,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC9B,OAAO,KAAK,CAAA;QAChB,CAAC;aAAM,IAAI,KAAK,EAAE,CAAC;YACf,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA,CAAC,0CAA0C;QAClG,CAAC;QAED,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;;OAGG;IACH,eAAe,CACX,WAA0B,EAC1B,YAA2B;QAE3B,MAAM,gBAAgB,GAAG,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QACzD,IAAI,CAAC,gBAAgB;YAAE,OAAO,KAAK,CAAA;QAEnC,MAAM,iBAAiB,GAAG,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,CAAA;QAC3D,IAAI,CAAC,iBAAiB;YAAE,OAAO,KAAK,CAAA;QAEpC,OAAO,QAAQ,CAAC,UAAU,CAAC,gBAAgB,EAAE,iBAAiB,CAAC,CAAA;IACnE,CAAC;IAED;;OAEG;IACH,0BAA0B,CACtB,YAAoB;QAEpB,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CACpB,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,YAAY,KAAK,YAAY,CACnD,CAAA;IACL,CAAC;IAED;;OAEG;IACH,0BAA0B,CACtB,YAAoB;QAEpB,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CACpB,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,YAAY,KAAK,YAAY,CACnD,CAAA;IACL,CAAC;IAED;;OAEG;IACH,yBAAyB,CAAC,YAAoB;QAC1C,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAC/B,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,YAAY,KAAK,YAAY,CACnD,CAAA;QACD,OAAO,SAAS,IAAI,IAAI,CAAC,2BAA2B,CAAC,YAAY,CAAC,CAAA;IACtE,CAAC;IAED;;OAEG;IACH,0BAA0B,CACtB,YAAoB;QAEpB,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAC5B,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,YAAY,KAAK,YAAY,CACnD,CAAA;QACD,IAAI,MAAM;YAAE,OAAO,MAAM,CAAA;QAEzB,yGAAyG;QACzG,2FAA2F;QAC3F,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAChC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,YAAY,KAAK,YAAY,CACvD,CAAA;QACD,IAAI,QAAQ,IAAI,QAAQ,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;YAC7C,OAAO,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAA;QAElC,OAAO,SAAS,CAAA;IACpB,CAAC;IAED;;;OAGG;IACH,gCAAgC,CAC5B,YAAoB;QAEpB,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CACpB,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,YAAY,KAAK,YAAY,CACnD,CAAA;IACL,CAAC;IAED;;;OAGG;IACH,2BAA2B,CAAC,YAAoB;QAC5C,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAC5B,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,YAAY,KAAK,YAAY,CACnD,CAAA;QACD,IAAI,MAAM;YAAE,OAAO,CAAC,MAAM,CAAC,CAAA;QAE3B,yGAAyG;QACzG,2FAA2F;QAC3F,MAAM,QAAQ,GAAG,IAAI,CAAC,4BAA4B,CAAC,YAAY,CAAC,CAAA;QAChE,IAAI,QAAQ,IAAI,QAAQ,CAAC,WAAW;YAAE,OAAO,QAAQ,CAAC,WAAW,CAAA;QAEjE,OAAO,EAAE,CAAA;IACb,CAAC;IAED;;OAEG;IACH,2BAA2B,CAAC,YAAoB;QAC5C,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CACtB,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,YAAY,KAAK,YAAY,CACvD,CAAA;IACL,CAAC;IAED;;OAEG;IACH,4BAA4B,CACxB,YAAoB;QAEpB,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CACtB,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,YAAY,KAAK,YAAY,CACvD,CAAA;IACL,CAAC;IAED;;OAEG;IACH,2BAA2B,CAAC,YAAoB;QAC5C,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CACzB,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,YAAY,KAAK,YAAY,CACvD,CAAA;IACL,CAAC;IAED;;OAEG;IACH,4BAA4B,CACxB,YAAoB;QAEpB,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CACzB,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,YAAY,KAAK,YAAY,CACvD,CAAA;IACL,CAAC;IAED;;OAEG;IACH,yBAAyB,CAAC,aAAuB;QAC7C,OAAO,aAAa,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,EAAE;YACtC,MAAM,MAAM,GAAG,IAAI,CAAC,0BAA0B,CAAC,YAAY,CAAC,CAAA;YAC5D,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;gBACjB,MAAM,IAAI,2BAA2B,CAAC,YAAY,EAAE,IAAI,CAAC,CAAA;YAC7D,CAAC;YACD,OAAO,MAAM,CAAA;QACjB,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;;OAGG;IACH,+BAA+B,CAC3B,MAAqB,EACrB,SAA6B;QAE7B,MAAM,kBAAkB,GAA8C,EAAE,CAAA;QACxE,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;YAC3B,MAAM,KAAK,GAAG,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;YAC7C,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;gBACvB,KAAK,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE,CACvB,kBAAkB,CAAC,IAAI,CAAC;oBACpB,QAAQ;oBACR,QAAQ;oBACR,cAAc,CAAC,wBAAwB,CACnC,QAAQ,EACR,QAAQ,CACX;iBACJ,CAAC,CACL,CAAA;YACL,CAAC;iBAAM,IAAI,KAAK,EAAE,CAAC;gBACf,kBAAkB,CAAC,IAAI,CAAC;oBACpB,QAAQ;oBACR,KAAK;oBACL,cAAc,CAAC,wBAAwB,CAAC,KAAK,EAAE,QAAQ,CAAC;iBAC3D,CAAC,CAAA;YACN,CAAC;QACL,CAAC,CAAC,CAAA;QACF,OAAO,kBAAkB,CAAA;IAC7B,CAAC;IAED;;;;;;;OAOG;IACH,uBAAuB,CAAC,KAAU;QAC9B,iFAAiF;QACjF,yEAAyE;QACzE,oEAAoE;QAEpE,IACI,IAAI,CAAC,kBAAkB,KAAK,KAAK;YACjC,IAAI,CAAC,oBAAoB,CAAC,MAAM,GAAG,CAAC,EACtC,CAAC;YACC,2FAA2F;YAC3F,IAAI,6BAAsC,CAAA;YAC1C,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBAC3B,6BAA6B;oBACzB,KAAK,CAAC,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAA;YACpD,CAAC;YACD,OAAO,CACH,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAC1B,CAAC,IAAI,EAAE,EAAE,CACL,6BAA6B;gBACzB,IAAI,CAAC,kBAAkB;gBAC3B,KAAK,CAAC,WAAW,KAAK,IAAI,CAAC,MAAM,CACxC,IAAI,IAAI,CACZ,CAAA;QACL,CAAC;QACD,OAAO,IAAI,CAAA;IACf,CAAC;IAED,4EAA4E;IAC5E,yBAAyB;IACzB,4EAA4E;IAEpE,MAAM,CAAC,wBAAwB,CACnC,KAAU,EACV,QAA0B;QAE1B,OAAO,QAAQ,CAAC,qBAAqB,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAA;IACxE,CAAC;IAED,4EAA4E;IAC5E,wBAAwB;IACxB,4EAA4E;IAE5E;;;;OAIG;IACH,MAAM,CAAC,kBAAkB,CACrB,QAAwB,EACxB,MAAqB,EACrB,SAAiB,EAAE;QAEnB,MAAM,KAAK,GAAa,EAAE,CAAA;QAC1B,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;YAChC,iHAAiH;YACjH,6DAA6D;YAC7D,MAAM,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;YACpD,IAAI,QAAQ,CAAC,2BAA2B,CAAC,UAAU,CAAC,EAAE,CAAC;gBACnD,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CACpC,QAAQ,EACR,MAAM,CAAC,GAAG,CAAC,EACX,UAAU,CACb,CAAA;gBACD,KAAK,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,CAAA;YAC3B,CAAC;iBAAM,CAAC;gBACJ,MAAM,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;gBAC9C,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACpB,CAAC;QACL,CAAC,CAAC,CAAA;QACF,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,UAAU,CACb,WAA4B,EAC5B,YAA6B;QAE7B,OAAO,WAAW,CAAC,MAAM,CAAC,CAAC,UAAU,EAAE,EAAE;YACrC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,EAAE,CACtC,QAAQ,CAAC,UAAU,CAAC,UAAU,EAAE,WAAW,CAAC,CAC/C,CAAA;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,WAAW,CACd,MAAqB,EACrB,OAAyB,EACzB,OAAiC;QAEjC,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;YAClC,MAAM,KAAK,GAAG,MAAM,CAAC,iBAAiB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;YAEvD,mEAAmE;YACnE,IAAI,GAAG,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS;gBAC1D,OAAO,SAAS,CAAA;YAEpB,OAAO,QAAQ,CAAC,SAAS,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;QACzC,CAAC,EAAE,EAA+B,CAAC,CAAA;IACvC,CAAC;IAED,wEAAwE;IACxE,yBAAyB;IACzB,wEAAwE;IAExE,KAAK;QACD,MAAM,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAA;QACrD,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,YAAY,CAAA;QACzD,MAAM,qBAAqB,GACvB,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,qBAAqB,CAAA;QAEjD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAA;QAC3C,IAAI,CAAC,QAAQ;YACT,IAAI,CAAC,iBAAiB,CAAC,IAAI,KAAK,cAAc;gBAC9C,IAAI,CAAC,oBAAoB;gBACrB,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,QAAQ;gBACpC,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAA;QACzC,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC;YAChC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAA;QAC/C,CAAC;aAAM,IACH,IAAI,CAAC,iBAAiB,CAAC,IAAI,KAAK,cAAc;YAC9C,IAAI,CAAC,oBAAoB,EAC3B,CAAC;YACC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAA;QAClD,CAAC;aAAM,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,cAAc,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC3D,IAAI,CAAC,MAAM,GAAI,IAAI,CAAC,UAAU,CAAC,OAAe,CAAC,MAAM,CAAA;QACzD,CAAC;QACD,IAAI,CAAC,cAAc;YACf,IAAI,CAAC,iBAAiB,CAAC,IAAI,KAAK,cAAc;gBAC9C,IAAI,CAAC,oBAAoB;gBACrB,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,cAAc;gBAC1C,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAA;QACrC,IAAI,CAAC,WAAW;YACZ,IAAI,CAAC,iBAAiB,CAAC,WAAW,KAAK,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAA;QAC/D,IAAI,CAAC,UAAU;YACX,OAAO,IAAI,CAAC,iBAAiB,CAAC,MAAM,KAAK,UAAU;gBAC/C,CAAC,CAAE,IAAI,CAAC,iBAAiB,CAAC,MAAc,CAAC,IAAI;gBAC7C,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAA;QACvC,IAAI,IAAI,CAAC,iBAAiB,CAAC,IAAI,KAAK,kBAAkB,EAAE,CAAC;YACrD,IAAI,CAAC,sBAAsB;gBACvB,cAAc,CAAC,wBAAwB,CAAC,IAAI,CAAC,cAAe,CAAC,CAAA;QACrE,CAAC;aAAM,IACH,IAAI,CAAC,iBAAiB,CAAC,IAAI,KAAK,cAAc;YAC9C,IAAI,CAAC,oBAAoB,EAC3B,CAAC;YACC,IAAI,CAAC,sBAAsB,GAAG,cAAc,CAAC,SAAS,CAClD,IAAI,CAAC,oBAAoB,CAAC,UAAU,EACpC,IAAI,CAAC,oBAAoB,CAAC,cAAc,CAC3C,CAAA;QACL,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,sBAAsB,GAAG,cAAc,CAAC,SAAS,CAClD,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,cAAc,CACtB,CAAA;YAED,IACI,IAAI,CAAC,iBAAiB,CAAC,IAAI,KAAK,UAAU;gBAC1C,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc;gBACrC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,GAAG,CAAC;gBACzC,IAAI,CAAC,sBAAsB,CAAC,MAAM;oBAC9B,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,EAC3C,CAAC;gBACC,oFAAoF;gBACpF,mFAAmF;gBACnF,gEAAgE;gBAChE,IAAI,CAAC,sBAAsB,GAAG,OAAO,CACjC,IAAI,CAAC,sBAAsB,EAC3B,EAAE,SAAS,EAAE,GAAG,EAAE,aAAa,EAAE,CAAC,EAAE,CACvC,CAAA;YACL,CAAC;QACL,CAAC;QACD,IAAI,CAAC,SAAS,GAAG,YAAY;YACzB,CAAC,CAAC,cAAc,CAAC,eAAe,CAC1B,YAAY,EACZ,IAAI,CAAC,sBAAsB,CAC9B;YACH,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAA;QACjC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAA;QACxD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAA;QAC9D,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAA;QACnD,IAAI,CAAC,YAAY;YACb,IAAI,CAAC,iBAAiB,CAAC,YAAY,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAA;QAC/D,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CAClD,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,QAAQ,CAChB,CAAA;QACD,IAAI,CAAC,OAAO;YACR,OAAO,IAAI,CAAC,iBAAiB,CAAC,OAAO,KAAK,UAAU;gBAChD,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC;gBACpD,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAA,CAAC,4DAA4D;QAErG,IAAI,qBAAqB,KAAK,SAAS,EAAE,CAAC;YACtC,IAAI,CAAC,wBAAwB,GAAG,CAAC,qBAAqB,CAAA;QAC1D,CAAC;QAED,IAAI,CAAC,UAAU;YACX,IAAI,CAAC,iBAAiB,CAAC,IAAI,KAAK,kBAAkB;gBAClD,IAAI,CAAC,iBAAiB,CAAC,IAAI,KAAK,UAAU,CAAA;QAC9C,IAAI,CAAC,iBAAiB;YAClB,IAAI,CAAC,iBAAiB,CAAC,IAAI,KAAK,kBAAkB,CAAA;QAEtD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAA;IACjD,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,MAAsB;QACjC,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAAE,OAAM;QAElD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QAC5B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAChC,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,EAC/D,IAAI,CAAC,UAAU,CAClB,CAAA;QACD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,CAAA;QACvE,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAA;QAC5D,IAAI,CAAC,uBAAuB;YACxB,IAAI,CAAC,OAAO,CAAC,MAAM,CACf,CAAC,MAAM,EAAE,EAAE,CACP,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,kBAAkB,KAAK,MAAM,CACjE,CAAC,MAAM,GAAG,CAAC,CAAA;QAChB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAA;QAC/C,IAAI,IAAI,CAAC,oBAAoB;YACzB,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,EAAE,CACjD,cAAc,CAAC,cAAc,CAAC,MAAM,CAAC,CACxC,CAAA;IACT,CAAC;IAED;;;;;;;OAOG;IACH,mBAAmB;QACf,MAAM,GAAG,GAAqC,EAAE,CAAA;QAChD,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE,CAC5B,QAAQ,CAAC,SAAS,CAAC,GAAG,EAAE,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CACtE,CAAA;QACD,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE,CAChC,QAAQ,CAAC,SAAS,CACd,GAAG,EACH,QAAQ,CAAC,cAAc,CAAC,QAAQ,CAAC,YAAY,CAAC,CACjD,CACJ,CAAA;QACD,OAAO,GAAG,CAAA;IACd,CAAC;IAED;;;;;OAKG;IACH,4BAA4B;QACxB,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE;YAClC,OAAO,CACH,MAAM,CAAC,OAAO,KAAK,SAAS;gBAC5B,MAAM,CAAC,YAAY,KAAK,SAAS;gBACjC,MAAM,CAAC,WAAW;gBAClB,MAAM,CAAC,YAAY;gBACnB,MAAM,CAAC,YAAY;gBACnB,MAAM,CAAC,YAAY;gBACnB,MAAM,CAAC,SAAS,CACnB,CAAA;QACL,CAAC,CAAC,CAAA;IACN,CAAC;CACJ", "file": "EntityMetadata.js", "sourcesContent": ["import { Que<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>uery<PERSON><PERSON><PERSON> } from \"..\"\nimport { ObjectLiteral } from \"../common/ObjectLiteral\"\nimport { DataSource } from \"../data-source/DataSource\"\nimport { CannotCreateEntityIdMapError } from \"../error/CannotCreateEntityIdMapError\"\nimport { OrderByCondition } from \"../find-options/OrderByCondition\"\nimport { TableMetadataArgs } from \"../metadata-args/TableMetadataArgs\"\nimport { TreeMetadataArgs } from \"../metadata-args/TreeMetadataArgs\"\nimport { OrmUtils } from \"../util/OrmUtils\"\nimport { CheckMetadata } from \"./CheckMetadata\"\nimport { ColumnMetadata } from \"./ColumnMetadata\"\nimport { EmbeddedMetadata } from \"./EmbeddedMetadata\"\nimport { EntityListenerMetadata } from \"./EntityListenerMetadata\"\nimport { ExclusionMetadata } from \"./ExclusionMetadata\"\nimport { ForeignKeyMetadata } from \"./ForeignKeyMetadata\"\nimport { IndexMetadata } from \"./IndexMetadata\"\nimport { RelationCountMetadata } from \"./RelationCountMetadata\"\nimport { RelationIdMetadata } from \"./RelationIdMetadata\"\nimport { RelationMetadata } from \"./RelationMetadata\"\nimport { TableType } from \"./types/TableTypes\"\nimport { TreeType } from \"./types/TreeTypes\"\nimport { UniqueMetadata } from \"./UniqueMetadata\"\nimport { ClosureTreeOptions } from \"./types/ClosureTreeOptions\"\nimport { EntityPropertyNotFoundError } from \"../error/EntityPropertyNotFoundError\"\nimport { ObjectUtils } from \"../util/ObjectUtils\"\nimport { shorten } from \"../util/StringUtils\"\n\n/**\n * Contains all entity metadata.\n */\nexport class EntityMetadata {\n    readonly \"@instanceof\" = Symbol.for(\"EntityMetadata\")\n\n    // -------------------------------------------------------------------------\n    // Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Connection where this entity metadata is created.\n     */\n    connection: DataSource\n\n    /**\n     * Metadata arguments used to build this entity metadata.\n     */\n    tableMetadataArgs: TableMetadataArgs\n\n    /**\n     * If entity's table is a closure-typed table, then this entity will have a closure junction table metadata.\n     */\n    closureJunctionTable: EntityMetadata\n\n    /**\n     * If this is entity metadata for a junction closure table then its owner closure table metadata will be set here.\n     */\n    parentClosureEntityMetadata: EntityMetadata\n\n    /**\n     * Parent's entity metadata. Used in inheritance patterns.\n     */\n    parentEntityMetadata: EntityMetadata\n\n    /**\n     * Children entity metadatas. Used in inheritance patterns.\n     */\n    childEntityMetadatas: EntityMetadata[] = []\n\n    /**\n     * All \"inheritance tree\" from a target entity.\n     * For example for target Post < ContentModel < Unit it will be an array of [Post, ContentModel, Unit].\n     * It also contains child entities for single table inheritance.\n     */\n    inheritanceTree: Function[] = []\n\n    /**\n     * Table type. Tables can be closure, junction, etc.\n     */\n    tableType: TableType = \"regular\"\n\n    /**\n     * Target class to which this entity metadata is bind.\n     * Note, that when using table inheritance patterns target can be different rather then table's target.\n     * For virtual tables which lack of real entity (like junction tables) target is equal to their table name.\n     */\n    target: Function | string\n\n    /**\n     * Gets the name of the target.\n     */\n    targetName: string\n\n    /**\n     * Entity's name.\n     * Equal to entity target class's name if target is set to table.\n     * If target class is not then then it equals to table name.\n     */\n    name: string\n\n    /**\n     * View's expression.\n     * Used in views\n     */\n    expression?: string | ((connection: DataSource) => SelectQueryBuilder<any>)\n\n    /**\n     * View's dependencies.\n     * Used in views\n     */\n    dependsOn?: Set<Function | string>\n\n    /**\n     * Enables Sqlite \"WITHOUT ROWID\" modifier for the \"CREATE TABLE\" statement\n     */\n    withoutRowid?: boolean = false\n\n    /**\n     * Original user-given table name (taken from schema or @Entity(tableName) decorator).\n     * If user haven't specified a table name this property will be undefined.\n     */\n    givenTableName?: string\n\n    /**\n     * Entity table name in the database.\n     * This is final table name of the entity.\n     * This name already passed naming strategy, and generated based on\n     * multiple criteria, including user table name and global table prefix.\n     */\n    tableName: string\n\n    /**\n     * Entity table path. Contains database name, schema name and table name.\n     * E.g. myDB.mySchema.myTable\n     */\n    tablePath: string\n\n    /**\n     * Gets the table name without global table prefix.\n     * When querying table you need a table name with prefix, but in some scenarios,\n     * for example when you want to name a junction table that contains names of two other tables,\n     * you may want a table name without prefix.\n     */\n    tableNameWithoutPrefix: string\n\n    /**\n     * Indicates if schema will be synchronized for this entity or not.\n     */\n    synchronize: boolean = true\n\n    /**\n     * Table's database engine type (like \"InnoDB\", \"MyISAM\", etc).\n     */\n    engine?: string\n\n    /**\n     * Database name.\n     */\n    database?: string\n\n    /**\n     * Schema name. Used in Postgres and Sql Server.\n     */\n    schema?: string\n\n    /**\n     * Specifies a default order by used for queries from this table when no explicit order by is specified.\n     */\n    orderBy?: OrderByCondition\n\n    /**\n     * If this entity metadata's table using one of the inheritance patterns,\n     * then this will contain what pattern it uses.\n     */\n    inheritancePattern?: \"STI\" /*|\"CTI\"*/\n\n    /**\n     * Checks if there any non-nullable column exist in this entity.\n     */\n    hasNonNullableRelations: boolean = false\n\n    /**\n     * Indicates if this entity metadata of a junction table, or not.\n     * Junction table is a table created by many-to-many relationship.\n     *\n     * Its also possible to understand if entity is junction via tableType.\n     */\n    isJunction: boolean = false\n\n    /**\n     * Indicates if the entity should be instantiated using the constructor\n     * or via allocating a new object via `Object.create()`.\n     */\n    isAlwaysUsingConstructor: boolean = true\n\n    /**\n     * Indicates if this entity is a tree, what type of tree it is.\n     */\n    treeType?: TreeType\n\n    /**\n     * Indicates if this entity is a tree, what options of tree it has.\n     */\n    treeOptions?: ClosureTreeOptions\n\n    /**\n     * Checks if this table is a junction table of the closure table.\n     * This type is for tables that contain junction metadata of the closure tables.\n     */\n    isClosureJunction: boolean = false\n\n    /**\n     * Checks if entity's table has multiple primary columns.\n     */\n    hasMultiplePrimaryKeys: boolean = false\n\n    /**\n     * Indicates if this entity metadata has uuid generated columns.\n     */\n    hasUUIDGeneratedColumns: boolean = false\n\n    /**\n     * If this entity metadata is a child table of some table, it should have a discriminator value.\n     * Used to store a value in a discriminator column.\n     */\n    discriminatorValue?: string\n\n    /**\n     * Entity's column metadatas defined by user.\n     */\n    ownColumns: ColumnMetadata[] = []\n\n    /**\n     * Columns of the entity, including columns that are coming from the embeddeds of this entity.\n     */\n    columns: ColumnMetadata[] = []\n\n    /**\n     * Ancestor columns used only in closure junction tables.\n     */\n    ancestorColumns: ColumnMetadata[] = []\n\n    /**\n     * Descendant columns used only in closure junction tables.\n     */\n    descendantColumns: ColumnMetadata[] = []\n\n    /**\n     * All columns except for virtual columns.\n     */\n    nonVirtualColumns: ColumnMetadata[] = []\n\n    /**\n     * In the case if this entity metadata is junction table's entity metadata,\n     * this will contain all referenced columns of owner entity.\n     */\n    ownerColumns: ColumnMetadata[] = []\n\n    /**\n     * In the case if this entity metadata is junction table's entity metadata,\n     * this will contain all referenced columns of inverse entity.\n     */\n    inverseColumns: ColumnMetadata[] = []\n\n    /**\n     * Gets the column with generated flag.\n     */\n    generatedColumns: ColumnMetadata[] = []\n\n    /**\n     * Gets the object id column used with mongodb database.\n     */\n    objectIdColumn?: ColumnMetadata\n\n    /**\n     * Gets entity column which contains a create date value.\n     */\n    createDateColumn?: ColumnMetadata\n\n    /**\n     * Gets entity column which contains an update date value.\n     */\n    updateDateColumn?: ColumnMetadata\n\n    /**\n     * Gets entity column which contains a delete date value.\n     */\n    deleteDateColumn?: ColumnMetadata\n\n    /**\n     * Gets entity column which contains an entity version.\n     */\n    versionColumn?: ColumnMetadata\n\n    /**\n     * Gets the discriminator column used to store entity identificator in single-table inheritance tables.\n     */\n    discriminatorColumn?: ColumnMetadata\n\n    /**\n     * Special column that stores tree level in tree entities.\n     */\n    treeLevelColumn?: ColumnMetadata\n\n    /**\n     * Nested set's left value column.\n     * Used only in tree entities with nested set pattern applied.\n     */\n    nestedSetLeftColumn?: ColumnMetadata\n\n    /**\n     * Nested set's right value column.\n     * Used only in tree entities with nested set pattern applied.\n     */\n    nestedSetRightColumn?: ColumnMetadata\n\n    /**\n     * Materialized path column.\n     * Used only in tree entities with materialized path pattern applied.\n     */\n    materializedPathColumn?: ColumnMetadata\n\n    /**\n     * Gets the primary columns.\n     */\n    primaryColumns: ColumnMetadata[] = []\n\n    /**\n     * Entity's relation metadatas.\n     */\n    ownRelations: RelationMetadata[] = []\n\n    /**\n     * Relations of the entity, including relations that are coming from the embeddeds of this entity.\n     */\n    relations: RelationMetadata[] = []\n\n    /**\n     * List of eager relations this metadata has.\n     */\n    eagerRelations: RelationMetadata[] = []\n\n    /**\n     * List of eager relations this metadata has.\n     */\n    lazyRelations: RelationMetadata[] = []\n\n    /**\n     * Gets only one-to-one relations of the entity.\n     */\n    oneToOneRelations: RelationMetadata[] = []\n\n    /**\n     * Gets only owner one-to-one relations of the entity.\n     */\n    ownerOneToOneRelations: RelationMetadata[] = []\n\n    /**\n     * Gets only one-to-many relations of the entity.\n     */\n    oneToManyRelations: RelationMetadata[] = []\n\n    /**\n     * Gets only many-to-one relations of the entity.\n     */\n    manyToOneRelations: RelationMetadata[] = []\n\n    /**\n     * Gets only many-to-many relations of the entity.\n     */\n    manyToManyRelations: RelationMetadata[] = []\n\n    /**\n     * Gets only owner many-to-many relations of the entity.\n     */\n    ownerManyToManyRelations: RelationMetadata[] = []\n\n    /**\n     * Gets only owner one-to-one and many-to-one relations.\n     */\n    relationsWithJoinColumns: RelationMetadata[] = []\n\n    /**\n     * Tree parent relation. Used only in tree-tables.\n     */\n    treeParentRelation?: RelationMetadata\n\n    /**\n     * Tree children relation. Used only in tree-tables.\n     */\n    treeChildrenRelation?: RelationMetadata\n\n    /**\n     * Entity's relation id metadatas.\n     */\n    relationIds: RelationIdMetadata[] = []\n\n    /**\n     * Entity's relation id metadatas.\n     */\n    relationCounts: RelationCountMetadata[] = []\n\n    /**\n     * Entity's foreign key metadatas.\n     */\n    foreignKeys: ForeignKeyMetadata[] = []\n\n    /**\n     * Entity's embedded metadatas.\n     */\n    embeddeds: EmbeddedMetadata[] = []\n\n    /**\n     * All embeddeds - embeddeds from this entity metadata and from all child embeddeds, etc.\n     */\n    allEmbeddeds: EmbeddedMetadata[] = []\n\n    /**\n     * Entity's own indices.\n     */\n    ownIndices: IndexMetadata[] = []\n\n    /**\n     * Entity's index metadatas.\n     */\n    indices: IndexMetadata[] = []\n\n    /**\n     * Entity's unique metadatas.\n     */\n    uniques: UniqueMetadata[] = []\n\n    /**\n     * Entity's own uniques.\n     */\n    ownUniques: UniqueMetadata[] = []\n\n    /**\n     * Entity's check metadatas.\n     */\n    checks: CheckMetadata[] = []\n\n    /**\n     * Entity's exclusion metadatas.\n     */\n    exclusions: ExclusionMetadata[] = []\n\n    /**\n     * Entity's own listener metadatas.\n     */\n    ownListeners: EntityListenerMetadata[] = []\n\n    /**\n     * Entity listener metadatas.\n     */\n    listeners: EntityListenerMetadata[] = []\n\n    /**\n     * Listener metadatas with \"AFTER LOAD\" type.\n     */\n    afterLoadListeners: EntityListenerMetadata[] = []\n\n    /**\n     * Listener metadatas with \"BEFORE INSERT\" type.\n     */\n    beforeInsertListeners: EntityListenerMetadata[] = []\n\n    /**\n     * Listener metadatas with \"AFTER INSERT\" type.\n     */\n    afterInsertListeners: EntityListenerMetadata[] = []\n\n    /**\n     * Listener metadatas with \"BEFORE UPDATE\" type.\n     */\n    beforeUpdateListeners: EntityListenerMetadata[] = []\n\n    /**\n     * Listener metadatas with \"AFTER UPDATE\" type.\n     */\n    afterUpdateListeners: EntityListenerMetadata[] = []\n\n    /**\n     * Listener metadatas with \"BEFORE REMOVE\" type.\n     */\n    beforeRemoveListeners: EntityListenerMetadata[] = []\n\n    /**\n     * Listener metadatas with \"BEFORE SOFT REMOVE\" type.\n     */\n    beforeSoftRemoveListeners: EntityListenerMetadata[] = []\n\n    /**\n     * Listener metadatas with \"BEFORE RECOVER\" type.\n     */\n    beforeRecoverListeners: EntityListenerMetadata[] = []\n\n    /**\n     * Listener metadatas with \"AFTER REMOVE\" type.\n     */\n    afterRemoveListeners: EntityListenerMetadata[] = []\n\n    /**\n     * Listener metadatas with \"AFTER SOFT REMOVE\" type.\n     */\n    afterSoftRemoveListeners: EntityListenerMetadata[] = []\n\n    /**\n     * Listener metadatas with \"AFTER RECOVER\" type.\n     */\n    afterRecoverListeners: EntityListenerMetadata[] = []\n\n    /**\n     * Map of columns and relations of the entity.\n     *\n     * example: Post{ id: number, name: string, counterEmbed: { count: number }, category: Category }.\n     * This method will create following object:\n     * { id: \"id\", counterEmbed: { count: \"counterEmbed.count\" }, category: \"category\" }\n     */\n    propertiesMap: ObjectLiteral\n\n    /**\n     * Table comment. Not supported by all database types.\n     */\n    comment?: string\n\n    // ---------------------------------------------------------------------\n    // Constructor\n    // ---------------------------------------------------------------------\n\n    constructor(options: {\n        connection: DataSource\n        inheritanceTree?: Function[]\n        inheritancePattern?: \"STI\" /*|\"CTI\"*/\n        tableTree?: TreeMetadataArgs\n        parentClosureEntityMetadata?: EntityMetadata\n        args: TableMetadataArgs\n    }) {\n        this.connection = options.connection\n        this.inheritanceTree = options.inheritanceTree || []\n        this.inheritancePattern = options.inheritancePattern\n        this.treeType = options.tableTree ? options.tableTree.type : undefined\n        this.treeOptions = options.tableTree\n            ? options.tableTree.options\n            : undefined\n        this.parentClosureEntityMetadata = options.parentClosureEntityMetadata!\n        this.tableMetadataArgs = options.args\n        this.target = this.tableMetadataArgs.target\n        this.tableType = this.tableMetadataArgs.type\n        this.expression = this.tableMetadataArgs.expression\n        this.withoutRowid = this.tableMetadataArgs.withoutRowid\n        this.dependsOn = this.tableMetadataArgs.dependsOn\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Creates a new entity.\n     */\n    create(\n        queryRunner?: QueryRunner,\n        options?: { fromDeserializer?: boolean; pojo?: boolean },\n    ): any {\n        const pojo = options && options.pojo === true ? true : false\n        // if target is set to a function (e.g. class) that can be created then create it\n        let ret: any\n        if (typeof this.target === \"function\" && !pojo) {\n            if (!options?.fromDeserializer || this.isAlwaysUsingConstructor) {\n                ret = new (<any>this.target)()\n            } else {\n                ret = Object.create(this.target.prototype)\n            }\n        } else {\n            // otherwise simply return a new empty object\n            ret = {}\n        }\n\n        // add \"typename\" property\n        if (this.connection.options.typename) {\n            ret[this.connection.options.typename] = this.targetName\n        }\n\n        this.lazyRelations.forEach((relation) =>\n            this.connection.relationLoader.enableLazyLoad(\n                relation,\n                ret,\n                queryRunner,\n            ),\n        )\n        return ret\n    }\n\n    /**\n     * Checks if given entity has an id.\n     */\n    hasId(entity: ObjectLiteral): boolean {\n        if (!entity) return false\n\n        return this.primaryColumns.every((primaryColumn) => {\n            const value = primaryColumn.getEntityValue(entity)\n            return value !== null && value !== undefined && value !== \"\"\n        })\n    }\n\n    /**\n     * Checks if given entity / object contains ALL primary keys entity must have.\n     * Returns true if it contains all of them, false if at least one of them is not defined.\n     */\n    hasAllPrimaryKeys(entity: ObjectLiteral): boolean {\n        return this.primaryColumns.every((primaryColumn) => {\n            const value = primaryColumn.getEntityValue(entity)\n            return value !== null && value !== undefined\n        })\n    }\n\n    /**\n     * Ensures that given object is an entity id map.\n     * If given id is an object then it means its already id map.\n     * If given id isn't an object then it means its a value of the id column\n     * and it creates a new id map with this value and name of the primary column.\n     */\n    ensureEntityIdMap(id: any): ObjectLiteral {\n        if (ObjectUtils.isObject(id)) return id\n\n        if (this.hasMultiplePrimaryKeys)\n            throw new CannotCreateEntityIdMapError(this, id)\n\n        return this.primaryColumns[0].createValueMap(id)\n    }\n\n    /**\n     * Gets primary keys of the entity and returns them in a literal object.\n     * For example, for Post{ id: 1, title: \"hello\" } where id is primary it will return { id: 1 }\n     * For multiple primary keys it returns multiple keys in object.\n     * For primary keys inside embeds it returns complex object literal with keys in them.\n     */\n    getEntityIdMap(\n        entity: ObjectLiteral | undefined,\n    ): ObjectLiteral | undefined {\n        if (!entity) return undefined\n\n        return EntityMetadata.getValueMap(entity, this.primaryColumns, {\n            skipNulls: true,\n        })\n    }\n\n    /**\n     * Creates a \"mixed id map\".\n     * If entity has multiple primary keys (ids) then it will return just regular id map, like what getEntityIdMap returns.\n     * But if entity has a single primary key then it will return just value of the id column of the entity, just value.\n     * This is called mixed id map.\n     */\n    getEntityIdMixedMap(\n        entity: ObjectLiteral | undefined,\n    ): ObjectLiteral | undefined {\n        if (!entity) return entity\n\n        const idMap = this.getEntityIdMap(entity)\n        if (this.hasMultiplePrimaryKeys) {\n            return idMap\n        } else if (idMap) {\n            return this.primaryColumns[0].getEntityValue(idMap) // todo: what about parent primary column?\n        }\n\n        return idMap\n    }\n\n    /**\n     * Compares two different entities by their ids.\n     * Returns true if they match, false otherwise.\n     */\n    compareEntities(\n        firstEntity: ObjectLiteral,\n        secondEntity: ObjectLiteral,\n    ): boolean {\n        const firstEntityIdMap = this.getEntityIdMap(firstEntity)\n        if (!firstEntityIdMap) return false\n\n        const secondEntityIdMap = this.getEntityIdMap(secondEntity)\n        if (!secondEntityIdMap) return false\n\n        return OrmUtils.compareIds(firstEntityIdMap, secondEntityIdMap)\n    }\n\n    /**\n     * Finds column with a given property name.\n     */\n    findColumnWithPropertyName(\n        propertyName: string,\n    ): ColumnMetadata | undefined {\n        return this.columns.find(\n            (column) => column.propertyName === propertyName,\n        )\n    }\n\n    /**\n     * Finds column with a given database name.\n     */\n    findColumnWithDatabaseName(\n        databaseName: string,\n    ): ColumnMetadata | undefined {\n        return this.columns.find(\n            (column) => column.databaseName === databaseName,\n        )\n    }\n\n    /**\n     * Checks if there is a column or relationship with a given property path.\n     */\n    hasColumnWithPropertyPath(propertyPath: string): boolean {\n        const hasColumn = this.columns.some(\n            (column) => column.propertyPath === propertyPath,\n        )\n        return hasColumn || this.hasRelationWithPropertyPath(propertyPath)\n    }\n\n    /**\n     * Finds column with a given property path.\n     */\n    findColumnWithPropertyPath(\n        propertyPath: string,\n    ): ColumnMetadata | undefined {\n        const column = this.columns.find(\n            (column) => column.propertyPath === propertyPath,\n        )\n        if (column) return column\n\n        // in the case if column with property path was not found, try to find a relation with such property path\n        // if we find relation and it has a single join column then its the column user was seeking\n        const relation = this.relations.find(\n            (relation) => relation.propertyPath === propertyPath,\n        )\n        if (relation && relation.joinColumns.length === 1)\n            return relation.joinColumns[0]\n\n        return undefined\n    }\n\n    /**\n     * Finds column with a given property path.\n     * Does not search in relation unlike findColumnWithPropertyPath.\n     */\n    findColumnWithPropertyPathStrict(\n        propertyPath: string,\n    ): ColumnMetadata | undefined {\n        return this.columns.find(\n            (column) => column.propertyPath === propertyPath,\n        )\n    }\n\n    /**\n     * Finds columns with a given property path.\n     * Property path can match a relation, and relations can contain multiple columns.\n     */\n    findColumnsWithPropertyPath(propertyPath: string): ColumnMetadata[] {\n        const column = this.columns.find(\n            (column) => column.propertyPath === propertyPath,\n        )\n        if (column) return [column]\n\n        // in the case if column with property path was not found, try to find a relation with such property path\n        // if we find relation and it has a single join column then its the column user was seeking\n        const relation = this.findRelationWithPropertyPath(propertyPath)\n        if (relation && relation.joinColumns) return relation.joinColumns\n\n        return []\n    }\n\n    /**\n     * Checks if there is a relation with the given property path.\n     */\n    hasRelationWithPropertyPath(propertyPath: string): boolean {\n        return this.relations.some(\n            (relation) => relation.propertyPath === propertyPath,\n        )\n    }\n\n    /**\n     * Finds relation with the given property path.\n     */\n    findRelationWithPropertyPath(\n        propertyPath: string,\n    ): RelationMetadata | undefined {\n        return this.relations.find(\n            (relation) => relation.propertyPath === propertyPath,\n        )\n    }\n\n    /**\n     * Checks if there is an embedded with a given property path.\n     */\n    hasEmbeddedWithPropertyPath(propertyPath: string): boolean {\n        return this.allEmbeddeds.some(\n            (embedded) => embedded.propertyPath === propertyPath,\n        )\n    }\n\n    /**\n     * Finds embedded with a given property path.\n     */\n    findEmbeddedWithPropertyPath(\n        propertyPath: string,\n    ): EmbeddedMetadata | undefined {\n        return this.allEmbeddeds.find(\n            (embedded) => embedded.propertyPath === propertyPath,\n        )\n    }\n\n    /**\n     * Returns an array of databaseNames mapped from provided propertyPaths\n     */\n    mapPropertyPathsToColumns(propertyPaths: string[]) {\n        return propertyPaths.map((propertyPath) => {\n            const column = this.findColumnWithPropertyPath(propertyPath)\n            if (column == null) {\n                throw new EntityPropertyNotFoundError(propertyPath, this)\n            }\n            return column\n        })\n    }\n\n    /**\n     * Iterates through entity and finds and extracts all values from relations in the entity.\n     * If relation value is an array its being flattened.\n     */\n    extractRelationValuesFromEntity(\n        entity: ObjectLiteral,\n        relations: RelationMetadata[],\n    ): [RelationMetadata, any, EntityMetadata][] {\n        const relationsAndValues: [RelationMetadata, any, EntityMetadata][] = []\n        relations.forEach((relation) => {\n            const value = relation.getEntityValue(entity)\n            if (Array.isArray(value)) {\n                value.forEach((subValue) =>\n                    relationsAndValues.push([\n                        relation,\n                        subValue,\n                        EntityMetadata.getInverseEntityMetadata(\n                            subValue,\n                            relation,\n                        ),\n                    ]),\n                )\n            } else if (value) {\n                relationsAndValues.push([\n                    relation,\n                    value,\n                    EntityMetadata.getInverseEntityMetadata(value, relation),\n                ])\n            }\n        })\n        return relationsAndValues\n    }\n\n    /**\n     * In the case of SingleTableInheritance, find the correct metadata\n     * for a given value.\n     *\n     * @param value The value to find the metadata for.\n     * @returns The found metadata for the entity or the base metadata if no matching metadata\n     *          was found in the whole inheritance tree.\n     */\n    findInheritanceMetadata(value: any): EntityMetadata {\n        // Check for single table inheritance and find the correct metadata in that case.\n        // Goal is to use the correct discriminator as we could have a repository\n        // for an (abstract) base class and thus the target would not match.\n\n        if (\n            this.inheritancePattern === \"STI\" &&\n            this.childEntityMetadatas.length > 0\n        ) {\n            // There could be a column on the base class that can manually be set to override the type.\n            let manuallySetDiscriminatorValue: unknown\n            if (this.discriminatorColumn) {\n                manuallySetDiscriminatorValue =\n                    value[this.discriminatorColumn.propertyName]\n            }\n            return (\n                this.childEntityMetadatas.find(\n                    (meta) =>\n                        manuallySetDiscriminatorValue ===\n                            meta.discriminatorValue ||\n                        value.constructor === meta.target,\n                ) || this\n            )\n        }\n        return this\n    }\n\n    // -------------------------------------------------------------------------\n    // Private Static Methods\n    // -------------------------------------------------------------------------\n\n    private static getInverseEntityMetadata(\n        value: any,\n        relation: RelationMetadata,\n    ): EntityMetadata {\n        return relation.inverseEntityMetadata.findInheritanceMetadata(value)\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Static Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Creates a property paths for a given entity.\n     *\n     * @deprecated\n     */\n    static createPropertyPath(\n        metadata: EntityMetadata,\n        entity: ObjectLiteral,\n        prefix: string = \"\",\n    ) {\n        const paths: string[] = []\n        Object.keys(entity).forEach((key) => {\n            // check for function is needed in the cases when createPropertyPath used on values contain a function as a value\n            // example: .update().set({ name: () => `SUBSTR('', 1, 2)` })\n            const parentPath = prefix ? prefix + \".\" + key : key\n            if (metadata.hasEmbeddedWithPropertyPath(parentPath)) {\n                const subPaths = this.createPropertyPath(\n                    metadata,\n                    entity[key],\n                    parentPath,\n                )\n                paths.push(...subPaths)\n            } else {\n                const path = prefix ? prefix + \".\" + key : key\n                paths.push(path)\n            }\n        })\n        return paths\n    }\n\n    /**\n     * Finds difference between two entity id maps.\n     * Returns items that exist in the first array and absent in the second array.\n     */\n    static difference(\n        firstIdMaps: ObjectLiteral[],\n        secondIdMaps: ObjectLiteral[],\n    ): ObjectLiteral[] {\n        return firstIdMaps.filter((firstIdMap) => {\n            return !secondIdMaps.find((secondIdMap) =>\n                OrmUtils.compareIds(firstIdMap, secondIdMap),\n            )\n        })\n    }\n\n    /**\n     * Creates value map from the given values and columns.\n     * Examples of usages are primary columns map and join columns map.\n     */\n    static getValueMap(\n        entity: ObjectLiteral,\n        columns: ColumnMetadata[],\n        options?: { skipNulls?: boolean },\n    ): ObjectLiteral | undefined {\n        return columns.reduce((map, column) => {\n            const value = column.getEntityValueMap(entity, options)\n\n            // make sure that none of the values of the columns are not missing\n            if (map === undefined || value === null || value === undefined)\n                return undefined\n\n            return OrmUtils.mergeDeep(map, value)\n        }, {} as ObjectLiteral | undefined)\n    }\n\n    // ---------------------------------------------------------------------\n    // Public Builder Methods\n    // ---------------------------------------------------------------------\n\n    build() {\n        const namingStrategy = this.connection.namingStrategy\n        const entityPrefix = this.connection.options.entityPrefix\n        const entitySkipConstructor =\n            this.connection.options.entitySkipConstructor\n\n        this.engine = this.tableMetadataArgs.engine\n        this.database =\n            this.tableMetadataArgs.type === \"entity-child\" &&\n            this.parentEntityMetadata\n                ? this.parentEntityMetadata.database\n                : this.tableMetadataArgs.database\n        if (this.tableMetadataArgs.schema) {\n            this.schema = this.tableMetadataArgs.schema\n        } else if (\n            this.tableMetadataArgs.type === \"entity-child\" &&\n            this.parentEntityMetadata\n        ) {\n            this.schema = this.parentEntityMetadata.schema\n        } else if (this.connection.options?.hasOwnProperty(\"schema\")) {\n            this.schema = (this.connection.options as any).schema\n        }\n        this.givenTableName =\n            this.tableMetadataArgs.type === \"entity-child\" &&\n            this.parentEntityMetadata\n                ? this.parentEntityMetadata.givenTableName\n                : this.tableMetadataArgs.name\n        this.synchronize =\n            this.tableMetadataArgs.synchronize === false ? false : true\n        this.targetName =\n            typeof this.tableMetadataArgs.target === \"function\"\n                ? (this.tableMetadataArgs.target as any).name\n                : this.tableMetadataArgs.target\n        if (this.tableMetadataArgs.type === \"closure-junction\") {\n            this.tableNameWithoutPrefix =\n                namingStrategy.closureJunctionTableName(this.givenTableName!)\n        } else if (\n            this.tableMetadataArgs.type === \"entity-child\" &&\n            this.parentEntityMetadata\n        ) {\n            this.tableNameWithoutPrefix = namingStrategy.tableName(\n                this.parentEntityMetadata.targetName,\n                this.parentEntityMetadata.givenTableName,\n            )\n        } else {\n            this.tableNameWithoutPrefix = namingStrategy.tableName(\n                this.targetName,\n                this.givenTableName,\n            )\n\n            if (\n                this.tableMetadataArgs.type === \"junction\" &&\n                this.connection.driver.maxAliasLength &&\n                this.connection.driver.maxAliasLength > 0 &&\n                this.tableNameWithoutPrefix.length >\n                    this.connection.driver.maxAliasLength\n            ) {\n                // note: we are not using DriverUtils.buildAlias here because we would like to avoid\n                // hashed table names. However, current algorithm also isn't perfect, but we cannot\n                // change it, since it's a big breaking change. Planned to 0.4.0\n                this.tableNameWithoutPrefix = shorten(\n                    this.tableNameWithoutPrefix,\n                    { separator: \"_\", segmentLength: 3 },\n                )\n            }\n        }\n        this.tableName = entityPrefix\n            ? namingStrategy.prefixTableName(\n                  entityPrefix,\n                  this.tableNameWithoutPrefix,\n              )\n            : this.tableNameWithoutPrefix\n        this.target = this.target ? this.target : this.tableName\n        this.name = this.targetName ? this.targetName : this.tableName\n        this.expression = this.tableMetadataArgs.expression\n        this.withoutRowid =\n            this.tableMetadataArgs.withoutRowid === true ? true : false\n        this.tablePath = this.connection.driver.buildTableName(\n            this.tableName,\n            this.schema,\n            this.database,\n        )\n        this.orderBy =\n            typeof this.tableMetadataArgs.orderBy === \"function\"\n                ? this.tableMetadataArgs.orderBy(this.propertiesMap)\n                : this.tableMetadataArgs.orderBy // todo: is propertiesMap available here? Looks like its not\n\n        if (entitySkipConstructor !== undefined) {\n            this.isAlwaysUsingConstructor = !entitySkipConstructor\n        }\n\n        this.isJunction =\n            this.tableMetadataArgs.type === \"closure-junction\" ||\n            this.tableMetadataArgs.type === \"junction\"\n        this.isClosureJunction =\n            this.tableMetadataArgs.type === \"closure-junction\"\n\n        this.comment = this.tableMetadataArgs.comment\n    }\n\n    /**\n     * Registers a new column in the entity and recomputes all depend properties.\n     */\n    registerColumn(column: ColumnMetadata) {\n        if (this.ownColumns.indexOf(column) !== -1) return\n\n        this.ownColumns.push(column)\n        this.columns = this.embeddeds.reduce(\n            (columns, embedded) => columns.concat(embedded.columnsFromTree),\n            this.ownColumns,\n        )\n        this.primaryColumns = this.columns.filter((column) => column.isPrimary)\n        this.hasMultiplePrimaryKeys = this.primaryColumns.length > 1\n        this.hasUUIDGeneratedColumns =\n            this.columns.filter(\n                (column) =>\n                    column.isGenerated || column.generationStrategy === \"uuid\",\n            ).length > 0\n        this.propertiesMap = this.createPropertiesMap()\n        if (this.childEntityMetadatas)\n            this.childEntityMetadatas.forEach((entityMetadata) =>\n                entityMetadata.registerColumn(column),\n            )\n    }\n\n    /**\n     * Creates a special object - all columns and relations of the object (plus columns and relations from embeds)\n     * in a special format - { propertyName: propertyName }.\n     *\n     * example: Post{ id: number, name: string, counterEmbed: { count: number }, category: Category }.\n     * This method will create following object:\n     * { id: \"id\", counterEmbed: { count: \"counterEmbed.count\" }, category: \"category\" }\n     */\n    createPropertiesMap(): { [name: string]: string | any } {\n        const map: { [name: string]: string | any } = {}\n        this.columns.forEach((column) =>\n            OrmUtils.mergeDeep(map, column.createValueMap(column.propertyPath)),\n        )\n        this.relations.forEach((relation) =>\n            OrmUtils.mergeDeep(\n                map,\n                relation.createValueMap(relation.propertyPath),\n            ),\n        )\n        return map\n    }\n\n    /**\n     * Checks if entity has any column which rely on returning data,\n     * e.g. columns with auto generated value, DEFAULT values considered as dependant of returning data.\n     * For example, if we need to have RETURNING after INSERT (or we need returned id for DBs not supporting RETURNING),\n     * it means we cannot execute bulk inserts in some cases.\n     */\n    getInsertionReturningColumns(): ColumnMetadata[] {\n        return this.columns.filter((column) => {\n            return (\n                column.default !== undefined ||\n                column.asExpression !== undefined ||\n                column.isGenerated ||\n                column.isCreateDate ||\n                column.isUpdateDate ||\n                column.isDeleteDate ||\n                column.isVersion\n            )\n        })\n    }\n}\n"], "sourceRoot": ".."}