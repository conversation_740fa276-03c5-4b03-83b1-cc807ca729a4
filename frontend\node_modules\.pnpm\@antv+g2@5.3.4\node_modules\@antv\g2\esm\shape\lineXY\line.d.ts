import { ShapeComponent as SC } from '../../runtime';
export type LineOptions = {
    /**
     * Whether show arrow.
     */
    arrow?: boolean;
    /**
     * Arrow size(px), default is 4px.
     */
    arrowSize?: number;
    /**
     * Fill color of arrow.
     */
    arrowFill?: string;
    /**
     * Stroke color of arrow.
     */
    arrowStroke?: string;
    /**
     * Others.
     */
    [key: string]: any;
};
export declare const Line: SC<LineOptions>;
