import { ElementHighlight, ElementHighlightByX, ElementHighlightByColor, ElementSelect, ElementSelectByX, ElementSelectByColor, ChartIndex, Fisheye as ChartFisheye, Tooltip, LegendFilter, LegendHighlight, BrushHighlight, BrushXHighlight, BrushYHighlight, BrushAxisHighlight, BrushFilter, BrushXFilter, BrushYFilter, SliderFilter, Poptip, ScrollbarFilter, TreemapDrillDown, ElementPointMove } from '../interaction';
export declare function corelib(): {
    readonly 'data.fetch': import("../runtime").DataComponent<import("../data").FetchOptions>;
    readonly 'data.inline': import("../runtime").DataComponent<import("../data").InlineOptions>;
    readonly 'data.sortBy': import("../runtime").DataComponent<import("../data").SortByOptions>;
    readonly 'data.sort': import("../runtime").DataComponent<import("../data").SortOptions>;
    readonly 'data.filter': import("../runtime").DataComponent<import("../data").FilterDataOptions>;
    readonly 'data.pick': import("../runtime").DataComponent<import("../data").PickOptions>;
    readonly 'data.rename': import("../runtime").DataComponent<import("../data").RenameOptions>;
    readonly 'data.fold': import("../runtime").DataComponent<import("../data").FoldOptions>;
    readonly 'data.slice': import("../runtime").DataComponent<import("../data").SliceOptions>;
    readonly 'data.custom': import("../runtime").DataComponent<import("../data").CustomOptions>;
    readonly 'data.map': import("../runtime").DataComponent<import("../data").MapOptions>;
    readonly 'data.join': import("../runtime").DataComponent<import("../data").JoinOptions>;
    readonly 'data.kde': import("../runtime").DataComponent<import("../data").KDEOptions>;
    readonly 'data.log': import("../runtime").DataComponent<import("../data").LogDataOptions>;
    readonly 'data.wordCloud': import("../runtime").DataComponent<Partial<import("../data").WordCloudOptions>>;
    readonly 'data.ema': import("../runtime").DataComponent<import("../data").EMAOptions>;
    readonly 'transform.stackY': import("../runtime").TransformComponent<import("../transform").StackYOptions>;
    readonly 'transform.binX': import("../runtime").TransformComponent<import("../transform").BinXOptions>;
    readonly 'transform.bin': import("../runtime").TransformComponent<import("../transform").BinOptions>;
    readonly 'transform.dodgeX': import("../runtime").TransformComponent<import("../transform").DodgeXOptions>;
    readonly 'transform.jitter': import("../runtime").TransformComponent<import("../transform").JitterOptions>;
    readonly 'transform.jitterX': import("../runtime").TransformComponent<import("../transform").JitterXOptions>;
    readonly 'transform.jitterY': import("../runtime").TransformComponent<import("../transform").JitterYOptions>;
    readonly 'transform.symmetryY': import("../runtime").TransformComponent<import("../transform").SymmetryYOptions>;
    readonly 'transform.diffY': import("../runtime").TransformComponent<import("../transform").DiffYOptions>;
    readonly 'transform.stackEnter': import("../runtime").TransformComponent<import("../transform").StackEnterOptions>;
    readonly 'transform.normalizeY': import("../runtime").TransformComponent<import("../transform").NormalizeYOptions>;
    readonly 'transform.select': import("../runtime").TransformComponent<import("../transform").SelectOptions>;
    readonly 'transform.selectX': import("../runtime").TransformComponent<import("../transform").SelectXOptions>;
    readonly 'transform.selectY': import("../runtime").TransformComponent<import("../transform").SelectYOptions>;
    readonly 'transform.groupX': import("../runtime").TransformComponent<import("../transform").GroupXOptions>;
    readonly 'transform.groupY': import("../runtime").TransformComponent<import("../transform").GroupYOptions>;
    readonly 'transform.groupColor': import("../runtime").TransformComponent<import("../transform").GroupColorOptions>;
    readonly 'transform.group': import("../runtime").TransformComponent<import("../transform").GroupOptions>;
    readonly 'transform.sortX': import("../runtime").TransformComponent<import("../transform").SortXOptions>;
    readonly 'transform.sortY': import("../runtime").TransformComponent<import("../transform").SortYOptions>;
    readonly 'transform.sortColor': import("../runtime").TransformComponent<import("../transform").SortColorOptions>;
    readonly 'transform.flexX': import("../runtime").TransformComponent<import("../transform").FlexXOptions>;
    readonly 'transform.pack': import("../runtime").TransformComponent<import("../transform").PackOptions>;
    readonly 'transform.sample': import("../runtime").TransformComponent<import("../transform").SampleOptions>;
    readonly 'transform.filter': import("../runtime").TransformComponent<import("../transform").FilterOptions>;
    readonly 'coordinate.cartesian': import("../runtime").CoordinateComponent<import("../coordinate").CartesianOptions>;
    readonly 'coordinate.polar': import("../runtime").CoordinateComponent<import("../coordinate").PolarOptions>;
    readonly 'coordinate.transpose': import("../runtime").CoordinateComponent<import("../coordinate").TransposeOptions>;
    readonly 'coordinate.theta': import("../runtime").CoordinateComponent<import("..").ThetaCoordinate>;
    readonly 'coordinate.parallel': import("../runtime").CoordinateComponent<import("../coordinate").ParallelOptions>;
    readonly 'coordinate.fisheye': import("../runtime").CoordinateComponent<import("..").FisheyeCoordinate>;
    readonly 'coordinate.radial': import("../runtime").CoordinateComponent<import("../coordinate").RadialOptions>;
    readonly 'coordinate.radar': import("../runtime").CoordinateComponent<import("..").RadarCoordinate>;
    readonly 'coordinate.helix': import("../runtime").CoordinateComponent<import("..").HelixCoordinate>;
    readonly 'encode.constant': import("../runtime").EncodeComponent<import("../encode").ConstantOptions>;
    readonly 'encode.field': import("../runtime").EncodeComponent<import("../encode").FieldOptions>;
    readonly 'encode.transform': import("../runtime").EncodeComponent<import("../encode").TransformOptions>;
    readonly 'encode.column': import("../runtime").EncodeComponent<import("../encode").ColumnOptions>;
    readonly 'mark.interval': import("..").MarkComponent<import("../mark").IntervalOptions>;
    readonly 'mark.rect': import("..").MarkComponent<import("../mark").RectOptions>;
    readonly 'mark.line': import("..").MarkComponent<import("../mark").LineOptions>;
    readonly 'mark.point': import("..").MarkComponent<import("../mark").PointOptions>;
    readonly 'mark.text': import("..").MarkComponent<import("../mark").TextOptions>;
    readonly 'mark.cell': import("..").MarkComponent<import("../mark").CellOptions>;
    readonly 'mark.area': import("..").MarkComponent<import("../mark").AreaOptions>;
    readonly 'mark.link': import("..").MarkComponent<import("../mark").LinkOptions>;
    readonly 'mark.image': import("..").MarkComponent<import("../mark").ImageOptions>;
    readonly 'mark.polygon': import("..").MarkComponent<import("../mark").PolygonOptions>;
    readonly 'mark.box': import("..").MarkComponent<import("../mark").BoxOptions>;
    readonly 'mark.vector': import("..").MarkComponent<import("../mark").VectorOptions>;
    readonly 'mark.lineX': import("..").MarkComponent<import("../mark").LineXOptions>;
    readonly 'mark.lineY': import("..").MarkComponent<import("../mark").LineYOptions>;
    readonly 'mark.connector': import("..").MarkComponent<import("../mark").ConnectorOptions>;
    readonly 'mark.range': import("..").MarkComponent<import("../mark").RangeOptions>;
    readonly 'mark.rangeX': import("..").MarkComponent<import("../mark").RangeXOptions>;
    readonly 'mark.rangeY': import("..").MarkComponent<import("../mark").RangeYOptions>;
    readonly 'mark.path': import("..").MarkComponent<import("../mark/path").PathOptions>;
    readonly 'mark.shape': import("..").MarkComponent<import("../mark").ShapeOptions>;
    readonly 'mark.density': import("..").MarkComponent<import("../mark").DensityOptions>;
    readonly 'mark.heatmap': import("..").MarkComponent<import("../mark").HeatmapOptions>;
    readonly 'mark.wordCloud': import("../runtime").CompositeMarkComponent<import("../mark").WordCloudOptions>;
    readonly 'palette.category10': import("../runtime").PaletteComponent<import("../palette").Category10Options>;
    readonly 'palette.category20': import("../runtime").PaletteComponent<import("../palette").Category20Options>;
    readonly 'scale.linear': import("../runtime").ScaleComponent<import("../scale").LinearOptions>;
    readonly 'scale.ordinal': import("../runtime").ScaleComponent<import("../scale").OrdinalOptions>;
    readonly 'scale.band': import("../runtime").ScaleComponent<import("../scale").BandOptions>;
    readonly 'scale.identity': import("../runtime").ScaleComponent<import("../scale").IdentityOptions>;
    readonly 'scale.point': import("../runtime").ScaleComponent<import("../scale").PointOptions>;
    readonly 'scale.time': import("../runtime").ScaleComponent<import("../scale").TimeOptions>;
    readonly 'scale.log': import("../runtime").ScaleComponent<import("../scale").LogOptions>;
    readonly 'scale.pow': import("../runtime").ScaleComponent<import("../scale").PowOptions>;
    readonly 'scale.sqrt': import("../runtime").ScaleComponent<import("../scale").SqrtOptions>;
    readonly 'scale.threshold': import("../runtime").ScaleComponent<import("../scale").ThresholdOptions>;
    readonly 'scale.quantile': import("../runtime").ScaleComponent<import("../scale").QuantileOptions>;
    readonly 'scale.quantize': import("../runtime").ScaleComponent<import("../scale").QuantizeOptions>;
    readonly 'scale.sequential': import("../runtime").ScaleComponent<import("../scale").SequentialOptions>;
    readonly 'scale.constant': import("../runtime").ScaleComponent<import("../scale").ConstantOptions>;
    readonly 'theme.classic': import("../runtime").ThemeComponent<import("../runtime").G2Theme>;
    readonly 'theme.classicDark': import("../runtime").ThemeComponent<import("../runtime").G2Theme>;
    readonly 'theme.academy': import("../runtime").ThemeComponent<import("../runtime").G2Theme>;
    readonly 'theme.light': import("../runtime").ThemeComponent<import("../runtime").G2Theme>;
    readonly 'theme.dark': import("../runtime").ThemeComponent<import("../runtime").G2Theme>;
    readonly 'component.axisX': import("..").GuideComponentComponent<import("..").AxisOptions>;
    readonly 'component.axisY': import("..").GuideComponentComponent<import("..").AxisOptions>;
    readonly 'component.legendCategory': import("..").GuideComponentComponent<import("../component").LegendCategoryOptions>;
    readonly 'component.legendContinuous': import("..").GuideComponentComponent<import("../component").LegendContinuousOptions>;
    readonly 'component.legends': import("..").GuideComponentComponent<import("../component").LegendsOptions>;
    readonly 'component.title': import("..").GuideComponentComponent<import("../runtime").G2Title>;
    readonly 'component.sliderX': import("..").GuideComponentComponent<import("../component/slider").SliderOptions>;
    readonly 'component.sliderY': import("..").GuideComponentComponent<import("../component/slider").SliderOptions>;
    readonly 'component.scrollbarX': import("..").GuideComponentComponent<import("../component/scrollbar").ScrollbarOptions>;
    readonly 'component.scrollbarY': import("..").GuideComponentComponent<import("../component/scrollbar").ScrollbarOptions>;
    readonly 'animation.scaleInX': import("../runtime").AnimationComponent<import("../animation/types").Animation>;
    readonly 'animation.scaleOutX': import("../runtime").AnimationComponent<import("../animation/types").Animation>;
    readonly 'animation.scaleInY': import("../runtime").AnimationComponent<import("../animation/types").Animation>;
    readonly 'animation.scaleOutY': import("../runtime").AnimationComponent<import("../animation/types").Animation>;
    readonly 'animation.waveIn': import("../runtime").AnimationComponent<import("../animation/types").Animation>;
    readonly 'animation.fadeIn': import("../runtime").AnimationComponent<import("../animation/types").Animation>;
    readonly 'animation.fadeOut': import("../runtime").AnimationComponent<import("../animation/types").Animation>;
    readonly 'animation.zoomIn': import("../runtime").AnimationComponent<import("../animation/types").Animation>;
    readonly 'animation.zoomOut': import("../runtime").AnimationComponent<import("../animation/types").Animation>;
    readonly 'animation.pathIn': import("../runtime").AnimationComponent<import("../animation/types").Animation>;
    readonly 'animation.morphing': import("../runtime").AnimationComponent<import("../animation").MorphingOptions>;
    readonly 'animation.growInX': import("../runtime").AnimationComponent<import("../animation/types").Animation>;
    readonly 'animation.growInY': import("../runtime").AnimationComponent<import("../animation/types").Animation>;
    readonly 'interaction.elementHighlight': typeof ElementHighlight;
    readonly 'interaction.elementHighlightByX': typeof ElementHighlightByX;
    readonly 'interaction.elementHighlightByColor': typeof ElementHighlightByColor;
    readonly 'interaction.elementSelect': typeof ElementSelect;
    readonly 'interaction.elementSelectByX': typeof ElementSelectByX;
    readonly 'interaction.elementSelectByColor': typeof ElementSelectByColor;
    readonly 'interaction.fisheye': typeof ChartFisheye;
    readonly 'interaction.chartIndex': typeof ChartIndex;
    readonly 'interaction.tooltip': typeof Tooltip;
    readonly 'interaction.legendFilter': typeof LegendFilter;
    readonly 'interaction.legendHighlight': typeof LegendHighlight;
    readonly 'interaction.brushHighlight': typeof BrushHighlight;
    readonly 'interaction.brushXHighlight': typeof BrushXHighlight;
    readonly 'interaction.brushYHighlight': typeof BrushYHighlight;
    readonly 'interaction.brushAxisHighlight': typeof BrushAxisHighlight;
    readonly 'interaction.brushFilter': typeof BrushFilter;
    readonly 'interaction.brushXFilter': typeof BrushXFilter;
    readonly 'interaction.brushYFilter': typeof BrushYFilter;
    readonly 'interaction.sliderFilter': typeof SliderFilter;
    readonly 'interaction.scrollbarFilter': typeof ScrollbarFilter;
    readonly 'interaction.poptip': typeof Poptip;
    readonly 'interaction.treemapDrillDown': typeof TreemapDrillDown;
    readonly 'interaction.elementPointMove': typeof ElementPointMove;
    readonly 'composition.spaceLayer': import("../runtime").CompositionComponent<import("../composition").SpaceLayerOptions>;
    readonly 'composition.spaceFlex': import("../runtime").CompositionComponent<import("../composition").SpaceFlexOptions>;
    readonly 'composition.facetRect': import("../runtime").CompositionComponent<import("../composition").FacetRectOptions>;
    readonly 'composition.repeatMatrix': import("../runtime").CompositionComponent<import("..").RepeatMatrixComposition>;
    readonly 'composition.facetCircle': import("../runtime").CompositionComponent<import("..").FacetCircleComposition>;
    readonly 'composition.timingKeyframe': import("../runtime").CompositionComponent<import("../composition").TimingKeyframeOptions>;
    readonly 'labelTransform.overlapHide': import("../runtime").LabelTransformComponent<import("../label-transform").OverlapHideOptions>;
    readonly 'labelTransform.overlapDodgeY': import("../runtime").LabelTransformComponent<import("../label-transform").OverlapDodgeYOptions>;
    readonly 'labelTransform.overflowHide': import("../runtime").LabelTransformComponent<import("../label-transform").OverflowHideOptions>;
    readonly 'labelTransform.contrastReverse': import("../runtime").LabelTransformComponent<import("../label-transform").ContrastReverseOptions>;
    readonly 'labelTransform.overflowStroke': import("../runtime").LabelTransformComponent<import("../label-transform").OverflowStrokeOptions>;
    readonly 'labelTransform.exceedAdjust': import("../runtime").LabelTransformComponent<import("../label-transform").ExceedAdjustOptions>;
};
