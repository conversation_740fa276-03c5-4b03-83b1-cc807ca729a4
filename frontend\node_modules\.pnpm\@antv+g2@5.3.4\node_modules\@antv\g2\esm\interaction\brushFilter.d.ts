export declare function brushFilter(root: any, { filter, reset, brushRegion, extent: optionalExtent, reverse, emitter, scale, coordinate, selection, series, ...rest }: {
    [x: string]: any;
    filter: any;
    reset: any;
    brushRegion: any;
    extent: any;
    reverse: any;
    emitter: any;
    scale: any;
    coordinate: any;
    selection: any;
    series?: boolean;
}): () => void;
export declare function BrushFilter({ hideX, hideY, ...rest }: {
    [x: string]: any;
    hideX?: boolean;
    hideY?: boolean;
}): (target: any, viewInstances: any, emitter: any) => () => void;
