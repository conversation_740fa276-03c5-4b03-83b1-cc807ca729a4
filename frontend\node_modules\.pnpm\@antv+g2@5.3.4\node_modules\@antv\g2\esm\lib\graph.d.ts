export declare function graphlib(): {
    readonly 'data.arc': import("../runtime").DataComponent<import("../data").ArcOptions>;
    readonly 'data.cluster': import("../runtime").DataComponent<import("../data").ClusterOptions>;
    readonly 'mark.forceGraph': import("../runtime").CompositeMarkComponent<import("../mark").ForceGraphOptions>;
    readonly 'mark.tree': import("../runtime").CompositeMarkComponent<import("../mark").TreeOptions>;
    readonly 'mark.pack': import("../runtime").CompositionComponent<import("../mark").PackOptions>;
    readonly 'mark.sankey': import("../runtime").CompositeMarkComponent<import("../mark").SankeyOptions>;
    readonly 'mark.chord': import("../runtime").CompositeMarkComponent<import("../mark").ChordOptions>;
    readonly 'mark.treemap': import("../runtime").CompositionComponent<import("../mark").TreemapOptions>;
};
