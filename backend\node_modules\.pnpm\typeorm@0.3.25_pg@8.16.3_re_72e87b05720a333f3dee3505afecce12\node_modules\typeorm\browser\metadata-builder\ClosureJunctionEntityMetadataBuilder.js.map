{"version": 3, "sources": ["../browser/src/metadata-builder/ClosureJunctionEntityMetadataBuilder.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,cAAc,EAAE,MAAM,4BAA4B,CAAA;AAC3D,OAAO,EAAE,cAAc,EAAE,MAAM,4BAA4B,CAAA;AAC3D,OAAO,EAAE,kBAAkB,EAAE,MAAM,gCAAgC,CAAA;AAEnE,OAAO,EAAE,aAAa,EAAE,MAAM,2BAA2B,CAAA;AAEzD;;;GAGG;AACH,MAAM,OAAO,oCAAoC;IAC7C,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAoB,UAAsB;QAAtB,eAAU,GAAV,UAAU,CAAY;IAAG,CAAC;IAE9C,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,KAAK,CAAC,2BAA2C;QAC7C,gCAAgC;QAChC,MAAM,cAAc,GAAG,IAAI,cAAc,CAAC;YACtC,2BAA2B,EAAE,2BAA2B;YACxD,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,IAAI,EAAE;gBACF,MAAM,EAAE,EAAE;gBACV,IAAI,EACA,2BAA2B,CAAC,WAAW;oBACvC,2BAA2B,CAAC,WAAW,CAAC,gBAAgB;oBACpD,CAAC,CAAC,2BAA2B,CAAC,WAAW;yBAClC,gBAAgB;oBACvB,CAAC,CAAC,2BAA2B,CAAC,sBAAsB;gBAC5D,IAAI,EAAE,kBAAkB;aAC3B;SACJ,CAAC,CAAA;QACF,cAAc,CAAC,KAAK,EAAE,CAAA;QAEtB,wEAAwE;QACxE,2BAA2B,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,aAAa,EAAE,EAAE;YACjE,cAAc,CAAC,UAAU,CAAC,IAAI,CAC1B,IAAI,cAAc,CAAC;gBACf,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,cAAc,EAAE,cAAc;gBAC9B,WAAW,EAAE,UAAU;gBACvB,gBAAgB,EAAE,aAAa;gBAC/B,IAAI,EAAE;oBACF,MAAM,EAAE,EAAE;oBACV,IAAI,EAAE,SAAS;oBACf,YAAY,EACR,2BAA2B,CAAC,WAAW;wBACvC,2BAA2B,CAAC,WAAW;6BAClC,kBAAkB;wBACnB,CAAC,CAAC,2BAA2B,CAAC,WAAW,CAAC,kBAAkB,CACtD,aAAa,CAChB;wBACH,CAAC,CAAC,aAAa,CAAC,YAAY,GAAG,WAAW;oBAClD,OAAO,EAAE;wBACL,OAAO,EAAE,IAAI;wBACb,MAAM,EAAE,aAAa,CAAC,MAAM;wBAC5B,IAAI,EAAE,aAAa,CAAC,IAAI;wBACxB,QAAQ,EAAE,aAAa,CAAC,QAAQ;wBAChC,KAAK,EAAE,aAAa,CAAC,KAAK;wBAC1B,SAAS,EAAE,aAAa,CAAC,SAAS;wBAClC,KAAK,EAAE,aAAa,CAAC,KAAK;wBAC1B,QAAQ,EAAE,aAAa,CAAC,QAAQ;wBAChC,OAAO,EAAE,aAAa,CAAC,OAAO;wBAC9B,SAAS,EAAE,aAAa,CAAC,SAAS;qBACrC;iBACJ;aACJ,CAAC,CACL,CAAA;YACD,cAAc,CAAC,UAAU,CAAC,IAAI,CAC1B,IAAI,cAAc,CAAC;gBACf,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,cAAc,EAAE,cAAc;gBAC9B,WAAW,EAAE,YAAY;gBACzB,gBAAgB,EAAE,aAAa;gBAC/B,IAAI,EAAE;oBACF,MAAM,EAAE,EAAE;oBACV,IAAI,EAAE,SAAS;oBACf,YAAY,EACR,2BAA2B,CAAC,WAAW;wBACvC,2BAA2B,CAAC,WAAW;6BAClC,oBAAoB;wBACrB,CAAC,CAAC,2BAA2B,CAAC,WAAW,CAAC,oBAAoB,CACxD,aAAa,CAChB;wBACH,CAAC,CAAC,aAAa,CAAC,YAAY,GAAG,aAAa;oBACpD,OAAO,EAAE;wBACL,OAAO,EAAE,IAAI;wBACb,MAAM,EAAE,aAAa,CAAC,MAAM;wBAC5B,IAAI,EAAE,aAAa,CAAC,IAAI;wBACxB,QAAQ,EAAE,aAAa,CAAC,QAAQ;wBAChC,KAAK,EAAE,aAAa,CAAC,KAAK;wBAC1B,SAAS,EAAE,aAAa,CAAC,SAAS;wBAClC,KAAK,EAAE,aAAa,CAAC,KAAK;wBAC1B,QAAQ,EAAE,aAAa,CAAC,QAAQ;wBAChC,OAAO,EAAE,aAAa,CAAC,OAAO;wBAC9B,SAAS,EAAE,aAAa,CAAC,SAAS;qBACrC;iBACJ;aACJ,CAAC,CACL,CAAA;QACL,CAAC,CAAC,CAAA;QAEF,cAAc,CAAC,UAAU,GAAG;YACxB,IAAI,aAAa,CAAC;gBACd,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;gBACvC,IAAI,EAAE;oBACF,MAAM,EAAE,cAAc,CAAC,MAAM;oBAC7B,WAAW,EAAE,IAAI;iBACpB;aACJ,CAAC;YACF,IAAI,aAAa,CAAC;gBACd,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;gBACvC,IAAI,EAAE;oBACF,MAAM,EAAE,cAAc,CAAC,MAAM;oBAC7B,WAAW,EAAE,IAAI;iBACpB;aACJ,CAAC;SACL,CAAA;QAED,mGAAmG;QACnG,IAAI,2BAA2B,CAAC,eAAe,EAAE,CAAC;YAC9C,cAAc,CAAC,UAAU,CAAC,IAAI,CAC1B,IAAI,cAAc,CAAC;gBACf,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,cAAc,EAAE,cAAc;gBAC9B,IAAI,EAAE;oBACF,MAAM,EAAE,EAAE;oBACV,IAAI,EAAE,SAAS;oBACf,YAAY,EAAE,OAAO;oBACrB,OAAO,EAAE;wBACL,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,eAAe;6BACvC,SAAS;qBACjB;iBACJ;aACJ,CAAC,CACL,CAAA;QACL,CAAC;QAED,qCAAqC;QACrC,wFAAwF;QACxF,cAAc,CAAC,WAAW,GAAG;YACzB,IAAI,kBAAkB,CAAC;gBACnB,cAAc,EAAE,cAAc;gBAC9B,wBAAwB,EAAE,2BAA2B;gBACrD,OAAO,EAAE,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;gBACvC,iBAAiB,EAAE,2BAA2B,CAAC,cAAc;gBAC7D,QAAQ,EACJ,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,OAAO;oBAC3C,CAAC,CAAC,WAAW;oBACb,CAAC,CAAC,SAAS;aACtB,CAAC;YACF,IAAI,kBAAkB,CAAC;gBACnB,cAAc,EAAE,cAAc;gBAC9B,wBAAwB,EAAE,2BAA2B;gBACrD,OAAO,EAAE,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;gBACvC,iBAAiB,EAAE,2BAA2B,CAAC,cAAc;gBAC7D,QAAQ,EACJ,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,OAAO;oBAC3C,CAAC,CAAC,WAAW;oBACb,CAAC,CAAC,SAAS;aACtB,CAAC;SACL,CAAA;QAED,OAAO,cAAc,CAAA;IACzB,CAAC;CACJ", "file": "ClosureJunctionEntityMetadataBuilder.js", "sourcesContent": ["import { EntityMetadata } from \"../metadata/EntityMetadata\"\nimport { ColumnMetadata } from \"../metadata/ColumnMetadata\"\nimport { ForeignKeyMetadata } from \"../metadata/ForeignKeyMetadata\"\nimport { DataSource } from \"../data-source/DataSource\"\nimport { IndexMetadata } from \"../metadata/IndexMetadata\"\n\n/**\n * Creates EntityMetadata for junction tables of the closure entities.\n * Closure junction tables are tables generated by closure entities.\n */\nexport class ClosureJunctionEntityMetadataBuilder {\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(private connection: DataSource) {}\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Builds EntityMetadata for the closure junction of the given closure entity.\n     */\n    build(parentClosureEntityMetadata: EntityMetadata) {\n        // create entity metadata itself\n        const entityMetadata = new EntityMetadata({\n            parentClosureEntityMetadata: parentClosureEntityMetadata,\n            connection: this.connection,\n            args: {\n                target: \"\",\n                name:\n                    parentClosureEntityMetadata.treeOptions &&\n                    parentClosureEntityMetadata.treeOptions.closureTableName\n                        ? parentClosureEntityMetadata.treeOptions\n                              .closureTableName\n                        : parentClosureEntityMetadata.tableNameWithoutPrefix,\n                type: \"closure-junction\",\n            },\n        })\n        entityMetadata.build()\n\n        // create ancestor and descendant columns for new closure junction table\n        parentClosureEntityMetadata.primaryColumns.forEach((primaryColumn) => {\n            entityMetadata.ownColumns.push(\n                new ColumnMetadata({\n                    connection: this.connection,\n                    entityMetadata: entityMetadata,\n                    closureType: \"ancestor\",\n                    referencedColumn: primaryColumn,\n                    args: {\n                        target: \"\",\n                        mode: \"virtual\",\n                        propertyName:\n                            parentClosureEntityMetadata.treeOptions &&\n                            parentClosureEntityMetadata.treeOptions\n                                .ancestorColumnName\n                                ? parentClosureEntityMetadata.treeOptions.ancestorColumnName(\n                                      primaryColumn,\n                                  )\n                                : primaryColumn.propertyName + \"_ancestor\",\n                        options: {\n                            primary: true,\n                            length: primaryColumn.length,\n                            type: primaryColumn.type,\n                            unsigned: primaryColumn.unsigned,\n                            width: primaryColumn.width,\n                            precision: primaryColumn.precision,\n                            scale: primaryColumn.scale,\n                            zerofill: primaryColumn.zerofill,\n                            charset: primaryColumn.charset,\n                            collation: primaryColumn.collation,\n                        },\n                    },\n                }),\n            )\n            entityMetadata.ownColumns.push(\n                new ColumnMetadata({\n                    connection: this.connection,\n                    entityMetadata: entityMetadata,\n                    closureType: \"descendant\",\n                    referencedColumn: primaryColumn,\n                    args: {\n                        target: \"\",\n                        mode: \"virtual\",\n                        propertyName:\n                            parentClosureEntityMetadata.treeOptions &&\n                            parentClosureEntityMetadata.treeOptions\n                                .descendantColumnName\n                                ? parentClosureEntityMetadata.treeOptions.descendantColumnName(\n                                      primaryColumn,\n                                  )\n                                : primaryColumn.propertyName + \"_descendant\",\n                        options: {\n                            primary: true,\n                            length: primaryColumn.length,\n                            type: primaryColumn.type,\n                            unsigned: primaryColumn.unsigned,\n                            width: primaryColumn.width,\n                            precision: primaryColumn.precision,\n                            scale: primaryColumn.scale,\n                            zerofill: primaryColumn.zerofill,\n                            charset: primaryColumn.charset,\n                            collation: primaryColumn.collation,\n                        },\n                    },\n                }),\n            )\n        })\n\n        entityMetadata.ownIndices = [\n            new IndexMetadata({\n                entityMetadata: entityMetadata,\n                columns: [entityMetadata.ownColumns[0]],\n                args: {\n                    target: entityMetadata.target,\n                    synchronize: true,\n                },\n            }),\n            new IndexMetadata({\n                entityMetadata: entityMetadata,\n                columns: [entityMetadata.ownColumns[1]],\n                args: {\n                    target: entityMetadata.target,\n                    synchronize: true,\n                },\n            }),\n        ]\n\n        // if tree level column was defined by a closure entity then add it to the junction columns as well\n        if (parentClosureEntityMetadata.treeLevelColumn) {\n            entityMetadata.ownColumns.push(\n                new ColumnMetadata({\n                    connection: this.connection,\n                    entityMetadata: entityMetadata,\n                    args: {\n                        target: \"\",\n                        mode: \"virtual\",\n                        propertyName: \"level\",\n                        options: {\n                            type: this.connection.driver.mappedDataTypes\n                                .treeLevel,\n                        },\n                    },\n                }),\n            )\n        }\n\n        // create junction table foreign keys\n        // Note: CASCADE is not applied to mssql because it does not support multi cascade paths\n        entityMetadata.foreignKeys = [\n            new ForeignKeyMetadata({\n                entityMetadata: entityMetadata,\n                referencedEntityMetadata: parentClosureEntityMetadata,\n                columns: [entityMetadata.ownColumns[0]],\n                referencedColumns: parentClosureEntityMetadata.primaryColumns,\n                onDelete:\n                    this.connection.driver.options.type === \"mssql\"\n                        ? \"NO ACTION\"\n                        : \"CASCADE\",\n            }),\n            new ForeignKeyMetadata({\n                entityMetadata: entityMetadata,\n                referencedEntityMetadata: parentClosureEntityMetadata,\n                columns: [entityMetadata.ownColumns[1]],\n                referencedColumns: parentClosureEntityMetadata.primaryColumns,\n                onDelete:\n                    this.connection.driver.options.type === \"mssql\"\n                        ? \"NO ACTION\"\n                        : \"CASCADE\",\n            }),\n        ]\n\n        return entityMetadata\n    }\n}\n"], "sourceRoot": ".."}