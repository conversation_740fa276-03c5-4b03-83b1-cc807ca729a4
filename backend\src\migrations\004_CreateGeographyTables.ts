import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreateGeographyTables1700000004 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // 确保PostGIS扩展已安装
    await queryRunner.query('CREATE EXTENSION IF NOT EXISTS postgis;');

    // 创建地理分布表
    await queryRunner.createTable(
      new Table({
        name: 'distribution_ranges',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'gen_random_uuid()',
          },
          {
            name: 'species_id',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'kmlFilePath',
            type: 'varchar',
            length: '500',
            isNullable: true,
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
        ],
        foreignKeys: [
          {
            columnNames: ['species_id'],
            referencedTableName: 'species',
            referencedColumnNames: ['id'],
            onDelete: 'CASCADE',
          },
        ],
        indices: [
          {
            name: 'IDX_DISTRIBUTION_SPECIES_ID',
            columnNames: ['species_id'],
          },
        ],
      }),
      true
    );

    // 添加几何字段（PostGIS）
    await queryRunner.query(`
      ALTER TABLE distribution_ranges 
      ADD COLUMN geometry GEOMETRY(MULTIPOLYGON, 4326);
    `);

    // 创建空间索引
    await queryRunner.query(`
      CREATE INDEX IDX_DISTRIBUTION_GEOMETRY 
      ON distribution_ranges USING GIST(geometry);
    `);

    // 创建操作日志表
    await queryRunner.createTable(
      new Table({
        name: 'operation_logs',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'gen_random_uuid()',
          },
          {
            name: 'user_id',
            type: 'uuid',
            isNullable: true,
          },
          {
            name: 'action',
            type: 'varchar',
            length: '100',
            isNullable: false,
          },
          {
            name: 'resource',
            type: 'varchar',
            length: '100',
            isNullable: false,
          },
          {
            name: 'resourceId',
            type: 'varchar',
            length: '100',
            isNullable: true,
          },
          {
            name: 'details',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'ipAddress',
            type: 'varchar',
            length: '45',
            isNullable: true,
          },
          {
            name: 'userAgent',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
        ],
        foreignKeys: [
          {
            columnNames: ['user_id'],
            referencedTableName: 'users',
            referencedColumnNames: ['id'],
            onDelete: 'SET NULL',
          },
        ],
        indices: [
          {
            name: 'IDX_LOGS_USER_ID',
            columnNames: ['user_id'],
          },
          {
            name: 'IDX_LOGS_ACTION',
            columnNames: ['action'],
          },
          {
            name: 'IDX_LOGS_RESOURCE',
            columnNames: ['resource'],
          },
          {
            name: 'IDX_LOGS_CREATED_AT',
            columnNames: ['createdAt'],
          },
        ],
      }),
      true
    );

    // 创建音频识别记录表
    await queryRunner.createTable(
      new Table({
        name: 'audio_recognition_records',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'gen_random_uuid()',
          },
          {
            name: 'user_id',
            type: 'uuid',
            isNullable: true,
          },
          {
            name: 'originalFileName',
            type: 'varchar',
            length: '255',
            isNullable: false,
          },
          {
            name: 'filePath',
            type: 'varchar',
            length: '500',
            isNullable: false,
          },
          {
            name: 'fileSize',
            type: 'bigint',
            isNullable: true,
          },
          {
            name: 'recognitionResult',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'confidence',
            type: 'decimal',
            precision: 5,
            scale: 4,
            isNullable: true,
          },
          {
            name: 'status',
            type: 'enum',
            enum: ['pending', 'processing', 'completed', 'failed'],
            default: "'pending'",
          },
          {
            name: 'errorMessage',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'completedAt',
            type: 'timestamp',
            isNullable: true,
          },
        ],
        foreignKeys: [
          {
            columnNames: ['user_id'],
            referencedTableName: 'users',
            referencedColumnNames: ['id'],
            onDelete: 'SET NULL',
          },
        ],
        indices: [
          {
            name: 'IDX_RECOGNITION_USER_ID',
            columnNames: ['user_id'],
          },
          {
            name: 'IDX_RECOGNITION_STATUS',
            columnNames: ['status'],
          },
          {
            name: 'IDX_RECOGNITION_CREATED_AT',
            columnNames: ['createdAt'],
          },
        ],
      }),
      true
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('audio_recognition_records');
    await queryRunner.dropTable('operation_logs');
    await queryRunner.dropTable('distribution_ranges');
  }
}
