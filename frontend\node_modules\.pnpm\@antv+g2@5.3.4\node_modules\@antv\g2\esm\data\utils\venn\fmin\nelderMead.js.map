{"version": 3, "file": "nelderMead.js", "sourceRoot": "", "sources": ["../../../../../src/data/utils/venn/fmin/nelderMead.ts"], "names": [], "mappings": "AAAA,OAAO,EAAc,WAAW,EAAE,MAAM,SAAS,CAAC;AAElD,6DAA6D;AAC7D,MAAM,UAAU,UAAU,CAAC,CAAC,EAAE,EAAE,EAAE,UAAgB;IAChD,UAAU,GAAG,UAAU,IAAI,EAAE,CAAC;IAE9B,MAAM,aAAa,GAAG,UAAU,CAAC,aAAa,IAAI,EAAE,CAAC,MAAM,GAAG,GAAG,CAAC;IAClE,MAAM,YAAY,GAAG,UAAU,CAAC,YAAY,IAAI,IAAI,CAAC;IACrD,MAAM,SAAS,GAAG,UAAU,CAAC,SAAS,IAAI,KAAK,CAAC;IAChD,MAAM,aAAa,GAAG,UAAU,CAAC,aAAa,IAAI,IAAI,CAAC;IACvD,MAAM,YAAY,GAAG,UAAU,CAAC,aAAa,IAAI,IAAI,CAAC;IACtD,MAAM,GAAG,GAAG,UAAU,CAAC,GAAG,KAAK,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9D,MAAM,GAAG,GAAG,UAAU,CAAC,GAAG,KAAK,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9D,MAAM,GAAG,GAAG,UAAU,CAAC,GAAG,KAAK,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;IACjE,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;IACtE,IAAI,OAAO,CAAC;IAEZ,sBAAsB;IACtB,MAAM,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC;IACpB,MAAM,OAAO,GAAG,IAAI,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IACjC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;IAChB,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IACtB,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;IAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;QAC1B,MAAM,KAAK,GAAG,EAAE,CAAC,KAAK,EAAE,CAAC;QACzB,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,SAAS,CAAC;QAC1D,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;QACvB,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;QAC7B,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;KAC3B;IAED,SAAS,aAAa,CAAC,KAAK;QAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACrC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;SAC1B;QACD,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,CAAC;IAC3B,CAAC;IAED,MAAM,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC;IAExC,MAAM,QAAQ,GAAG,EAAE,CAAC,KAAK,EAAE,CAAC;IAC5B,MAAM,SAAS,GAAG,EAAE,CAAC,KAAK,EAAE,CAAC;IAC7B,MAAM,UAAU,GAAG,EAAE,CAAC,KAAK,EAAE,CAAC;IAC9B,MAAM,QAAQ,GAAG,EAAE,CAAC,KAAK,EAAE,CAAC;IAE5B,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,aAAa,EAAE,EAAE,SAAS,EAAE;QAC9D,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAExB,IAAI,UAAU,CAAC,OAAO,EAAE;YACtB,4DAA4D;YAC5D,wDAAwD;YACxD,MAAM,aAAa,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE;gBACtC,MAAM,KAAK,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC;gBACxB,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC;gBAChB,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC;gBAChB,OAAO,KAAK,CAAC;YACf,CAAC,CAAC,CAAC;YACH,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;YAE1C,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC;gBACtB,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE;gBACrB,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE;gBACjB,OAAO,EAAE,aAAa;aACvB,CAAC,CAAC;SACJ;QAED,OAAO,GAAG,CAAC,CAAC;QACZ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;YAC1B,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACtE;QAED,IACE,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,aAAa;YACvD,OAAO,GAAG,YAAY,EACtB;YACA,MAAM;SACP;QAED,iEAAiE;QACjE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;YAC1B,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;gBAC1B,QAAQ,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aAC9B;YACD,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;SAClB;QAED,2EAA2E;QAC3E,QAAQ;QACR,MAAM,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;QACzB,WAAW,CAAC,SAAS,EAAE,CAAC,GAAG,GAAG,EAAE,QAAQ,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACvD,SAAS,CAAC,EAAE,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC;QAE5B,gEAAgE;QAChE,IAAI,SAAS,CAAC,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;YAChC,WAAW,CAAC,QAAQ,EAAE,CAAC,GAAG,GAAG,EAAE,QAAQ,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;YACtD,QAAQ,CAAC,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC;YAC1B,IAAI,QAAQ,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,EAAE;gBAC9B,aAAa,CAAC,QAAQ,CAAC,CAAC;aACzB;iBAAM;gBACL,aAAa,CAAC,SAAS,CAAC,CAAC;aAC1B;SACF;QAED,oEAAoE;QACpE,WAAW;aACN,IAAI,SAAS,CAAC,EAAE,IAAI,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE;YAC1C,IAAI,YAAY,GAAG,KAAK,CAAC;YAEzB,IAAI,SAAS,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,EAAE;gBAC3B,2BAA2B;gBAC3B,WAAW,CAAC,UAAU,EAAE,CAAC,GAAG,GAAG,EAAE,QAAQ,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;gBACxD,UAAU,CAAC,EAAE,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC;gBAC9B,IAAI,UAAU,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,EAAE;oBAC5B,aAAa,CAAC,UAAU,CAAC,CAAC;iBAC3B;qBAAM;oBACL,YAAY,GAAG,IAAI,CAAC;iBACrB;aACF;iBAAM;gBACL,4BAA4B;gBAC5B,WAAW,CAAC,UAAU,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,EAAE,QAAQ,EAAE,GAAG,GAAG,GAAG,EAAE,KAAK,CAAC,CAAC;gBACnE,UAAU,CAAC,EAAE,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC;gBAC9B,IAAI,UAAU,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,EAAE;oBAChC,aAAa,CAAC,UAAU,CAAC,CAAC;iBAC3B;qBAAM;oBACL,YAAY,GAAG,IAAI,CAAC;iBACrB;aACF;YAED,IAAI,YAAY,EAAE;gBAChB,wCAAwC;gBACxC,IAAI,KAAK,IAAI,CAAC;oBAAE,MAAM;gBAEtB,iBAAiB;gBACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;oBACvC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;oBAClE,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;iBAC/B;aACF;SACF;aAAM;YACL,aAAa,CAAC,SAAS,CAAC,CAAC;SAC1B;KACF;IAED,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACxB,OAAO,EAAE,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;AAC9C,CAAC"}