import { HeatmapRendererData, HeatmapRendererOptions } from './types';
/**
 * Render a heatmap with canvas.
 * See [heatmap.js](https://github.com/pa7/heatmap.js/blob/master/src/renderer/canvas2d.js).
 */
export declare function HeatmapRenderer(width: number, height: number, min: number, max: number, data: HeatmapRendererData[], options: HeatmapRendererOptions, createCanvas: () => HTMLCanvasElement): CanvasRenderingContext2D;
