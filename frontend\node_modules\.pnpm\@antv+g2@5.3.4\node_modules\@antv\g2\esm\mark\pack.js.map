{"version": 3, "file": "pack.js", "sourceRoot": "", "sources": ["../../src/mark/pack.ts"], "names": [], "mappings": ";;;;;;;;;;;AAAA,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AAC9C,OAAO,EACL,QAAQ,EACR,SAAS,EACT,IAAI,IAAI,UAAU,GACnB,MAAM,2BAA2B,CAAC;AAEnC,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAE5C,OAAO,EAAE,YAAY,EAAE,MAAM,eAAe,CAAC;AAC7C,OAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAEhC,MAAM,0BAA0B,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;IACrD,IAAI,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC;IACrB,OAAO,EAAE,CAAC;IACV,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK;CAClC,CAAC,CAAC;AAEH,MAAM,mBAAmB,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;IACtD,IAAI,EAAE,OAAO;IACb,IAAI,EAAE,KAAK;IACX,MAAM,EAAE,KAAK;IACb,KAAK,EAAE;QACL,CAAC,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE;QACzB,CAAC,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE;QAC1B,IAAI,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE;KAC3B;IACD,MAAM,EAAE;QACN,CAAC,EAAE,GAAG;QACN,CAAC,EAAE,GAAG;QACN,IAAI,EAAE,GAAG;QACT,KAAK,EAAE,OAAO;KACf;IACD,KAAK,EAAE;QACL,IAAI,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS;QAC3E,MAAM,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS;KAC1E;CACF,CAAC,CAAC;AAEH,MAAM,qBAAqB,GAAG;IAC5B,IAAI,EAAE,EAAE;IACR,QAAQ,EAAE,QAAQ;IAClB,YAAY,EAAE,MAAM;IACpB,QAAQ,EAAE,IAAI;IACd,QAAQ,EAAE,CAAC;IACX,aAAa,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;CAC9B,CAAC;AACF,MAAM,uBAAuB,GAAG;IAC9B,KAAK,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI;IACzB,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC;CAC5B,CAAC;AAUF,MAAM,aAAa,GAAG,CAAC,IAAI,EAAE,MAAkB,EAAE,MAAM,EAAE,EAAE;IACzD,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,CAAC;IACzB,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QACxB,CAAC,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;QACpC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IACpB,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;IAC1E,aAAa;IACb,UAAU,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC;IAC7D,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC;AAC5B,CAAC,CAAC;AAIF,MAAM,CAAC,MAAM,IAAI,GAAoB,CAAC,WAAW,EAAE,OAAO,EAAE,EAAE;IAC5D,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;IAElC,MAAM,EACJ,IAAI,EACJ,MAAM,GAAG,EAAE,EACX,KAAK,GAAG,EAAE,EACV,KAAK,GAAG,EAAE,EACV,MAAM,GAAG,EAAE,EACX,MAAM,GAAG,EAAE,EACX,OAAO,GAAG,EAAE,KAEV,WAAW,EADV,UAAU,UACX,WAAW,EATT,mEASL,CAAc,CAAC;IAEhB,MAAM,eAAe,GAAG,mBAAmB,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;IAEnE,MAAM,eAAe,GAAG,aAAa,CACnC,IAAI,EACJ,OAAO,CAAC,EAAE,EAAE,0BAA0B,CAAC,KAAK,EAAE,MAAM,CAAC,EAAE,MAAM,CAAC,EAC9D,OAAO,CAAC,EAAE,EAAE,eAAe,CAAC,QAAQ,CAAC,EAAE,MAAM,CAAC,CAC/C,CAAC;IAEF,MAAM,UAAU,GAAG,SAAS,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IAE7C,OAAO,OAAO,CAAC,EAAE,EAAE,eAAe,gCAChC,IAAI,EAAE,eAAe,EACrB,MAAM;QACN,KAAK;QACL,KAAK,EACL,MAAM,EAAE;4CAED,qBAAqB,GACrB,UAAU;YAEf,GAAG,MAAM;SACV,IACE,UAAU,KACb,OAAO,EAAE,YAAY,CAAC,OAAO,EAAE,uBAAuB,CAAC,EACvD,IAAI,EAAE,KAAK,IACX,CAAC;AACL,CAAC,CAAC;AAEF,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC"}