{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ant-design/charts": "^2.6.0", "@ant-design/icons": "^6.0.0", "@reduxjs/toolkit": "^2.8.2", "@types/react-router-dom": "^5.3.3", "@types/wavesurfer.js": "^6.0.12", "antd": "^5.26.5", "axios": "^1.10.0", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "leaflet": "^1.9.4", "react": "^19.1.0", "react-dom": "^19.1.0", "react-leaflet": "^5.0.0", "react-redux": "^9.2.0", "react-router-dom": "^7.7.0", "wavesurfer.js": "^7.10.0"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/leaflet": "^1.9.20", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "prettier": "^3.6.2", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}}