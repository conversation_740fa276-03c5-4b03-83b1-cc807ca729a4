{"version": 3, "file": "overflowStroke.js", "sourceRoot": "", "sources": ["../../src/label-transform/overflowStroke.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,UAAU,EAAE,MAAM,gBAAgB,CAAC;AAC5C,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AACxD,OAAO,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AAC9C,OAAO,EAAE,YAAY,EAAE,MAAM,SAAS,CAAC;AAIvC;;;GAGG;AACH,SAAS,sBAAsB,CAAC,OAAsB;;IACpD,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;IAE3C,wCAAwC;IACxC,IAAI,CAAC,UAAU,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;QAC1C,OAAO,MAAM,CAAC,OAAO,CAAC,CAAC;KACxB;IAED,iDAAiD;IACjD,MAAM,YAAY,GAAG,OAAO,CAAC,SAAS,CAAC,IAAI,CAAkB,CAAC;IAC9D,YAAY,CAAC,KAAK,CAAC,UAAU,GAAG,QAAQ,CAAC;IAEzC,UAAU,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,EAAE;QAC/B,MAAM,SAAS,GAAG,SAAS,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;QAClD,IAAI,SAAS,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;YACrC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;SACpD;IACH,CAAC,CAAC,CAAC;IAEH,MAAA,OAAO,CAAC,UAAU,0CAAE,WAAW,CAAC,YAAY,CAAC,CAAC;IAC9C,MAAM,MAAM,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC;IACpC,YAAY,CAAC,OAAO,EAAE,CAAC;IAEvB,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,MAAM,CAAC,MAAM,cAAc,GAA+B,CAAC,OAAO,EAAE,EAAE;IACpE,MAAM,EAAE,OAAO,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,SAAS,GAAG,CAAC,EAAE,GAAG,OAAO,CAAC;IAE9D,OAAO,CAAC,MAAuB,EAAE,EAAE;QACjC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;;YACnB,MAAM,gBAAgB,GAAG,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACpD,MAAM,SAAS,GAAG,MAAA,MAAA,CAAC,CAAC,UAAU,CAAC,IAAI,mCAAI,CAAC,CAAC,WAAW,CAAC,IAAI,mCAAI,MAAM,CAAC;YAEpE,MAAM,UAAU,GAAG,SAAS,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC;YACxD,MAAM,aAAa,GAAG,SAAS,CAAC,sBAAsB,CAAC,gBAAgB,CAAC,CAAC,CAAC;YAE1E,IAAI,UAAU,CAAC,UAAU,EAAE,aAAa,EAAE,SAAS,CAAC,EAAE;gBACpD,oDAAoD;gBACpD,kDAAkD;gBAClD,MAAM,WAAW,GAAG,YAAY,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,OAAO,CAAC,CAAC;gBAEjE,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;aAC/B;iBAAM;gBACL,gDAAgD;gBAChD,CAAC,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;aAC7B;QACH,CAAC,CAAC,CAAC;QACH,OAAO,MAAM,CAAC;IAChB,CAAC,CAAC;AACJ,CAAC,CAAC"}