{"version": 3, "file": "brushFilter.js", "sourceRoot": "", "sources": ["../../src/interaction/brushFilter.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AACrC,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAC5C,OAAO,EAAE,WAAW,EAAE,MAAM,gBAAgB,CAAC;AAC7C,OAAO,EAAE,KAAK,IAAI,WAAW,EAAE,MAAM,kBAAkB,CAAC;AACxD,OAAO,EAAE,cAAc,EAAE,MAAM,SAAS,CAAC;AAEzC,wBAAwB;AACxB,SAAS,QAAQ,CAAC,QAAQ,GAAG,GAAG;IAC9B,IAAI,YAAY,GAAG,IAAI,CAAC;IACxB,OAAO,CAAC,CAAC,EAAE,EAAE;QACX,MAAM,EAAE,SAAS,EAAE,GAAG,CAAC,CAAC;QACxB,IAAI,YAAY,KAAK,IAAI,IAAI,SAAS,GAAG,YAAY,GAAG,QAAQ,EAAE;YAChE,YAAY,GAAG,SAAS,CAAC;YACzB,OAAO,IAAI,CAAC;SACb;QACD,YAAY,GAAG,SAAS,CAAC;QACzB,OAAO,KAAK,CAAC;IACf,CAAC,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,WAAW,CACzB,IAAI,EACJ,EAYC;QAZD,EACE,MAAM,EACN,KAAK,EACL,WAAW,EACX,MAAM,EAAE,cAAc,EACtB,OAAO,EACP,OAAO,EACP,KAAK,EACL,UAAU,EACV,SAAS,EACT,MAAM,GAAG,KAAK,OAEf,EADI,IAAI,cAXT,gHAYC,CADQ;IAGT,MAAM,UAAU,GAAG,SAAS,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IAC3C,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;IAChE,MAAM,MAAM,GAAG,cAAc;QAC3B,CAAC,CAAC,cAAc;QAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;IAClC,MAAM,UAAU,GAAG,QAAQ,EAAE,CAAC;IAE9B,MAAM,KAAK,GAAG,WAAW,CAAC,IAAI,kCACzB,UAAU,KACb,MAAM;QACN,WAAW;QACX,OAAO;QACP,YAAY,IACZ,CAAC;IAEH,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;IAEtC,6BAA6B;IAC7B,SAAS,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,KAAK;QACvC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE;YAAE,OAAO;QACjC,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC;QACzB,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;QACvC,KAAK,CAAC,MAAM,EAAE,CAAC;IACjB,CAAC;IAED,uBAAuB;IACvB,SAAS,KAAK,CAAC,CAAC;QACd,IAAI,UAAU,CAAC,CAAC,CAAC,EAAE;YACjB,CAAC,CAAC,WAAW,GAAG,IAAI,CAAC;YACrB,KAAK,CAAC,CAAC,CAAC,CAAC;SACV;IACH,CAAC;IAED,MAAM,QAAQ,GAAG,CAAC,EAAE,WAAW,EAAE,IAAI,EAAE,EAAE,EAAE;QACzC,IAAI,WAAW;YAAE,OAAO;QACxB,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC;QAC3B,MAAM,CAAC,SAAS,EAAE,EAAE,WAAW,EAAE,KAAK,EAAE,CAAC,CAAC;IAC5C,CAAC,CAAC;IACF,OAAO,CAAC,EAAE,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;IAErC,OAAO,GAAG,EAAE;QACV,KAAK,CAAC,OAAO,EAAE,CAAC;QAChB,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;QACtC,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;IAC3C,CAAC,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,WAAW,CAAC,EAAuC;QAAvC,EAAE,KAAK,GAAG,IAAI,EAAE,KAAK,GAAG,IAAI,OAAW,EAAN,IAAI,cAArC,kBAAuC,CAAF;IAC/D,OAAO,CAAC,MAAM,EAAE,aAAa,EAAE,OAAO,EAAE,EAAE;QACxC,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAC;QAC3E,MAAM,QAAQ,GAAG,cAAc,CAAC,SAAS,CAAC,CAAC;QAC3C,MAAM,cAAc,GAAG;YACrB,QAAQ,EAAE,MAAM;YAChB,eAAe,EAAE,KAAK;YACtB,UAAU,EAAE,MAAM;YAClB,oBAAoB,EAAE,GAAG;YACzB,OAAO,EAAE,KAAK;SACf,CAAC;QAEF,IAAI,QAAQ,GAAG,KAAK,CAAC;QACrB,IAAI,SAAS,GAAG,KAAK,CAAC;QACtB,IAAI,OAAO,GAAG,IAAI,CAAC;QAEnB,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC;QACnC,OAAO,WAAW,CAAC,QAAQ,gCACzB,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAC7C,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;gBAC1B,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;gBACtC,OAAO,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;YACtD,CAAC,EACD,MAAM,EAAE,CAAO,SAAS,EAAE,KAAK,EAAE,EAAE;gBACjC,0BAA0B;gBAC1B,IAAI,SAAS;oBAAE,OAAO;gBACtB,SAAS,GAAG,IAAI,CAAC;gBAEjB,qDAAqD;gBACrD,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,GAAG,SAAS,CAAC;gBAErC,QAAQ,CAAC,aAAa,EAAE,CAAC,OAAO,EAAE,EAAE;oBAClC,MAAM,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;oBAC1B,MAAM,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAClC,OAAO,CACL;wBACE,wCAAwC;wBACxC,IAAI,kCACC,CAAC,KAAK,IAAI,EAAE,CAAC,EAAE,EAAE,SAAS,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC,GACnD,CAAC,KAAK,IAAI,EAAE,CAAC,EAAE,EAAE,SAAS,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC,CACvD;qBACF,EACD,IAAI,EACJ;wBACE,4CAA4C;wBAC5C,KAAK,EAAE;4BACL,CAAC,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE;4BACnC,CAAC,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE;yBACpC;qBACF,CACF,CACF,CAAC;oBAEF,uCACK,WAAW,KACd,KAAK,EAAE,QAAQ,EACf,IAAI,EAAE,IAAI,IACV;gBACJ,CAAC,CAAC,CAAC;gBAEH,cAAc;gBACd,OAAO,CAAC,IAAI,CAAC,cAAc,kCACtB,KAAK,KACR,IAAI,EAAE,EAAE,SAAS,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,IACvC,CAAC;gBAEH,MAAM,QAAQ,GAAG,MAAM,MAAM,EAAE,CAAC;gBAChC,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC;gBACxB,SAAS,GAAG,KAAK,CAAC;gBAClB,QAAQ,GAAG,IAAI,CAAC;YAClB,CAAC,CAAA,EACD,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;gBACf,IAAI,SAAS,IAAI,CAAC,QAAQ;oBAAE,OAAO;gBAEnC,cAAc;gBACd,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC;gBACvB,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC;gBACvC,MAAM,OAAO,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC,MAAM,CAAC;gBAC3C,MAAM,OAAO,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC,MAAM,CAAC;gBAC3C,OAAO,CAAC,IAAI,CAAC,cAAc,kCACtB,KAAK,KACR,IAAI,EAAE,EAAE,SAAS,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,IACvC,CAAC;gBACH,QAAQ,GAAG,KAAK,CAAC;gBACjB,OAAO,GAAG,IAAI,CAAC;gBACf,QAAQ,CAAC,aAAa,CAAC,CAAC;gBACxB,MAAM,EAAE,CAAC;YACX,CAAC,EACD,MAAM,EAAE,SAAS,EACjB,OAAO;YACP,KAAK;YACL,UAAU,IACP,cAAc,GACd,IAAI,EACP,CAAC;IACL,CAAC,CAAC;AACJ,CAAC"}