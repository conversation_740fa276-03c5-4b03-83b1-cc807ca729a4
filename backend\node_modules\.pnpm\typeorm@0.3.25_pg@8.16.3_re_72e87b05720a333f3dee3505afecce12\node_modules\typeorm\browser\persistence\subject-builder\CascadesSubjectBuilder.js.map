{"version": 3, "sources": ["../browser/src/persistence/subject-builder/CascadesSubjectBuilder.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAA;AAEpC,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAA;AAEpD;;;GAGG;AACH,MAAM,OAAO,sBAAsB;IAC/B,wEAAwE;IACxE,cAAc;IACd,wEAAwE;IAExE,YAAsB,WAAsB;QAAtB,gBAAW,GAAX,WAAW,CAAW;IAAG,CAAC;IAEhD,wEAAwE;IACxE,iBAAiB;IACjB,wEAAwE;IAExE;;OAEG;IACH,KAAK,CACD,OAAgB,EAChB,aAA4D;QAE5D,OAAO,CAAC,QAAQ;aACX,+BAA+B,CAC5B,OAAO,CAAC,MAAO,EACf,OAAO,CAAC,QAAQ,CAAC,SAAS,CAC7B,CAAC,sDAAsD;aACvD,OAAO,CAAC,CAAC,CAAC,QAAQ,EAAE,cAAc,EAAE,sBAAsB,CAAC,EAAE,EAAE;YAC5D,gHAAgH;YAChH,IACI,cAAc,KAAK,SAAS;gBAC5B,cAAc,KAAK,IAAI;gBACvB,CAAC,CAAC,QAAQ,CAAC,eAAe;oBACtB,CAAC,QAAQ,CAAC,eAAe;oBACzB,CAAC,QAAQ,CAAC,mBAAmB;oBAC7B,CAAC,QAAQ,CAAC,gBAAgB,CAAC;gBAE/B,OAAM;YAEV,0EAA0E;YAC1E,2FAA2F;YAC3F,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,cAAc,CAAC;gBAAE,OAAM;YAEjD,6FAA6F;YAC7F,MAAM,iCAAiC,GACnC,IAAI,CAAC,uBAAuB,CACxB,sBAAsB,CAAC,MAAM,EAC7B,cAAc,CACjB,CAAA;YACL,IAAI,iCAAiC,EAAE,CAAC;gBACpC,IACI,iCAAiC,CAAC,aAAa;oBAC/C,KAAK;oBAEL,sCAAsC;oBACtC,iCAAiC,CAAC,aAAa;wBAC3C,QAAQ,CAAC,eAAe,KAAK,IAAI;4BACjC,aAAa,KAAK,MAAM,CAAA;gBAChC,IACI,iCAAiC,CAAC,YAAY,KAAK,KAAK;oBAExD,mCAAmC;oBACnC,iCAAiC,CAAC,YAAY;wBAC1C,QAAQ,CAAC,eAAe,KAAK,IAAI;4BACjC,aAAa,KAAK,MAAM,CAAA;gBAChC,IACI,iCAAiC,CAAC,gBAAgB;oBAClD,KAAK;oBAEL,oCAAoC;oBACpC,iCAAiC,CAAC,gBAAgB;wBAC9C,QAAQ,CAAC,mBAAmB,KAAK,IAAI;4BACrC,aAAa,KAAK,aAAa,CAAA;gBACvC,IACI,iCAAiC,CAAC,cAAc;oBAChD,KAAK;oBAEL,qCAAqC;oBACrC,iCAAiC,CAAC,cAAc;wBAC5C,QAAQ,CAAC,gBAAgB,KAAK,IAAI;4BAClC,aAAa,KAAK,SAAS,CAAA;gBACnC,OAAM;YACV,CAAC;YAED,2CAA2C;YAC3C,yFAAyF;YACzF,MAAM,qBAAqB,GAAG,IAAI,OAAO,CAAC;gBACtC,QAAQ,EAAE,sBAAsB;gBAChC,aAAa,EAAE,OAAO;gBACtB,MAAM,EAAE,cAAc;gBACtB,aAAa,EACT,QAAQ,CAAC,eAAe,KAAK,IAAI;oBACjC,aAAa,KAAK,MAAM;gBAC5B,YAAY,EACR,QAAQ,CAAC,eAAe,KAAK,IAAI;oBACjC,aAAa,KAAK,MAAM;gBAC5B,gBAAgB,EACZ,QAAQ,CAAC,mBAAmB,KAAK,IAAI;oBACrC,aAAa,KAAK,aAAa;gBACnC,cAAc,EACV,QAAQ,CAAC,gBAAgB,KAAK,IAAI;oBAClC,aAAa,KAAK,SAAS;aAClC,CAAC,CAAA;YACF,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAA;YAE5C,kEAAkE;YAClE,IAAI,CAAC,KAAK,CAAC,qBAAqB,EAAE,aAAa,CAAC,CAAA;QACpD,CAAC,CAAC,CAAA;IACV,CAAC;IAED,wEAAwE;IACxE,oBAAoB;IACpB,wEAAwE;IAExE;;;OAGG;IACO,uBAAuB,CAC7B,YAA+B,EAC/B,MAAqB;QAErB,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE;YACrC,IAAI,CAAC,OAAO,CAAC,MAAM;gBAAE,OAAO,KAAK,CAAA;YAEjC,IAAI,OAAO,CAAC,MAAM,KAAK,MAAM;gBAAE,OAAO,IAAI,CAAA;YAE1C,OAAO,CACH,OAAO,CAAC,QAAQ,CAAC,MAAM,KAAK,YAAY;gBACxC,OAAO,CAAC,QAAQ,CAAC,eAAe,CAC5B,OAAO,CAAC,sBAAuB,EAC/B,MAAM,CACT,CACJ,CAAA;QACL,CAAC,CAAC,CAAA;IACN,CAAC;CACJ", "file": "CascadesSubjectBuilder.js", "sourcesContent": ["import { Subject } from \"../Subject\"\nimport { ObjectLiteral } from \"../../common/ObjectLiteral\"\nimport { ObjectUtils } from \"../../util/ObjectUtils\"\n\n/**\n * Finds all cascade operations of the given subject and cascade operations of the found cascaded subjects,\n * e.g. builds a cascade tree and creates a subjects for them.\n */\nexport class CascadesSubjectBuilder {\n    // ---------------------------------------------------------------------\n    // Constructor\n    // ---------------------------------------------------------------------\n\n    constructor(protected allSubjects: Subject[]) {}\n\n    // ---------------------------------------------------------------------\n    // Public Methods\n    // ---------------------------------------------------------------------\n\n    /**\n     * Builds a cascade subjects tree and pushes them in into the given array of subjects.\n     */\n    build(\n        subject: Subject,\n        operationType: \"save\" | \"remove\" | \"soft-remove\" | \"recover\",\n    ) {\n        subject.metadata\n            .extractRelationValuesFromEntity(\n                subject.entity!,\n                subject.metadata.relations,\n            ) // todo: we can create EntityMetadata.cascadeRelations\n            .forEach(([relation, relationEntity, relationEntityMetadata]) => {\n                // we need only defined values and insert, update, soft-remove or recover cascades of the relation should be set\n                if (\n                    relationEntity === undefined ||\n                    relationEntity === null ||\n                    (!relation.isCascadeInsert &&\n                        !relation.isCascadeUpdate &&\n                        !relation.isCascadeSoftRemove &&\n                        !relation.isCascadeRecover)\n                )\n                    return\n\n                // if relation entity is just a relation id set (for example post.tag = 1)\n                // then we don't really need to check cascades since there is no object to insert or update\n                if (!ObjectUtils.isObject(relationEntity)) return\n\n                // if we already has this entity in list of operated subjects then skip it to avoid recursion\n                const alreadyExistRelationEntitySubject =\n                    this.findByPersistEntityLike(\n                        relationEntityMetadata.target,\n                        relationEntity,\n                    )\n                if (alreadyExistRelationEntitySubject) {\n                    if (\n                        alreadyExistRelationEntitySubject.canBeInserted ===\n                        false\n                    )\n                        // if its not marked for insertion yet\n                        alreadyExistRelationEntitySubject.canBeInserted =\n                            relation.isCascadeInsert === true &&\n                            operationType === \"save\"\n                    if (\n                        alreadyExistRelationEntitySubject.canBeUpdated === false\n                    )\n                        // if its not marked for update yet\n                        alreadyExistRelationEntitySubject.canBeUpdated =\n                            relation.isCascadeUpdate === true &&\n                            operationType === \"save\"\n                    if (\n                        alreadyExistRelationEntitySubject.canBeSoftRemoved ===\n                        false\n                    )\n                        // if its not marked for removal yet\n                        alreadyExistRelationEntitySubject.canBeSoftRemoved =\n                            relation.isCascadeSoftRemove === true &&\n                            operationType === \"soft-remove\"\n                    if (\n                        alreadyExistRelationEntitySubject.canBeRecovered ===\n                        false\n                    )\n                        // if its not marked for recovery yet\n                        alreadyExistRelationEntitySubject.canBeRecovered =\n                            relation.isCascadeRecover === true &&\n                            operationType === \"recover\"\n                    return\n                }\n\n                // mark subject with what we can do with it\n                // and add to the array of subjects to load only if there is no same entity there already\n                const relationEntitySubject = new Subject({\n                    metadata: relationEntityMetadata,\n                    parentSubject: subject,\n                    entity: relationEntity,\n                    canBeInserted:\n                        relation.isCascadeInsert === true &&\n                        operationType === \"save\",\n                    canBeUpdated:\n                        relation.isCascadeUpdate === true &&\n                        operationType === \"save\",\n                    canBeSoftRemoved:\n                        relation.isCascadeSoftRemove === true &&\n                        operationType === \"soft-remove\",\n                    canBeRecovered:\n                        relation.isCascadeRecover === true &&\n                        operationType === \"recover\",\n                })\n                this.allSubjects.push(relationEntitySubject)\n\n                // go recursively and find other entities we need to insert/update\n                this.build(relationEntitySubject, operationType)\n            })\n    }\n\n    // ---------------------------------------------------------------------\n    // Protected Methods\n    // ---------------------------------------------------------------------\n\n    /**\n     * Finds subject where entity like given subject's entity.\n     * Comparison made by entity id.\n     */\n    protected findByPersistEntityLike(\n        entityTarget: Function | string,\n        entity: ObjectLiteral,\n    ): Subject | undefined {\n        return this.allSubjects.find((subject) => {\n            if (!subject.entity) return false\n\n            if (subject.entity === entity) return true\n\n            return (\n                subject.metadata.target === entityTarget &&\n                subject.metadata.compareEntities(\n                    subject.entityWithFulfilledIds!,\n                    entity,\n                )\n            )\n        })\n    }\n}\n"], "sourceRoot": "../.."}