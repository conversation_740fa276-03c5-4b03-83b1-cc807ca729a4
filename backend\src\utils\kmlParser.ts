import * as xml2js from 'xml2js';
import * as fs from 'fs/promises';

export interface Coordinate {
  longitude: number;
  latitude: number;
  altitude?: number;
}

export interface Polygon {
  outerBoundary: Coordinate[];
  innerBoundaries?: Coordinate[][];
}

export interface MultiPolygon {
  polygons: Polygon[];
}

export interface KMLParseResult {
  name?: string;
  description?: string;
  multiPolygon: MultiPolygon;
}

/**
 * 解析坐标字符串为坐标数组
 * KML坐标格式: "longitude,latitude,altitude longitude,latitude,altitude ..."
 */
function parseCoordinates(coordinatesStr: string): Coordinate[] {
  if (!coordinatesStr || typeof coordinatesStr !== 'string') {
    return [];
  }

  return coordinatesStr
    .trim()
    .split(/\s+/)
    .map(coordStr => {
      const parts = coordStr.split(',');
      if (parts.length < 2) return null;
      
      const longitude = parseFloat(parts[0]);
      const latitude = parseFloat(parts[1]);
      const altitude = parts[2] ? parseFloat(parts[2]) : undefined;
      
      if (isNaN(longitude) || isNaN(latitude)) return null;
      
      return { longitude, latitude, altitude };
    })
    .filter(coord => coord !== null) as Coordinate[];
}

/**
 * 解析多边形数据
 */
function parsePolygon(polygonData: any): Polygon | null {
  if (!polygonData) return null;

  const outerBoundary = polygonData.outerBoundaryIs?.[0]?.LinearRing?.[0]?.coordinates?.[0];
  const innerBoundaries = polygonData.innerBoundaryIs;

  if (!outerBoundary) return null;

  const polygon: Polygon = {
    outerBoundary: parseCoordinates(outerBoundary),
  };

  if (innerBoundaries && Array.isArray(innerBoundaries)) {
    polygon.innerBoundaries = innerBoundaries
      .map((inner: any) => {
        const coords = inner.LinearRing?.[0]?.coordinates?.[0];
        return coords ? parseCoordinates(coords) : null;
      })
      .filter((coords: any) => coords !== null);
  }

  return polygon.outerBoundary.length > 0 ? polygon : null;
}

/**
 * 解析KML文件
 */
export async function parseKMLFile(filePath: string): Promise<KMLParseResult[]> {
  try {
    const kmlContent = await fs.readFile(filePath, 'utf-8');
    return parseKMLString(kmlContent);
  } catch (error) {
    throw new Error(`读取KML文件失败: ${error}`);
  }
}

/**
 * 解析KML字符串
 */
export async function parseKMLString(kmlContent: string): Promise<KMLParseResult[]> {
  try {
    const parser = new xml2js.Parser({
      explicitArray: true,
      ignoreAttrs: false,
      mergeAttrs: true,
    });

    const result = await parser.parseStringPromise(kmlContent);
    const kml = result.kml || result.Document;
    
    if (!kml) {
      throw new Error('无效的KML格式');
    }

    const document = kml.Document?.[0] || kml;
    const placemarks = document.Placemark || [];

    const results: KMLParseResult[] = [];

    for (const placemark of placemarks) {
      const name = placemark.name?.[0];
      const description = placemark.description?.[0];
      
      // 处理多边形
      const polygons: Polygon[] = [];
      
      if (placemark.Polygon) {
        for (const polygonData of placemark.Polygon) {
          const polygon = parsePolygon(polygonData);
          if (polygon) {
            polygons.push(polygon);
          }
        }
      }
      
      // 处理多多边形
      if (placemark.MultiGeometry) {
        for (const multiGeom of placemark.MultiGeometry) {
          if (multiGeom.Polygon) {
            for (const polygonData of multiGeom.Polygon) {
              const polygon = parsePolygon(polygonData);
              if (polygon) {
                polygons.push(polygon);
              }
            }
          }
        }
      }

      if (polygons.length > 0) {
        results.push({
          name,
          description,
          multiPolygon: { polygons },
        });
      }
    }

    return results;
  } catch (error) {
    throw new Error(`解析KML内容失败: ${error}`);
  }
}

/**
 * 将解析结果转换为PostGIS的WKT格式
 */
export function convertToWKT(multiPolygon: MultiPolygon): string {
  const polygonWKTs = multiPolygon.polygons.map(polygon => {
    const outerRing = polygon.outerBoundary
      .map(coord => `${coord.longitude} ${coord.latitude}`)
      .join(', ');
    
    let polygonWKT = `((${outerRing}))`;
    
    if (polygon.innerBoundaries && polygon.innerBoundaries.length > 0) {
      const innerRings = polygon.innerBoundaries.map(boundary => {
        const ring = boundary
          .map(coord => `${coord.longitude} ${coord.latitude}`)
          .join(', ');
        return `(${ring})`;
      });
      
      polygonWKT = `((${outerRing}), ${innerRings.join(', ')})`;
    }
    
    return polygonWKT;
  });

  return `MULTIPOLYGON(${polygonWKTs.join(', ')})`;
}

/**
 * 验证坐标是否有效
 */
export function validateCoordinates(coordinates: Coordinate[]): boolean {
  if (!coordinates || coordinates.length < 3) {
    return false; // 多边形至少需要3个点
  }

  return coordinates.every(coord => 
    coord.longitude >= -180 && coord.longitude <= 180 &&
    coord.latitude >= -90 && coord.latitude <= 90
  );
}
