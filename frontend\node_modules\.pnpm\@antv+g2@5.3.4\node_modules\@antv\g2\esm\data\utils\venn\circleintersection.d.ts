/**
 * Returns the intersection area of a bunch of circles (where each circle
 * is an object having an x,y and radius property)
 */
export declare function intersectionArea(circles: any, stats?: any): number;
/**
 * Returns whether a point is contained by all of a list of circles
 */
export declare function containedInCircles(point: any, circles: any): boolean;
/** Circular segment area calculation. See http://mathworld.wolfram.com/CircularSegment.html */
export declare function circleArea(r: any, width: any): number;
/** Euclidean distance between two points */
export declare function distance(p1: any, p2: any): number;
/** Returns the overlap area of two circles of radius r1 and r2 - that
have their centers separated by distance d. Simpler faster
circle intersection for only two circles */
export declare function circleOverlap(r1: any, r2: any, d: any): number;
/** Given two circles (containing a x/y/radius attributes),
returns the intersecting points if possible.
note: doesn't handle cases where there are infinitely many
intersection points (circles are equivalent):, or only one intersection point*/
export declare function circleCircleIntersection(p1: any, p2: any): ({
    x: any;
    y: number;
} | {
    x: number;
    y: any;
})[];
/** Returns the center of a bunch of points */
export declare function getCenter(points: any): {
    x: number;
    y: number;
};
