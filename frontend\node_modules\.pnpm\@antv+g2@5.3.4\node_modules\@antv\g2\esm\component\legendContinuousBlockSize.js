import { LegendContinuous } from './legendContinuous';
import { LegendContinuousSize } from './legendContinuousSize';
export const LegendContinuousBlockSize = (options) => {
    return LegendContinuousSize(Object.assign({}, { block: true }, options));
};
LegendContinuousBlockSize.props = Object.assign(Object.assign({}, LegendContinuous.props), { defaultPosition: 'top', defaultOrientation: 'horizontal' });
//# sourceMappingURL=legendContinuousBlockSize.js.map