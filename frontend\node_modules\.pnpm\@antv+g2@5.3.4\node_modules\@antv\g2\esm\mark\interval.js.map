{"version": 3, "file": "interval.js", "sourceRoot": "", "sources": ["../../src/mark/interval.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,WAAW,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AACvD,OAAO,EACL,aAAa,EACb,cAAc,EACd,cAAc,EACd,eAAe,GAChB,MAAM,UAAU,CAAC;AAClB,OAAO,EACL,oBAAoB,EACpB,iBAAiB,EACjB,gBAAgB,EAChB,SAAS,GACV,MAAM,SAAS,CAAC;AAEjB,SAAS,SAAS,CAAC,KAAW,EAAE,CAAM;IACpC,OAAO,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7C,CAAC;AAED,MAAM,KAAK,GAAG;IACZ,IAAI,EAAE,aAAa;IACnB,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE,cAAc;IACtB,OAAO,EAAE,eAAe;CACzB,CAAC;AAIF;;;;;;;GAOG;AACH,MAAM,CAAC,MAAM,QAAQ,GAAwB,GAAG,EAAE;IAChD,OAAO,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,EAAE;QACzC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,KAAK,CAAC;QAE1D,gCAAgC;QAChC,2DAA2D;QAC3D,MAAM,CAAC,GAAG,KAAK,CAAC,CAAS,CAAC;QAC1B,MAAM,MAAM,GAAG,KAAK,CAAC,MAAc,CAAC;QACpC,MAAM,CAAC,KAAK,CAAC,GAAG,UAAU,CAAC,OAAO,EAAE,CAAC;QACrC,MAAM,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAClD,MAAM,IAAI,GAAG,CAAC,EAAE;YACd,CAAC,CAAC,CAAC,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;YACjD,CAAC,CAAC,CAAC,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,EAAE;gBAClC,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBACrB,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;gBACjB,OAAO,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;YAClC,CAAC,CAAC;QAEN,oDAAoD;QACpD,2DAA2D;QAC3D,MAAM,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,EAAE;YAChC,MAAM,UAAU,GAAG,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,aAAD,CAAC,uBAAD,CAAC,CAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrD,MAAM,KAAK,GAAG,UAAU,GAAG,KAAK,CAAC;YACjC,MAAM,MAAM,GAAG,CAAC,CAAC,CAAA,CAAC,aAAD,CAAC,uBAAD,CAAC,CAAG,CAAC,CAAC,CAAA,IAAI,CAAC,CAAC,GAAG,UAAU,CAAC;YAC3C,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;YAC1B,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;YACpC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjB,MAAM,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAClB,MAAM,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YACpB,MAAM,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YACpB,MAAM,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YACpB,MAAM,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YACpB,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAc,CAAC;QACrE,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF,QAAQ,CAAC,KAAK,GAAG;IACf,YAAY,EAAE,MAAM;IACpB,iBAAiB,EAAE,OAAO;IAC1B,SAAS,EAAE,KAAK;IAChB,KAAK;IACL,QAAQ,EAAE;QACR,GAAG,oBAAoB,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;QACvD,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE;QAC5C,EAAE,IAAI,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE;QAC7B,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE;QACjC,EAAE,IAAI,EAAE,MAAM,EAAE;KACjB;IACD,YAAY,EAAE;QACZ,GAAG,gBAAgB,EAAE;QACrB,EAAE,IAAI,EAAE,WAAW,EAAE;QACrB,EAAE,IAAI,EAAE,UAAU,EAAE;KACrB;IACD,aAAa,EAAE,CAAC,GAAG,iBAAiB,EAAE,EAAE,GAAG,SAAS,EAAE,CAAC;IACvD,WAAW,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE;CACpC,CAAC"}