{"version": 3, "file": "image.js", "sourceRoot": "", "sources": ["../../../src/shape/image/image.ts"], "names": [], "mappings": ";;;;;;;;;;;AACA,OAAO,EAAE,UAAU,EAAE,MAAM,UAAU,CAAC;AACtC,OAAO,EAAE,MAAM,EAAE,MAAM,uBAAuB,CAAC;AAC/C,OAAO,EAAE,CAAC,EAAE,MAAM,kBAAkB,CAAC;AAIrC,MAAM,CAAC,MAAM,KAAK,GAAqB,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;IAC1D,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC;IACzC,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE;QACjC,MAAM,EAAE,KAAK,EAAE,YAAY,KAAc,QAAQ,EAAjB,IAAI,UAAK,QAAQ,EAA3C,SAAgC,CAAW,CAAC;QAClD,MAAM,EAAE,KAAK,GAAG,YAAY,EAAE,GAAG,GAAG,EAAE,EAAE,IAAI,GAAG,EAAE,EAAE,SAAS,GAAG,EAAE,EAAE,GAAG,KAAK,CAAC;QAC5E,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,MAAM,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;QAC9C,MAAM,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC;QAE1B,oCAAoC;QACpC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,UAAU,CAAC,OAAO,EAAE,CAAC;QACpC,KAAK,GAAG,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QACzD,MAAM,GAAG,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;QAE7D,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACjC,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAElC,OAAO,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;aAC/C,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;aACtB,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;aACb,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;aACb,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC;aACjB,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC;aACtB,KAAK,CAAC,WAAW,EAAE,SAAS,CAAC;aAC7B,IAAI,CAAC,UAAU,EAAE,OAAO,CAAC;aACzB,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC;aACrB,KAAK,CAAC,QAAQ,EAAE,MAAM,CAAC;aACvB,IAAI,EAAE,CAAC;IACZ,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF,KAAK,CAAC,KAAK,GAAG;IACZ,qBAAqB,EAAE,QAAQ;IAC/B,sBAAsB,EAAE,UAAU;IAClC,oBAAoB,EAAE,SAAS;CAChC,CAAC"}