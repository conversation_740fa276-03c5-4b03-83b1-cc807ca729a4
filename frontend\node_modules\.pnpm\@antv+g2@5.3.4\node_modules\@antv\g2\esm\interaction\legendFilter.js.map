{"version": 3, "file": "legendFilter.js", "sourceRoot": "", "sources": ["../../src/interaction/legendFilter.ts"], "names": [], "mappings": ";;;;;;;;;AACA,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AAC/C,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAC5C,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,aAAa,EAAE,MAAM,SAAS,CAAC;AAE7D,MAAM,CAAC,MAAM,0BAA0B,GAAG,iBAAiB,CAAC;AAE5D,MAAM,CAAC,MAAM,4BAA4B,GAAG,mBAAmB,CAAC;AAEhE,MAAM,CAAC,MAAM,uBAAuB,GAAG,YAAY,CAAC;AAEpD,MAAM,CAAC,MAAM,uBAAuB,GAAG,6BAA6B,CAAC;AAErE,MAAM,CAAC,MAAM,uBAAuB,GAAG,4BAA4B,CAAC;AAEpE,MAAM,UAAU,QAAQ,CAAC,IAAI;IAC3B,OAAO,IAAI,CAAC,sBAAsB,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC;AACjE,CAAC;AAED,MAAM,UAAU,OAAO,CAAC,IAAI;IAC1B,OAAO,IAAI,CAAC,sBAAsB,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC;AACjE,CAAC;AAED,MAAM,UAAU,OAAO,CAAC,IAAI;IAC1B,OAAO,IAAI,CAAC,sBAAsB,CAAC,uBAAuB,CAAC,CAAC;AAC9D,CAAC;AAED,MAAM,UAAU,SAAS,CAAC,IAAI;IAC5B,OAAO,IAAI,CAAC,sBAAsB,CAAC,0BAA0B,CAAC,CAAC;AACjE,CAAC;AAED,MAAM,UAAU,mBAAmB,CAAC,IAAI;IACtC,OAAO,IAAI,CAAC,sBAAsB,CAAC,4BAA4B,CAAC,CAAC;AACnE,CAAC;AAED,MAAM,UAAU,mBAAmB,CAAC,IAAI,EAAE,QAAQ;IAChD,MAAM,OAAO,GAAG,CAAC,GAAG,SAAS,CAAC,IAAI,CAAC,EAAE,GAAG,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC;IAEnE,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;QACzB,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC,CAAC,CAAC;AACL,CAAC;AAED,MAAM,UAAU,MAAM,CAAC,IAAI;IACzB,gCAAgC;IAChC,IAAI,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC;IAC7B,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;QACjC,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC;KAC5B;IACD,OAAO,MAAM,CAAC,QAAQ,CAAC;AACzB,CAAC;AAED,MAAM,UAAU,YAAY,CAAC,IAAI;IAC/B,IAAI,KAAK,GAAG,IAAI,CAAC;IACjB,OAAO,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;QACzD,KAAK,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;KAC3B;IACD,OAAO,KAAK,CAAC,UAAU,CAAC;AAC1B,CAAC;AAED,SAAS,mBAAmB,CAC1B,IAAmB,EACnB,EACE,OAAO,EAAE,4DAA4D;AACrE,MAAM,EAAE,QAAQ,EAAE,sCAAsC;AACxD,KAAK,EAAE,OAAO,EAAE,qCAAqC;AACrD,KAAK,EAAE,qCAAqC;AAC5C,MAAM,EAAE,qCAAqC;AAC7C,OAAO,EACP,OAAO,EACP,KAAK,GAAG,EAAyB,EAAE,gBAAgB;EACpD;IAED,yBAAyB;IACzB,MAAM,SAAS,GAAG,IAAI,GAAG,EAAE,CAAC;IAC5B,MAAM,gBAAgB,GAAG,IAAI,GAAG,EAAE,CAAC;IACnC,MAAM,cAAc,GAAG,IAAI,GAAG,EAAE,CAAC;IAEjC,MAAM,EACJ,UAAU,GAAG;QACX,YAAY,EAAE,MAAM;QACpB,UAAU,EAAE,MAAM;QAClB,SAAS,EAAE,MAAM;KAClB,GACF,GAAG,KAAK,CAAC;IACV,MAAM,WAAW,GAAG,EAAE,UAAU,EAAE,SAAS,CAAC,UAAU,EAAE,QAAQ,CAAC,EAAE,CAAC;IACpE,MAAM,UAAU,GAAG,EAAE,UAAU,EAAE,SAAS,CAAC,UAAU,EAAE,OAAO,CAAC,EAAE,CAAC;IAClE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG,QAAQ,CACvD,WAAW,EACX,SAAS,CACV,CAAC;IACF,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG,QAAQ,CACvD,UAAU,EACV,SAAS,CACV,CAAC;IAEF,MAAM,KAAK,GAAoB,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;IACzD,IAAI,cAAc,GAAG,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IACtC,MAAM,iBAAiB,GAAG,GAAG,EAAE;QAC7B,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;YACxB,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC;YAC1B,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;YAC9B,MAAM,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;YAC5B,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;gBACnC,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;gBAC3B,IAAI,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;aAC3B;iBAAM;gBACL,OAAO,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;gBAC9B,OAAO,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;aAC9B;SACF;IACH,CAAC,CAAC;IAEF,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;QACxB,oBAAoB;QACpB,MAAM,YAAY,GAAG,GAAG,EAAE;YACxB,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QAC7B,CAAC,CAAC;QAEF,MAAM,UAAU,GAAG,GAAG,EAAE;YACtB,aAAa,CAAC,IAAI,CAAC,CAAC;QACtB,CAAC,CAAC;QAEF,MAAM,KAAK,GAAG,CAAO,KAAK,EAAE,EAAE;YAC5B,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC;YAC1B,MAAM,KAAK,GAAG,cAAc,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YAC5C,IAAI,KAAK,KAAK,CAAC,CAAC;gBAAE,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;;gBACxC,cAAc,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YACrC,MAAM,MAAM,CAAC,cAAc,CAAC,CAAC;YAC7B,iBAAiB,EAAE,CAAC;YAEpB,MAAM,EAAE,WAAW,GAAG,IAAI,EAAE,GAAG,KAAK,CAAC;YACrC,IAAI,CAAC,WAAW;gBAAE,OAAO;YACzB,IAAI,cAAc,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM,EAAE;gBAC1C,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,WAAW,EAAE,CAAC,CAAC;aAC/C;iBAAM;gBACL,eAAe;gBACf,OAAO,CAAC,IAAI,CAAC,eAAe,kCACvB,KAAK,KACR,WAAW,EACX,IAAI,EAAE;wBACJ,OAAO;wBACP,MAAM,EAAE,cAAc;qBACvB,IACD,CAAC;aACJ;QACH,CAAC,CAAA,CAAC;QAEF,2BAA2B;QAC3B,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACtC,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;QACpD,IAAI,CAAC,gBAAgB,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;QAChD,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAC3B,gBAAgB,CAAC,GAAG,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;QACzC,cAAc,CAAC,GAAG,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;KACtC;IAED,MAAM,QAAQ,GAAG,CAAO,KAAK,EAAE,EAAE;QAC/B,MAAM,EAAE,WAAW,EAAE,GAAG,KAAK,CAAC;QAC9B,IAAI,WAAW;YAAE,OAAO;QACxB,MAAM,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC;QACvB,MAAM,EAAE,OAAO,EAAE,gBAAgB,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;QACnD,IAAI,gBAAgB,KAAK,OAAO;YAAE,OAAO;QACzC,cAAc,GAAG,MAAM,CAAC;QACxB,MAAM,MAAM,CAAC,cAAc,CAAC,CAAC;QAC7B,iBAAiB,EAAE,CAAC;IACtB,CAAC,CAAA,CAAC;IAEF,MAAM,KAAK,GAAG,CAAO,KAAK,EAAE,EAAE;QAC5B,MAAM,EAAE,WAAW,EAAE,GAAG,KAAK,CAAC;QAC9B,IAAI,WAAW;YAAE,OAAO;QACxB,cAAc,GAAG,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAClC,MAAM,MAAM,CAAC,cAAc,CAAC,CAAC;QAC7B,iBAAiB,EAAE,CAAC;IACtB,CAAC,CAAA,CAAC;IAEF,OAAO,CAAC,EAAE,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC;IACtC,OAAO,CAAC,EAAE,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;IAElC,OAAO,GAAG,EAAE;QACV,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;YACxB,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;YACvD,IAAI,CAAC,mBAAmB,CAAC,cAAc,EAAE,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;YACrE,IAAI,CAAC,mBAAmB,CAAC,YAAY,EAAE,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;YACjE,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC;YACvC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;SACpC;IACH,CAAC,CAAC;AACJ,CAAC;AAED,SAAS,sBAAsB,CAAC,CAAC,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE;IACrE,MAAM,aAAa,GAAG,CAAC,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE;QAC9C,MAAM,CAAC,KAAK,CAAC,CAAC;QACd,OAAO,CAAC,IAAI,CAAC;YACX,WAAW,EAAE,IAAI;YACjB,IAAI,EAAE;gBACJ,OAAO;gBACP,MAAM,EAAE,KAAK;aACd;SACF,CAAC,CAAC;IACL,CAAC,CAAC;IACF,MAAM,CAAC,gBAAgB,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;IACtD,OAAO,GAAG,EAAE;QACV,MAAM,CAAC,mBAAmB,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;IAC3D,CAAC,CAAC;AACJ,CAAC;AAED,SAAe,UAAU,CACvB,OAAO,EAAE,iBAAiB;AAC1B,EACE,MAAM,EAAE,mBAAmB;AAC3B,OAAO,EAAE,kBAAkB;AAC3B,KAAK,EAAE,mBAAmB;AAC1B,OAAO,EAAE,2BAA2B;AACpC,QAAQ,EAAE,4BAA4B;AACtC,WAAW,EAAE,4BAA4B;AACzC,KAAK,GAAG,KAAK,EAAE,aAAa;EAC7B;;QAED,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC;QAC3C,QAAQ,CAAC,MAAM,EAAE,CAAC,WAAW,EAAE,EAAE;YAC/B,MAAM,EAAE,KAAK,EAAE,GAAG,WAAW,CAAC;YAC9B,wCAAwC;YACxC,kDAAkD;YAClD,MAAM,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;gBAClC,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS;oBAAE,OAAO,IAAI,CAAC;gBAEzC,2DAA2D;gBAC3D,MAAM,EAAE,SAAS,GAAG,EAAE,EAAE,IAAI,GAAG,EAAE,EAAE,GAAG,IAAI,CAAC;gBAC3C,MAAM,KAAK,GAAG,SAAS,CAAC,SAAS,CAC/B,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CACjE,CAAC;gBACF,MAAM,YAAY,GAAG,CAAC,GAAG,SAAS,CAAC,CAAC;gBACpC,IAAI,IAAI,CAAC,MAAM,EAAE;oBACf,YAAY,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC,EAAE;wBAChC,IAAI,EAAE,QAAQ;wBACd,CAAC,OAAO,CAAC,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE;qBAC9B,CAAC,CAAC;iBACJ;gBAED,4CAA4C;gBAC5C,MAAM,QAAQ,GAAG,MAAM,CAAC,WAAW,CACjC,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC;oBACxB,OAAO;oBACP,EAAE,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,CAAC,MAAM,EAAE;iBACpD,CAAC,CACH,CAAC;gBACF,OAAO,OAAO,CAAC,EAAE,EAAE,IAAI,gCACrB,SAAS,EAAE,YAAY,EACvB,KAAK,EAAE,QAAQ,IACZ,CAAC,CAAC,OAAO,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,KACnC,MAAM,EAAE,KAAK;wBACX,CAAC,CAAC,KAAK;wBACP,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,IACvE,CAAC;YACL,CAAC,CAAC,CAAC;YACH,uCAAY,WAAW,KAAE,KAAK,EAAE,QAAQ,IAAG;QAC7C,CAAC,CAAC,CAAC;QACH,MAAM,MAAM,EAAE,CAAC;IACjB,CAAC;CAAA;AAED,SAAS,YAAY,CAAC,MAAM,EAAE,OAAO;IACnC,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;QAC1B,UAAU,CAAC,KAAK,kCAAO,OAAO,KAAE,KAAK,EAAE,IAAI,IAAG,CAAC;KAChD;AACH,CAAC;AAED,MAAM,UAAU,YAAY;IAC1B,OAAO,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,EAAE;QACpC,MAAM,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC;QAC9B,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,OAAO,CAAC,CAAC;QACrD,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;QAElC,MAAM,UAAU,GAAG,CAAC,MAAM,EAAE,EAAE;YAC5B,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QAClD,CAAC,CAAC;QACF,MAAM,OAAO,GAAG;YACd,GAAG,SAAS,CAAC,SAAS,CAAC;YACvB,GAAG,mBAAmB,CAAC,SAAS,CAAC;SAClC,CAAC;QACF,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAEhD,MAAM,MAAM,GAAG,OAAO;YACpB,CAAC,CAAC,QAAQ,CAAC,YAAY,EAAE,EAAE,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;YAChD,CAAC,CAAC,QAAQ,CAAC,UAAU,EAAE,EAAE,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;QAEjD,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;YACrC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC3D,MAAM,QAAQ,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC;YACpC,MAAM,MAAM,GAAG;gBACb,MAAM;gBACN,OAAO;gBACP,QAAQ;gBACR,WAAW;aACZ,CAAC;YACF,IAAI,MAAM,CAAC,SAAS,KAAK,0BAA0B,EAAE;gBACnD,OAAO,mBAAmB,CAAC,SAAS,EAAE;oBACpC,OAAO,EAAE,OAAO;oBAChB,MAAM,EAAE,QAAQ;oBAChB,KAAK,EAAE,OAAO;oBACd,KAAK,EAAE,CAAC,CAAC,EAAE,EAAE;wBACX,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;wBAC9B,MAAM,EAAE,KAAK,EAAE,GAAG,KAAK,CAAC;wBACxB,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC;oBACvB,CAAC;oBACD,MAAM,EAAE,CAAC,KAAK,EAAE,EAAE;wBAChB,MAAM,OAAO,mCAAQ,MAAM,KAAE,KAAK,EAAE,OAAO,EAAE,IAAI,GAAE,CAAC;wBACpD,IAAI,OAAO;4BAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;;4BAChC,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;oBAChC,CAAC;oBACD,KAAK,EAAE,MAAM,CAAC,UAAU,CAAC,KAAK;oBAC9B,OAAO;oBACP,OAAO;iBACR,CAAC,CAAC;aACJ;iBAAM;gBACL,OAAO,sBAAsB,CAAC,SAAS,EAAE;oBACvC,MAAM;oBACN,MAAM,EAAE,CAAC,KAAK,EAAE,EAAE;wBAChB,MAAM,OAAO,mCAAQ,MAAM,KAAE,KAAK,EAAE,OAAO,EAAE,KAAK,GAAE,CAAC;wBACrD,IAAI,OAAO;4BAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;;4BAChC,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;oBAChC,CAAC;oBACD,OAAO;oBACP,OAAO;iBACR,CAAC,CAAC;aACJ;QACH,CAAC,CAAC,CAAC;QACH,OAAO,GAAG,EAAE;YACV,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC;QACxC,CAAC,CAAC;IACJ,CAAC,CAAC;AACJ,CAAC"}