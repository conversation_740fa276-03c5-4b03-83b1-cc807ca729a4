export * from './builtinSymbolLikes';
export * from './containsAllTypesByName';
export * from './getConstrainedTypeAtLocation';
export * from './getContextualType';
export * from './getDeclaration';
export * from './getSourceFileOfNode';
export * from './getTypeName';
export * from './isSymbolFromDefaultLibrary';
export * from './isTypeReadonly';
export * from './isUnsafeAssignment';
export * from './predicates';
export * from './propertyTypes';
export * from './requiresQuoting';
export * from './typeFlagUtils';
export * from './TypeOrValueSpecifier';
export * from './discriminateAnyType';
export { getDecorators, getModifiers, typescriptVersionIsAtLeast, } from '@typescript-eslint/typescript-estree';
//# sourceMappingURL=index.d.ts.map