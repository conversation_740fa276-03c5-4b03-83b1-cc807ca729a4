{"version": 3, "sources": ["../browser/src/metadata-args/JoinColumnMetadataArgs.ts"], "names": [], "mappings": "", "file": "JoinColumnMetadataArgs.js", "sourcesContent": ["/**\n * Arguments for JoinColumnMetadata class.\n */\nexport interface JoinColumnMetadataArgs {\n    /**\n     * Class to which this column is applied.\n     */\n    target: Function | string\n\n    /**\n     * Class's property name to which this column is applied.\n     */\n    propertyName: string\n\n    /**\n     * Name of the column.\n     */\n    name?: string\n\n    /**\n     * Name of the column in the entity to which this column is referenced.\n     * This is column property name, not a column database name.\n     */\n    referencedColumnName?: string\n\n    /**\n     * Name of the foreign key constraint.\n     */\n    foreignKeyConstraintName?: string\n}\n"], "sourceRoot": ".."}