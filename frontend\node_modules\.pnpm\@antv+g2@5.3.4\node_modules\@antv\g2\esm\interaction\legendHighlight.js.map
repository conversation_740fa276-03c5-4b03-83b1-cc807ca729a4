{"version": 3, "file": "legendHighlight.js", "sourceRoot": "", "sources": ["../../src/interaction/legendHighlight.ts"], "names": [], "mappings": "AAAA,OAAO,EAAa,KAAK,EAAE,MAAM,uBAAuB,CAAC;AACzD,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAC5C,OAAO,EACL,UAAU,EACV,gBAAgB,EAChB,QAAQ,EACR,aAAa,EACb,aAAa,GACd,MAAM,SAAS,CAAC;AACjB,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AAE/E,MAAM,UAAU,eAAe;IAC7B,OAAO,CAAC,OAAO,EAAE,CAAC,EAAE,OAAO,EAAE,EAAE;QAC7B,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC;QAC7C,MAAM,OAAO,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC;QACrC,MAAM,QAAQ,GAAG,gBAAgB,CAAC,SAAS,CAAC,CAAC;QAC7C,MAAM,SAAS,GAAG,CAAC,MAAM,EAAE,EAAE;YAC3B,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QACvC,CAAC,CAAC;QACF,MAAM,OAAO,GAAG,CAAC,OAAO,EAAE,EAAE;YAC1B,MAAM,EACJ,KAAK,EAAE,EAAE,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,GAC5B,GAAG,IAAI,CAAC;YACT,OAAO,KAAK,CAAC;QACf,CAAC,CAAC;QACF,MAAM,SAAS,GAAG,UAAU,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC,CAAC;QAC9D,MAAM,OAAO,GAAG,aAAa,CAAC,QAAQ,EAAE,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;QAC7D,MAAM,QAAQ,GAAG,EAAE,CAAC;QAEpB,+BAA+B;QAC/B,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;YAC5B,MAAM,OAAO,GAAG,CAAC,IAAI,EAAE,EAAE;gBACvB,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC,UAAU,CAAC;gBACnC,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC;gBACjC,MAAM,EAAE,KAAK,EAAE,GAAG,KAAK,CAAC;gBACxB,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;YAC3B,CAAC,CAAC;YACF,MAAM,OAAO,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;YAClC,MAAM,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;YAC9B,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;YAC/B,MAAM,YAAY,GAAG,KAAK,CAAW,QAAQ,EAAE,CAAC,CAAC,EAAE,EAAE,CACnD,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAClC,CAAC;YACF,MAAM,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE,EAAE,GAAG,MAAM,CAAC,UAAU,CAAC;YACtD,MAAM,EAAE,QAAQ,GAAG,EAAE,EAAE,GAAG,WAAW,CAAC;YACtC,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG,QAAQ,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YAE/D,+BAA+B;YAC/B,MAAM,WAAW,GAAG,EAAE,QAAQ,EAAE,SAAS,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE,CAAC;YAChE,MAAM,UAAU,GAAG,EAAE,QAAQ,EAAE,SAAS,CAAC,QAAQ,EAAE,OAAO,CAAC,EAAE,CAAC;YAC9D,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC;YACvE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC;YACtE,MAAM,iBAAiB,GAAG,CAAC,SAAS,EAAE,EAAE;gBACtC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;oBACxB,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;oBAC9B,MAAM,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;oBAC5B,IAAI,IAAI,KAAK,SAAS,IAAI,SAAS,KAAK,IAAI,EAAE;wBAC5C,OAAO,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;wBAC5B,OAAO,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;qBAC5B;yBAAM;wBACL,IAAI,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;wBACzB,IAAI,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;qBACzB;iBACF;YACH,CAAC,CAAC;YACF,MAAM,aAAa,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE;gBACpC,aAAa;gBACb,MAAM,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;gBAC5B,MAAM,UAAU,GAAG,IAAI,GAAG,CACvB,YAA0C,CAAC,GAAG,CAAC,KAAK,CAAC,CACvD,CAAC;gBACF,KAAK,MAAM,CAAC,IAAI,QAAQ,EAAE;oBACxB,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;wBAAE,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;;wBACxC,QAAQ,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;iBAC9B;gBACD,iBAAiB,CAAC,IAAI,CAAC,CAAC;gBAExB,eAAe;gBACf,MAAM,EAAE,WAAW,GAAG,IAAI,EAAE,GAAG,KAAK,CAAC;gBACrC,IAAI,CAAC,WAAW;oBAAE,OAAO;gBACzB,OAAO,CAAC,IAAI,CAAC,kBAAkB,kCAC1B,KAAK,KACR,WAAW,EACX,IAAI,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,IACxB,CAAC;YACL,CAAC,CAAC;YAEF,MAAM,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC;YAElC,qCAAqC;YACrC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;gBACxB,MAAM,WAAW,GAAG,CAAC,KAAK,EAAE,EAAE;oBAC5B,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;gBAC7B,CAAC,CAAC;gBACF,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;gBAClD,eAAe,CAAC,GAAG,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;aACxC;YAED,qCAAqC;YACrC,MAAM,YAAY,GAAG,CAAC,KAAK,EAAE,EAAE;gBAC7B,KAAK,MAAM,CAAC,IAAI,QAAQ;oBAAE,WAAW,CAAC,CAAC,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;gBAC/D,iBAAiB,CAAC,IAAI,CAAC,CAAC;gBAExB,eAAe;gBACf,MAAM,EAAE,WAAW,GAAG,IAAI,EAAE,GAAG,KAAK,CAAC;gBACrC,IAAI,CAAC,WAAW;oBAAE,OAAO;gBACzB,OAAO,CAAC,IAAI,CAAC,oBAAoB,EAAE,EAAE,WAAW,EAAE,CAAC,CAAC;YACtD,CAAC,CAAC;YAEF,MAAM,WAAW,GAAG,CAAC,KAAK,EAAE,EAAE;gBAC5B,MAAM,EAAE,WAAW,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC;gBACpC,IAAI,WAAW;oBAAE,OAAO;gBACxB,MAAM,EAAE,OAAO,EAAE,gBAAgB,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC;gBAClD,IAAI,gBAAgB,KAAK,OAAO;oBAAE,OAAO;gBACzC,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC;gBACrD,IAAI,CAAC,IAAI;oBAAE,OAAO;gBAClB,aAAa,CAAC,EAAE,WAAW,EAAE,KAAK,EAAE,EAAE,IAAI,CAAC,CAAC;YAC9C,CAAC,CAAC;YAEF,MAAM,aAAa,GAAG,CAAC,KAAK,EAAE,EAAE;gBAC9B,MAAM,EAAE,WAAW,EAAE,GAAG,KAAK,CAAC;gBAC9B,IAAI,WAAW;oBAAE,OAAO;gBACxB,YAAY,CAAC,EAAE,WAAW,EAAE,KAAK,EAAE,CAAC,CAAC;YACvC,CAAC,CAAC;YAEF,MAAM,CAAC,gBAAgB,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;YACtD,OAAO,CAAC,EAAE,CAAC,kBAAkB,EAAE,WAAW,CAAC,CAAC;YAC5C,OAAO,CAAC,EAAE,CAAC,oBAAoB,EAAE,aAAa,CAAC,CAAC;YAEhD,MAAM,OAAO,GAAG,GAAG,EAAE;gBACnB,MAAM,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC;gBACzC,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE,WAAW,CAAC,CAAC;gBAC7C,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAAE,aAAa,CAAC,CAAC;gBACjD,KAAK,MAAM,CAAC,IAAI,EAAE,WAAW,CAAC,IAAI,eAAe,EAAE;oBACjD,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;iBACvC;YACH,CAAC,CAAC;YACF,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SACxB;QAED,OAAO,GAAG,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAC5C,CAAC,CAAC;AACJ,CAAC"}