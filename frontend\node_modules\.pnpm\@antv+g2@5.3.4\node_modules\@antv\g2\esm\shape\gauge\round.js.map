{"version": 3, "file": "round.js", "sourceRoot": "", "sources": ["../../../src/shape/gauge/round.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,EAAE,MAAM,YAAY,CAAC;AAKlC,4BAA4B;AAC5B,MAAM,IAAI,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE;IAC9B,OAAO,CACL,IAAI,CAAC,IAAI,CACP,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CACxE,GAAG,CAAC,CACN,CAAC;AACJ,CAAC,CAAC;AAEF,YAAY;AACZ,MAAM,QAAQ,GAAG,CAAC,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE;IACtC,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1E,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IACpE,IAAI,KAAK,GAAG,QAAQ,GAAG,UAAU,CAAC;IAClC,gBAAgB;IAChB,IAAI,KAAK,GAAG,CAAC;QAAE,KAAK,IAAI,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;IACpC,OAAO,KAAK,CAAC;AACf,CAAC,CAAC;AAEF,eAAe;AACf,MAAM,CAAC,MAAM,KAAK,GAAqB,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;IAC1D,IAAI,CAAC,OAAO;QAAE,OAAO;IACrB,MAAM,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;IAC/B,IAAI,CAAC,CAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,SAAS,CAAA;QAAE,OAAO;IACnC,+BAA+B;IAC/B,MAAM,MAAM,GAAG,UAAU,CAAC,SAAS,EAAa,CAAC;IAEjD,OAAO,CAAC,MAAM,EAAE,GAAG,EAAE,UAAU,EAAE,EAAE;QACjC,MAAM,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC;QACpC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,GAAG,CAAC;QAE7B,MAAM,CAAC,GAAG,QAAQ,CAAC,aAAa,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;QAE1C,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QACxC,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC;QAEzC,YAAY;QACZ,wBAAwB;QACxB,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;QAEhE;;;;WAIG;QACH,MAAM,SAAS,GAAG,QAAQ,CAAC,aAAa,CAAC,MAAM,EAAE;YAC/C,KAAK,8CACH,CAAC,EAAE;oBACD,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;oBACnB,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;oBACxC;wBACE,GAAG;wBACH,IAAI,GAAG,IAAI,GAAG,CAAC;wBACf,IAAI,GAAG,IAAI,GAAG,CAAC;wBACf,CAAC;wBACD,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACd,CAAC;wBACD,GAAG,MAAM,CAAC,CAAC,CAAC;qBACb;oBACD,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;oBAC1D,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;oBACrD,CAAC,GAAG,CAAC;iBACN,IACE,UAAU,GACV,IAAI,CAAC,OAAO,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC,KAC5C,IAAI,EAAE,KAAK,IAAI,UAAU,CAAC,KAAK,GAChC;SACF,CAAC,CAAC;QAEH,CAAC,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;QAEzB,OAAO,CAAC,CAAC;IACX,CAAC,CAAC;AACJ,CAAC,CAAC"}