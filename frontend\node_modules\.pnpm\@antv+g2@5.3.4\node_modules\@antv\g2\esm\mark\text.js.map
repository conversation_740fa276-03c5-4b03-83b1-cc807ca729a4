{"version": 3, "file": "text.js", "sourceRoot": "", "sources": ["../../src/mark/text.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,UAAU,CAAC;AACzD,OAAO,EAAE,UAAU,EAAE,mBAAmB,EAAE,MAAM,cAAc,CAAC;AAC/D,OAAO,EACL,oBAAoB,EACpB,iBAAiB,EACjB,gBAAgB,EAChB,gBAAgB,EAChB,SAAS,EACT,UAAU,GACX,MAAM,SAAS,CAAC;AAEjB,MAAM,KAAK,GAAG;IACZ,IAAI,EAAE,SAAS;IACf,KAAK,EAAE,SAAS;IAChB,GAAG,EAAE,OAAO;CACb,CAAC;AAIF,MAAM,CAAC,MAAM,IAAI,GAAoB,CAAC,OAAO,EAAE,EAAE;IAC/C,MAAM,EAAE,SAAS,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;IACtC,IAAI,SAAS;QAAE,OAAO,UAAkB,CAAC;IACzC,OAAO,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,EAAE;QAC1C,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC;QAC7B,MAAM,MAAM,GAAG,gBAAgB,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QACvD,MAAM,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,EAAE;YAChC,MAAM,CAAC,GAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAc,CAAC;QACrD,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;IACpB,CAAC,CAAS,CAAC;AACb,CAAC,CAAC;AAEF,IAAI,CAAC,KAAK,GAAG;IACX,YAAY,EAAE,MAAM;IACpB,iBAAiB,EAAE,OAAO;IAC1B,SAAS,EAAE,KAAK;IAChB,KAAK;IACL,QAAQ,EAAE;QACR,GAAG,oBAAoB,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;QACvD,EAAE,IAAI,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE;QAC7B,EAAE,IAAI,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE;QAC7B,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE;QACnC,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,UAAU,EAAE;QACvC,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE;KACtC;IACD,YAAY,EAAE;QACZ,GAAG,gBAAgB,EAAE;QACrB,EAAE,IAAI,EAAE,UAAU,EAAE;QACpB,EAAE,IAAI,EAAE,mBAAmB,EAAE;KAC9B;IACD,aAAa,EAAE,CAAC,GAAG,iBAAiB,EAAE,EAAE,GAAG,SAAS,EAAE,CAAC;CACxD,CAAC"}