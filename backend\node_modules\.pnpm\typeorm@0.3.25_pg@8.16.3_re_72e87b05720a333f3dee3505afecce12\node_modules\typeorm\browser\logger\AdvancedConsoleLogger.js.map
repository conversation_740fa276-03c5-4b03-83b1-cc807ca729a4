{"version": 3, "sources": ["../browser/src/logger/AdvancedConsoleLogger.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,aAAa,EAAE,MAAM,2BAA2B,CAAA;AACzD,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAA;AAIjD;;;GAGG;AACH,MAAM,OAAO,qBAAsB,SAAQ,cAAc;IACrD;;OAEG;IACO,QAAQ,CACd,KAAe,EACf,UAAqC,EACrC,WAAyB;QAEzB,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAA;QAEpD,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC7B,QAAQ,OAAO,CAAC,IAAI,IAAI,KAAK,EAAE,CAAC;gBAC5B,KAAK,KAAK,CAAC;gBACX,KAAK,cAAc,CAAC;gBACpB,KAAK,WAAW;oBACZ,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAA;oBAC1C,MAAK;gBAET,KAAK,MAAM,CAAC;gBACZ,KAAK,OAAO;oBACR,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;wBACjB,aAAa,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,OAAO,CAAC,CAAA;oBAC1D,CAAC;yBAAM,CAAC;wBACJ,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAA;oBAC9C,CAAC;oBACD,MAAK;gBAET,KAAK,MAAM,CAAC;gBACZ,KAAK,YAAY;oBACb,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;wBACjB,aAAa,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,OAAO,CAAC,CAAA;oBAC1D,CAAC;yBAAM,CAAC;wBACJ,OAAO,CAAC,IAAI,CACR,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAC9C,CAAA;oBACL,CAAC;oBACD,MAAK;gBAET,KAAK,OAAO,CAAC;gBACb,KAAK,aAAa;oBACd,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;wBACjB,aAAa,CAAC,QAAQ,CAClB,OAAO,CAAC,MAAM,EACd,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAC1B,CAAA;oBACL,CAAC;yBAAM,CAAC;wBACJ,OAAO,CAAC,KAAK,CACT,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAC/C,CAAA;oBACL,CAAC;oBACD,MAAK;YACb,CAAC;QACL,CAAC;IACL,CAAC;CACJ", "file": "AdvancedConsoleLogger.js", "sourcesContent": ["import { PlatformTools } from \"../platform/PlatformTools\"\nimport { AbstractLogger } from \"./AbstractLogger\"\nimport { LogLevel, LogMessage } from \"./Logger\"\nimport { QueryRunner } from \"../query-runner/QueryRunner\"\n\n/**\n * Performs logging of the events in TypeORM.\n * This version of logger uses console to log events and use syntax highlighting.\n */\nexport class AdvancedConsoleLogger extends AbstractLogger {\n    /**\n     * Write log to specific output.\n     */\n    protected writeLog(\n        level: LogLevel,\n        logMessage: LogMessage | LogMessage[],\n        queryRunner?: QueryRunner,\n    ) {\n        const messages = this.prepareLogMessages(logMessage)\n\n        for (const message of messages) {\n            switch (message.type ?? level) {\n                case \"log\":\n                case \"schema-build\":\n                case \"migration\":\n                    PlatformTools.log(String(message.message))\n                    break\n\n                case \"info\":\n                case \"query\":\n                    if (message.prefix) {\n                        PlatformTools.logInfo(message.prefix, message.message)\n                    } else {\n                        PlatformTools.log(String(message.message))\n                    }\n                    break\n\n                case \"warn\":\n                case \"query-slow\":\n                    if (message.prefix) {\n                        PlatformTools.logWarn(message.prefix, message.message)\n                    } else {\n                        console.warn(\n                            PlatformTools.warn(String(message.message)),\n                        )\n                    }\n                    break\n\n                case \"error\":\n                case \"query-error\":\n                    if (message.prefix) {\n                        PlatformTools.logError(\n                            message.prefix,\n                            String(message.message),\n                        )\n                    } else {\n                        console.error(\n                            PlatformTools.error(String(message.message)),\n                        )\n                    }\n                    break\n            }\n        }\n    }\n}\n"], "sourceRoot": ".."}