import { CoordinateComponent as CC } from '../runtime';
import { RadialCoordinate } from '../spec';
export type RadialOptions = Omit<RadialCoordinate, 'type'>;
export declare const getRadialOptions: (options?: RadialOptions) => {
    transform?: import("../spec").CoordinateTransform[];
    startAngle: number;
    endAngle: number;
    innerRadius: number;
    outerRadius: number;
};
/**
 * Radial
 */
export declare const Radial: CC<RadialOptions>;
