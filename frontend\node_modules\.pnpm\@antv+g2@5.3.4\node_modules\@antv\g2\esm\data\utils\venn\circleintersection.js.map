{"version": 3, "file": "circleintersection.js", "sourceRoot": "", "sources": ["../../../../src/data/utils/venn/circleintersection.ts"], "names": [], "mappings": "AAAA,MAAM,KAAK,GAAG,KAAK,CAAC;AAEpB;;;GAGG;AACH,MAAM,UAAU,gBAAgB,CAAC,OAAO,EAAE,KAAW;IACnD,iDAAiD;IACjD,MAAM,kBAAkB,GAAG,qBAAqB,CAAC,OAAO,CAAC,CAAC;IAE1D,4DAA4D;IAC5D,MAAM,WAAW,GAAG,kBAAkB,CAAC,MAAM,CAAC,UAAU,CAAC;QACvD,OAAO,kBAAkB,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;IACxC,CAAC,CAAC,CAAC;IAEH,IAAI,OAAO,GAAG,CAAC,EACb,WAAW,GAAG,CAAC,EACf,CAAC,CAAC;IACJ,MAAM,IAAI,GAAG,EAAE,CAAC;IAChB,kEAAkE;IAClE,6CAA6C;IAC7C,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;QAC1B,sEAAsE;QACtE,+CAA+C;QAC/C,MAAM,MAAM,GAAG,SAAS,CAAC,WAAW,CAAC,CAAC;QACtC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YACvC,MAAM,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;YACzB,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;SACtD;QACD,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;YAC7B,OAAO,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;QAC3B,CAAC,CAAC,CAAC;QAEH,sDAAsD;QACtD,uBAAuB;QACvB,IAAI,EAAE,GAAG,WAAW,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC7C,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YACvC,MAAM,EAAE,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;YAE1B,kCAAkC;YAClC,WAAW,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YAE7C,kDAAkD;YAClD,MAAM,QAAQ,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC;YAChE,IAAI,GAAG,GAAG,IAAI,CAAC;YAEf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;gBAC9C,IAAI,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE;oBAClD,sDAAsD;oBACtD,wBAAwB;oBACxB,MAAM,MAAM,GAAG,OAAO,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EACvC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,EACjD,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;oBAEpD,IAAI,SAAS,GAAG,EAAE,GAAG,EAAE,CAAC;oBACxB,IAAI,SAAS,GAAG,CAAC,EAAE;wBACjB,SAAS,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;qBAC1B;oBAED,oDAAoD;oBACpD,MAAM;oBACN,MAAM,CAAC,GAAG,EAAE,GAAG,SAAS,GAAG,CAAC,CAAC;oBAC7B,IAAI,KAAK,GAAG,QAAQ,CAAC,QAAQ,EAAE;wBAC7B,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;wBACzC,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;qBAC1C,CAAC,CAAC;oBAEH,oDAAoD;oBACpD,sDAAsD;oBACtD,IAAI,KAAK,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;wBAC7B,KAAK,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;qBAC3B;oBAED,mDAAmD;oBACnD,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,CAAC,KAAK,GAAG,KAAK,EAAE;wBACrC,GAAG,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;qBACxD;iBACF;aACF;YAED,IAAI,GAAG,KAAK,IAAI,EAAE;gBAChB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBACf,OAAO,IAAI,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;gBACpD,EAAE,GAAG,EAAE,CAAC;aACT;SACF;KACF;SAAM;QACL,gEAAgE;QAChE,gEAAgE;QAChE,IAAI,QAAQ,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;QAC1B,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YACnC,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,EAAE;gBACvC,QAAQ,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;aACvB;SACF;QAED,+DAA+D;QAC/D,oBAAoB;QACpB,IAAI,QAAQ,GAAG,KAAK,CAAC;QACrB,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YACnC,IACE,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC;gBAC9B,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,EAC7C;gBACA,QAAQ,GAAG,IAAI,CAAC;gBAChB,MAAM;aACP;SACF;QAED,IAAI,QAAQ,EAAE;YACZ,OAAO,GAAG,WAAW,GAAG,CAAC,CAAC;SAC3B;aAAM;YACL,OAAO,GAAG,QAAQ,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,EAAE,CAAC;YACtD,IAAI,CAAC,IAAI,CAAC;gBACR,MAAM,EAAE,QAAQ;gBAChB,EAAE,EAAE,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE;gBACtD,EAAE,EAAE,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE;gBAC9D,KAAK,EAAE,QAAQ,CAAC,MAAM,GAAG,CAAC;aAC3B,CAAC,CAAC;SACJ;KACF;IAED,WAAW,IAAI,CAAC,CAAC;IACjB,IAAI,KAAK,EAAE;QACT,KAAK,CAAC,IAAI,GAAG,OAAO,GAAG,WAAW,CAAC;QACnC,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;QACxB,KAAK,CAAC,WAAW,GAAG,WAAW,CAAC;QAChC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;QAClB,KAAK,CAAC,WAAW,GAAG,WAAW,CAAC;QAChC,KAAK,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;KAC/C;IAED,OAAO,OAAO,GAAG,WAAW,CAAC;AAC/B,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,kBAAkB,CAAC,KAAK,EAAE,OAAO;IAC/C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;QACvC,IAAI,QAAQ,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,EAAE;YAC3D,OAAO,KAAK,CAAC;SACd;KACF;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,8DAA8D;AAC9D,SAAS,qBAAqB,CAAC,OAAO;IACpC,MAAM,GAAG,GAAG,EAAE,CAAC;IACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;QACvC,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YAC3C,MAAM,SAAS,GAAG,wBAAwB,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;YACnE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;gBACzC,MAAM,CAAC,GAAQ,SAAS,CAAC,CAAC,CAAC,CAAC;gBAC5B,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACvB,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;aACb;SACF;KACF;IACD,OAAO,GAAG,CAAC;AACb,CAAC;AAED,+FAA+F;AAC/F,MAAM,UAAU,UAAU,CAAC,CAAC,EAAE,KAAK;IACjC,OAAO,CACL,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC;QAChC,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,CACjD,CAAC;AACJ,CAAC;AAED,4CAA4C;AAC5C,MAAM,UAAU,QAAQ,CAAC,EAAE,EAAE,EAAE;IAC7B,OAAO,IAAI,CAAC,IAAI,CACd,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAC9D,CAAC;AACJ,CAAC;AAED;;2CAE2C;AAC3C,MAAM,UAAU,aAAa,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;IACrC,aAAa;IACb,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,EAAE;QAChB,OAAO,CAAC,CAAC;KACV;IAED,wBAAwB;IACxB,IAAI,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE;QAC1B,OAAO,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;KACtD;IAED,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EACnD,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAClD,OAAO,UAAU,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AACjD,CAAC;AAED;;;+EAG+E;AAC/E,MAAM,UAAU,wBAAwB,CAAC,EAAE,EAAE,EAAE;IAC7C,MAAM,CAAC,GAAG,QAAQ,CAAC,EAAE,EAAE,EAAE,CAAC,EACxB,EAAE,GAAG,EAAE,CAAC,MAAM,EACd,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC;IAEjB,oDAAoD;IACpD,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE;QAC1C,OAAO,EAAE,CAAC;KACX;IAED,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAC7C,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,EAC9B,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EACnC,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EACnC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAC7B,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAEhC,OAAO;QACL,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE;QAC1B,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE;KAC3B,CAAC;AACJ,CAAC;AAED,8CAA8C;AAC9C,MAAM,UAAU,SAAS,CAAC,MAAM;IAC9B,MAAM,MAAM,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAC9B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;QACtC,MAAM,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,MAAM,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACzB;IACD,MAAM,CAAC,CAAC,IAAI,MAAM,CAAC,MAAM,CAAC;IAC1B,MAAM,CAAC,CAAC,IAAI,MAAM,CAAC,MAAM,CAAC;IAC1B,OAAO,MAAM,CAAC;AAChB,CAAC"}