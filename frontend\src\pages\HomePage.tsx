import React from 'react';
import { Card, Row, Col, Typography, Button, Space } from 'antd';
import { SearchOutlined, GlobalOutlined, SoundOutlined } from '@ant-design/icons';
import { Link } from 'react-router-dom';

const { Title, Paragraph } = Typography;

const HomePage: React.FC = () => {
  return (
    <div style={{ padding: '24px' }}>
      {/* 英雄区域 */}
      <div style={{ 
        textAlign: 'center', 
        padding: '80px 0',
        background: 'linear-gradient(135deg, #1890ff 0%, #096dd9 100%)',
        color: 'white',
        borderRadius: '8px',
        marginBottom: '48px'
      }}>
        <Title level={1} style={{ color: 'white', fontSize: '3rem', marginBottom: '16px' }}>
          海洋生物声音平台
        </Title>
        <Paragraph style={{ 
          color: 'white', 
          fontSize: '1.2rem', 
          maxWidth: '600px', 
          margin: '0 auto 32px' 
        }}>
          探索海洋生物的神秘声音世界，通过科学数据了解海洋生命的多样性
        </Paragraph>
        <Space size="large">
          <Link to="/species">
            <Button type="primary" size="large" icon={<SearchOutlined />}>
              探索物种
            </Button>
          </Link>
          <Link to="/map">
            <Button size="large" icon={<GlobalOutlined />} style={{ 
              background: 'rgba(255,255,255,0.2)', 
              borderColor: 'white',
              color: 'white'
            }}>
              查看地图
            </Button>
          </Link>
        </Space>
      </div>

      {/* 功能介绍 */}
      <Row gutter={[24, 24]} style={{ marginBottom: '48px' }}>
        <Col xs={24} md={8}>
          <Card 
            hoverable
            style={{ height: '100%', textAlign: 'center' }}
            cover={
              <div style={{ 
                padding: '40px', 
                background: '#f0f9ff',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center'
              }}>
                <SearchOutlined style={{ fontSize: '48px', color: '#1890ff' }} />
              </div>
            }
          >
            <Card.Meta
              title="物种搜索"
              description="通过中文名、英文名或拉丁学名搜索海洋生物，了解它们的分类信息和保护状态"
            />
          </Card>
        </Col>
        <Col xs={24} md={8}>
          <Card 
            hoverable
            style={{ height: '100%', textAlign: 'center' }}
            cover={
              <div style={{ 
                padding: '40px', 
                background: '#f6ffed',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center'
              }}>
                <GlobalOutlined style={{ fontSize: '48px', color: '#52c41a' }} />
              </div>
            }
          >
            <Card.Meta
              title="交互式地图"
              description="在全球地图上查看物种分布范围和声音数据采集点，直观了解海洋生物的栖息地"
            />
          </Card>
        </Col>
        <Col xs={24} md={8}>
          <Card 
            hoverable
            style={{ height: '100%', textAlign: 'center' }}
            cover={
              <div style={{ 
                padding: '40px', 
                background: '#fff7e6',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center'
              }}>
                <SoundOutlined style={{ fontSize: '48px', color: '#fa8c16' }} />
              </div>
            }
          >
            <Card.Meta
              title="声音数据"
              description="聆听真实的海洋生物发声，查看音频的时域图和时频图分析"
            />
          </Card>
        </Col>
      </Row>

      {/* 统计信息 */}
      <Card style={{ marginBottom: '48px' }}>
        <Title level={3} style={{ textAlign: 'center', marginBottom: '32px' }}>
          平台数据统计
        </Title>
        <Row gutter={[24, 24]} style={{ textAlign: 'center' }}>
          <Col xs={12} md={6}>
            <div>
              <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#1890ff' }}>
                1,200+
              </div>
              <div style={{ color: '#666' }}>物种数量</div>
            </div>
          </Col>
          <Col xs={12} md={6}>
            <div>
              <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#52c41a' }}>
                5,800+
              </div>
              <div style={{ color: '#666' }}>音频文件</div>
            </div>
          </Col>
          <Col xs={12} md={6}>
            <div>
              <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#fa8c16' }}>
                3,400+
              </div>
              <div style={{ color: '#666' }}>采集点</div>
            </div>
          </Col>
          <Col xs={12} md={6}>
            <div>
              <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#722ed1' }}>
                15TB+
              </div>
              <div style={{ color: '#666' }}>数据存储</div>
            </div>
          </Col>
        </Row>
      </Card>

      {/* 最新物种展示 */}
      <Card>
        <div style={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          marginBottom: '24px'
        }}>
          <Title level={3} style={{ margin: 0 }}>
            最新添加的物种
          </Title>
          <Link to="/species">
            <Button type="link">查看更多 →</Button>
          </Link>
        </div>
        <Row gutter={[16, 16]}>
          {[1, 2, 3, 4].map(item => (
            <Col xs={24} sm={12} md={6} key={item}>
              <Card 
                hoverable
                size="small"
                cover={
                  <div style={{ 
                    height: '120px', 
                    background: '#f5f5f5',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: '#999'
                  }}>
                    暂无图片
                  </div>
                }
              >
                <Card.Meta
                  title="示例物种"
                  description="Exemplum species"
                />
              </Card>
            </Col>
          ))}
        </Row>
      </Card>
    </div>
  );
};

export default HomePage;
