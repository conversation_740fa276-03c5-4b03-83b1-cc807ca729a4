{"version": 3, "file": "mark.js", "sourceRoot": "", "sources": ["../../src/api/mark.ts"], "names": [], "mappings": ";;;;;;AAGA,OAAO,EAAE,IAAI,EAAE,MAAM,QAAQ,CAAC;AAC9B,OAAO,EAAE,WAAW,EAAE,MAAM,UAAU,CAAC;AACvC,OAAO,EAAE,SAAS,EAAE,MAAM,SAAS,CAAC;AAM7B,IAAM,QAAQ,GAAd,MAAM,QAAS,SAAQ,IAAmC;IAC/D,UAAU,CAAC,IAAS;QAClB,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC7B,IAAI,CAAC,KAAK;YAAE,OAAO;QACnB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QACxB,OAAO,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,MAAM,EAAE,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,OAAO;;QACL,MAAM,SAAS,GAAG,MAAA,IAAI,CAAC,OAAO,EAAE,0CAAE,OAAO,EAAE,CAAC;QAC5C,IAAI,CAAC,SAAS;YAAE,OAAO,SAAS,CAAC;QACjC,MAAM,EAAE,SAAS,EAAE,GAAG,SAAS,CAAC;QAChC,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAC/C,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CACxC,CAAC;QACF,OAAO,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,QAAQ;;QACN,MAAM,SAAS,GAAG,MAAA,IAAI,CAAC,OAAO,EAAE,0CAAE,OAAO,EAAE,CAAC;QAC5C,IAAI,CAAC,SAAS;YAAE,OAAO,SAAS,CAAC;QACjC,OAAO,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,KAAK,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,OAAe;;QAC/B,MAAM,SAAS,GAAG,MAAA,IAAI,CAAC,OAAO,EAAE,0CAAE,OAAO,EAAE,CAAC;QAC5C,IAAI,CAAC,SAAS;YAAE,OAAO,SAAS,CAAC;QACjC,OAAO,MAAA,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,KAAK,0CAAG,OAAO,CAAC,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7B,IAAI,CAAC,GAAG;YAAE,OAAO,SAAS,CAAC;QAC3B,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC7B,MAAM,UAAU,GAAG,KAAK,CAAC,UAAU,EAAE,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;QACvD,OAAO,UAAU,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;IACxC,CAAC;CACF,CAAA;AAjDY,QAAQ;IADpB,WAAW,CAAC,SAAS,CAAC;GACV,QAAQ,CAiDpB;SAjDY,QAAQ"}