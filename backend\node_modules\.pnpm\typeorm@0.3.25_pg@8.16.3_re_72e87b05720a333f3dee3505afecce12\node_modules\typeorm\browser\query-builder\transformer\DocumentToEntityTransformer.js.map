{"version": 3, "sources": ["../browser/src/query-builder/transformer/DocumentToEntityTransformer.ts"], "names": [], "mappings": "AAIA;;;GAGG;AACH,MAAM,OAAO,2BAA2B;IACpC,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E;IACI,kCAAkC;IAClC,uCAAuC;IACvC,wDAAwD;IAChD,yBAAkC,KAAK;QAAvC,2BAAsB,GAAtB,sBAAsB,CAAiB;IAChD,CAAC;IAEJ,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E,YAAY,CAAC,SAA0B,EAAE,QAAwB;QAC7D,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAA;IAC1E,CAAC;IAED,SAAS,CAAC,QAAa,EAAE,QAAwB;QAC7C,MAAM,MAAM,GAAQ,QAAQ,CAAC,MAAM,CAAC,SAAS,EAAE;YAC3C,gBAAgB,EAAE,IAAI;SACzB,CAAC,CAAA;QACF,IAAI,OAAO,GAAG,KAAK,CAAA;QAEnB,sCAAsC;QACtC,IAAI,QAAQ,CAAC,cAAc,EAAE,CAAC;YAC1B,0CAA0C;YAC1C,sKAAsK;YACtK,qJAAqJ;YACrJ,MAAM,EAAE,2BAA2B,EAAE,YAAY,EAAE,GAC/C,QAAQ,CAAC,cAAc,CAAA;YAE3B,MAAM,yBAAyB,GAC3B,QAAQ,CAAC,2BAA2B,CAAC,CAAA;YACzC,MAAM,0BAA0B,GAAG,QAAQ,CAAC,YAAY,CAAC,CAAA;YAEzD,IAAI,yBAAyB,EAAE,CAAC;gBAC5B,MAAM,CAAC,YAAY,CAAC,GAAG,yBAAyB,CAAA;gBAChD,OAAO,GAAG,IAAI,CAAA;YAClB,CAAC;iBAAM,IAAI,0BAA0B,EAAE,CAAC;gBACpC,MAAM,CAAC,YAAY,CAAC,GAAG,0BAA0B,CAAA;gBACjD,OAAO,GAAG,IAAI,CAAA;YAClB,CAAC;QACL,CAAC;QAED,iDAAiD;QACjD,IAAI,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC9B,QAAQ,CAAC,OAAO;iBACX,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,gBAAgB,CAAC;iBAC7C,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;gBAChB,MAAM,aAAa,GACf,QAAQ,CAAC,MAAM,CAAC,2BAA2B,CAAC,CAAA;gBAChD,IACI,aAAa,KAAK,SAAS;oBAC3B,aAAa,KAAK,IAAI;oBACtB,MAAM,CAAC,YAAY,EACrB,CAAC;oBACC,0CAA0C;oBAC1C,yEAAyE;oBACzE,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,aAAa,CAAA;oBAC3C,OAAO,GAAG,IAAI,CAAA;gBAClB,CAAC;YACL,CAAC,CAAC,CAAA;QACV,CAAC;QAED;;;;;;;;;;;iBAWS;QAET,6DAA6D;QAC7D,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YACnC,MAAM,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAC,2BAA2B,CAAC,CAAA;YAClE,IACI,aAAa,KAAK,SAAS;gBAC3B,MAAM,CAAC,YAAY;gBACnB,CAAC,MAAM,CAAC,SAAS,EACnB,CAAC;gBACC,yEAAyE;gBAEzE,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,aAAa,CAAA;gBAC3C,OAAO,GAAG,IAAI,CAAA;YAClB,CAAC;QACL,CAAC,CAAC,CAAA;QAEF,MAAM,4BAA4B,GAAG,CACjC,MAAW,EACX,QAAa,EACb,SAA6B,EAC/B,EAAE;YACA,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;gBAC3B,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC;oBAAE,OAAM;gBAEtC,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;oBACnB,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,GACzB,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAC3B,CAAC,GAAG,CAAC,CAAC,QAAa,EAAE,KAAa,EAAE,EAAE;wBACnC,MAAM,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC;4BAC5B,gBAAgB,EAAE,IAAI;yBACzB,CAAC,CAAA;wBACF,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;4BAChC,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC;gCACxB,QAAQ,CAAC,MAAM,CAAC,2BAA2B,CAAC,CAAA;wBACpD,CAAC,CAAC,CAAA;wBACF,4BAA4B,CACxB,OAAO,EACP,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,EAChC,QAAQ,CAAC,SAAS,CACrB,CAAA;wBACD,OAAO,OAAO,CAAA;oBAClB,CAAC,CAAC,CAAA;gBACN,CAAC;qBAAM,CAAC;oBACJ,IACI,QAAQ,CAAC,SAAS,CAAC,MAAM;wBACzB,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC;wBAE9B,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC;4BAC5C,gBAAgB,EAAE,IAAI;yBACzB,CAAC,CAAA;oBAEN,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;wBAChC,MAAM,KAAK,GACP,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CACrB,MAAM,CAAC,2BAA2B,CACrC,CAAA;wBACL,IAAI,KAAK,KAAK,SAAS;4BAAE,OAAM;wBAE/B,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC;4BAC9B,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC;gCAC5C,gBAAgB,EAAE,IAAI;6BACzB,CAAC,CAAA;wBAEN,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC;4BAC9C,KAAK,CAAA;oBACb,CAAC,CAAC,CAAA;oBAEF,4BAA4B,CACxB,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,EAC7B,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,EACzB,QAAQ,CAAC,SAAS,CACrB,CAAA;gBACL,CAAC;YACL,CAAC,CAAC,CAAA;QACN,CAAC,CAAA;QAED,4BAA4B,CAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAA;QAElE,iFAAiF;QACjF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;aAgEK;QAEL,OAAO,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAA;IAClC,CAAC;CACJ", "file": "DocumentToEntityTransformer.js", "sourcesContent": ["import { EntityMetadata } from \"../../metadata/EntityMetadata\"\nimport { ObjectLiteral } from \"../../common/ObjectLiteral\"\nimport { EmbeddedMetadata } from \"../../metadata/EmbeddedMetadata\"\n\n/**\n * Transforms raw document into entity object.\n * Entity is constructed based on its entity metadata.\n */\nexport class DocumentToEntityTransformer {\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(\n        // private selectionMap: AliasMap,\n        // private joinMappings: JoinMapping[],\n        // private relationCountMetas: RelationCountAttribute[],\n        private enableRelationIdValues: boolean = false,\n    ) {}\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    transformAll(documents: ObjectLiteral[], metadata: EntityMetadata) {\n        return documents.map((document) => this.transform(document, metadata))\n    }\n\n    transform(document: any, metadata: EntityMetadata) {\n        const entity: any = metadata.create(undefined, {\n            fromDeserializer: true,\n        })\n        let hasData = false\n\n        // handle _id property the special way\n        if (metadata.objectIdColumn) {\n            // todo: we can't use driver in this class\n            // do we really need prepare hydrated value here? If no then no problem. If yes then think maybe prepareHydratedValue process should be extracted out of driver class?\n            // entity[metadata.ObjectIdColumn.propertyName] = this.driver.prepareHydratedValue(document[metadata.ObjectIdColumn.name\"], metadata.ObjectIdColumn);\n            const { databaseNameWithoutPrefixes, propertyName } =\n                metadata.objectIdColumn\n\n            const documentIdWithoutPrefixes =\n                document[databaseNameWithoutPrefixes]\n            const documentIdWithPropertyName = document[propertyName]\n\n            if (documentIdWithoutPrefixes) {\n                entity[propertyName] = documentIdWithoutPrefixes\n                hasData = true\n            } else if (documentIdWithPropertyName) {\n                entity[propertyName] = documentIdWithPropertyName\n                hasData = true\n            }\n        }\n\n        // add special columns that contains relation ids\n        if (this.enableRelationIdValues) {\n            metadata.columns\n                .filter((column) => !!column.relationMetadata)\n                .forEach((column) => {\n                    const valueInObject =\n                        document[column.databaseNameWithoutPrefixes]\n                    if (\n                        valueInObject !== undefined &&\n                        valueInObject !== null &&\n                        column.propertyName\n                    ) {\n                        // todo: we can't use driver in this class\n                        // const value = this.driver.prepareHydratedValue(valueInObject, column);\n                        entity[column.propertyName] = valueInObject\n                        hasData = true\n                    }\n                })\n        }\n\n        /*this.joinMappings\n            .filter(joinMapping => joinMapping.parentName === alias.name && !joinMapping.alias.relationOwnerSelection && joinMapping.alias.target)\n            .map(joinMapping => {\n                const relatedEntities = this.transformRawResultsGroup(rawSqlResults, joinMapping.alias);\n                const isResultArray = joinMapping.isMany;\n                const result = !isResultArray ? relatedEntities[0] : relatedEntities;\n\n                if (result && (!isResultArray || result.length > 0)) {\n                    entity[joinMapping.propertyName] = result;\n                    hasData = true;\n                }\n            });*/\n\n        // get value from columns selections and put them into object\n        metadata.ownColumns.forEach((column) => {\n            const valueInObject = document[column.databaseNameWithoutPrefixes]\n            if (\n                valueInObject !== undefined &&\n                column.propertyName &&\n                !column.isVirtual\n            ) {\n                // const value = this.driver.prepareHydratedValue(valueInObject, column);\n\n                entity[column.propertyName] = valueInObject\n                hasData = true\n            }\n        })\n\n        const addEmbeddedValuesRecursively = (\n            entity: any,\n            document: any,\n            embeddeds: EmbeddedMetadata[],\n        ) => {\n            embeddeds.forEach((embedded) => {\n                if (!document[embedded.prefix]) return\n\n                if (embedded.isArray) {\n                    entity[embedded.propertyName] = (\n                        document[embedded.prefix] as any[]\n                    ).map((subValue: any, index: number) => {\n                        const newItem = embedded.create({\n                            fromDeserializer: true,\n                        })\n                        embedded.columns.forEach((column) => {\n                            newItem[column.propertyName] =\n                                subValue[column.databaseNameWithoutPrefixes]\n                        })\n                        addEmbeddedValuesRecursively(\n                            newItem,\n                            document[embedded.prefix][index],\n                            embedded.embeddeds,\n                        )\n                        return newItem\n                    })\n                } else {\n                    if (\n                        embedded.embeddeds.length &&\n                        !entity[embedded.propertyName]\n                    )\n                        entity[embedded.propertyName] = embedded.create({\n                            fromDeserializer: true,\n                        })\n\n                    embedded.columns.forEach((column) => {\n                        const value =\n                            document[embedded.prefix][\n                                column.databaseNameWithoutPrefixes\n                            ]\n                        if (value === undefined) return\n\n                        if (!entity[embedded.propertyName])\n                            entity[embedded.propertyName] = embedded.create({\n                                fromDeserializer: true,\n                            })\n\n                        entity[embedded.propertyName][column.propertyName] =\n                            value\n                    })\n\n                    addEmbeddedValuesRecursively(\n                        entity[embedded.propertyName],\n                        document[embedded.prefix],\n                        embedded.embeddeds,\n                    )\n                }\n            })\n        }\n\n        addEmbeddedValuesRecursively(entity, document, metadata.embeddeds)\n\n        // if relation is loaded then go into it recursively and transform its values too\n        /*metadata.relations.forEach(relation => {\n            const relationAlias = this.selectionMap.findSelectionByParent(alias.name, relation.propertyName);\n            if (relationAlias) {\n                const joinMapping = this.joinMappings.find(joinMapping => joinMapping.type === \"join\" && joinMapping.alias === relationAlias);\n                const relatedEntities = this.transformRawResultsGroup(rawSqlResults, relationAlias);\n                const isResultArray = relation.isManyToMany || relation.isOneToMany;\n                const result = !isResultArray ? relatedEntities[0] : relatedEntities;\n\n                if (result) {\n                    let propertyName = relation.propertyName;\n                    if (joinMapping) {\n                        propertyName = joinMapping.propertyName;\n                    }\n\n                    if (relation.isLazy) {\n                        entity[\"__\" + propertyName + \"__\"] = result;\n                    } else {\n                        entity[propertyName] = result;\n                    }\n\n                    if (!isResultArray || result.length > 0)\n                        hasData = true;\n                }\n            }\n\n            // if relation has id field then relation id/ids to that field.\n            if (relation.isManyToMany) {\n                if (relationAlias) {\n                    const ids: any[] = [];\n                    const joinMapping = this.joinMappings.find(joinMapping => joinMapping.type === \"relationId\" && joinMapping.alias === relationAlias);\n\n                    if (relation.idField || joinMapping) {\n                        const propertyName = joinMapping ? joinMapping.propertyName : relation.idField as string;\n                        const junctionMetadata = relation.junctionEntityMetadata;\n                        const columnName = relation.isOwning ? junctionMetadata.columns[1].name : junctionMetadata.columns[0].name;\n\n                        rawSqlResults.forEach(results => {\n                            if (relationAlias) {\n                                const resultsKey = relationAlias.name + \"_\" + columnName;\n                                const value = this.driver.prepareHydratedValue(results[resultsKey], relation.referencedColumn);\n                                if (value !== undefined && value !== null)\n                                    ids.push(value);\n                            }\n                        });\n\n                        if (ids && ids.length)\n                            entity[propertyName] = ids;\n                    }\n                }\n            } else if (relation.idField) {\n                const relationName = relation.name;\n                entity[relation.idField] = this.driver.prepareHydratedValue(rawSqlResults[0][alias.name + \"_\" + relationName], relation.referencedColumn);\n            }\n\n            // if relation counter\n            this.relationCountMetas.forEach(joinMeta => {\n                if (joinMeta.alias === relationAlias) {\n                    // console.log(\"relation count was found for relation: \", relation);\n                    // joinMeta.entity = entity;\n                    joinMeta.entities.push({ entity: entity, metadata: metadata });\n                    // console.log(joinMeta);\n                    // console.log(\"---------------------\");\n                }\n            });\n        });*/\n\n        return hasData ? entity : null\n    }\n}\n"], "sourceRoot": "../.."}