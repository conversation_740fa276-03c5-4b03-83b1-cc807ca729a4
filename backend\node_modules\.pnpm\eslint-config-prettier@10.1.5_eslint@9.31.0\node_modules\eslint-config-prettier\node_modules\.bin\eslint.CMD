@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\workspaces\dd\mbdp\backend\node_modules\.pnpm\eslint@9.31.0\node_modules\eslint\bin\node_modules;C:\workspaces\dd\mbdp\backend\node_modules\.pnpm\eslint@9.31.0\node_modules\eslint\node_modules;C:\workspaces\dd\mbdp\backend\node_modules\.pnpm\eslint@9.31.0\node_modules;C:\workspaces\dd\mbdp\backend\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\workspaces\dd\mbdp\backend\node_modules\.pnpm\eslint@9.31.0\node_modules\eslint\bin\node_modules;C:\workspaces\dd\mbdp\backend\node_modules\.pnpm\eslint@9.31.0\node_modules\eslint\node_modules;C:\workspaces\dd\mbdp\backend\node_modules\.pnpm\eslint@9.31.0\node_modules;C:\workspaces\dd\mbdp\backend\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\..\..\..\eslint@9.31.0\node_modules\eslint\bin\eslint.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\..\..\..\eslint@9.31.0\node_modules\eslint\bin\eslint.js" %*
)
