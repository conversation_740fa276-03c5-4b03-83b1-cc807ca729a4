// 导出所有图表组件
export { default as BaseChart } from './BaseChart';
export { default as SpeciesStatsChart } from './SpeciesStatsChart';
export { default as DataExport } from './DataExport';

// 导出类型定义
export type { default as BaseChartProps } from './BaseChart';

// 图表工具函数
export const chartUtils = {
  // 格式化数字
  formatNumber: (num: number): string => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  },

  // 生成颜色数组
  generateColors: (count: number): string[] => {
    const baseColors = [
      '#1890ff', '#52c41a', '#fa8c16', '#f5222d', 
      '#722ed1', '#13c2c2', '#eb2f96', '#faad14'
    ];
    
    const colors = [];
    for (let i = 0; i < count; i++) {
      colors.push(baseColors[i % baseColors.length]);
    }
    return colors;
  },

  // 导出图表为图片
  exportChartAsImage: (chartInstance: any, filename: string = 'chart') => {
    if (chartInstance) {
      const url = chartInstance.getDataURL({
        type: 'png',
        pixelRatio: 2,
        backgroundColor: '#fff'
      });
      
      const link = document.createElement('a');
      link.href = url;
      link.download = `${filename}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  },
};
