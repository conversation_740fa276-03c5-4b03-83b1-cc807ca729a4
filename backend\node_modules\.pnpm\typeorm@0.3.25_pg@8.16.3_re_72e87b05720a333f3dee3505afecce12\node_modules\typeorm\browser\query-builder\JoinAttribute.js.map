{"version": 3, "sources": ["../browser/src/query-builder/JoinAttribute.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAA;AAGvD,OAAO,EAAE,WAAW,EAAE,MAAM,qBAAqB,CAAA;AACjD,OAAO,EAAE,YAAY,EAAE,MAAM,UAAU,CAAA;AACvC,OAAO,EAAE,WAAW,EAAE,MAAM,uBAAuB,CAAA;AAEnD;;GAEG;AACH,MAAM,OAAO,aAAa;IAwCtB,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YACY,UAAsB,EACtB,kBAAsC,EAC9C,aAA6B;QAFrB,eAAU,GAAV,UAAU,CAAY;QACtB,uBAAkB,GAAlB,kBAAkB,CAAoB;QAsBlD,wBAAmB,GAAY,KAAK,CAAA;QAuEpC,sBAAiB,GAAY,KAAK,CAAA;QA1F9B,IAAI,aAAa,EAAE,CAAC;YAChB,WAAW,CAAC,MAAM,CAAC,IAAI,EAAE,aAAa,CAAC,CAAA;QAC3C,CAAC;IACL,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E,IAAI,MAAM;QACN,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS;YAAE,OAAO,IAAI,CAAC,aAAa,CAAA;QAE/D,IAAI,IAAI,CAAC,QAAQ;YACb,OAAO,IAAI,CAAC,QAAQ,CAAC,YAAY,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAA;QAElE,OAAO,KAAK,CAAA;IAChB,CAAC;IAID;;OAEG;IACH,IAAI,UAAU;QACV,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC5B,MAAM,QAAQ,GAAG,GAAG,EAAE;gBAClB,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;oBACnD,IAAI,MAAM,CAAC,SAAS,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI;wBAAE,OAAO,IAAI,CAAA;oBAErD,IACI,IAAI,CAAC,QAAQ;wBACb,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CACxB,CAAC,MAAM,EAAE,EAAE,CACP,MAAM,CAAC,SAAS;4BAChB,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,GAAG,GAAG,MAAM,CAAC,YAAY,CAClD;wBAED,OAAO,IAAI,CAAA;gBACnB,CAAC;gBAED,OAAO,KAAK,CAAA;YAChB,CAAC,CAAA;YACD,IAAI,CAAC,eAAe,GAAG,QAAQ,EAAE,CAAA;YACjC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAA;QACnC,CAAC;QACD,OAAO,IAAI,CAAC,eAAe,CAAA;IAC/B,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACT,OAAO,IAAI,CAAC,QAAQ;YAChB,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS;YACzB,CAAC,CAAE,IAAI,CAAC,gBAA2B,CAAA;IAC3C,CAAC;IAED;;;;;OAKG;IACH,IAAI,WAAW;QACX,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,IAAI,CAAC,gBAAgB,CAAC;YACzD,OAAO,SAAS,CAAA;QAEpB,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAC/B,CAAC,EACD,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,GAAG,CAAC,CACrC,CAAA;IACL,CAAC;IAED;;;;;;OAMG;IACH,IAAI,oBAAoB;QACpB,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,IAAI,CAAC,gBAAgB,CAAC;YACzD,OAAO,SAAS,CAAA;QAEpB,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAC/B,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CACzC,CAAA;IACL,CAAC;IAID;;;;;OAKG;IACH,IAAI,QAAQ;QACR,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC1B,MAAM,QAAQ,GAAG,GAAG,EAAE;gBAClB,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,IAAI,CAAC,gBAAgB,CAAC;oBACzD,OAAO,SAAS,CAAA;gBAEpB,MAAM,sBAAsB,GACxB,IAAI,CAAC,kBAAkB,CAAC,eAAe,CAAC,IAAI,CAAC,WAAY,CAAC,CAAA;gBAC9D,IAAI,QAAQ,GACR,sBAAsB,CAAC,QAAQ,CAAC,4BAA4B,CACxD,IAAI,CAAC,oBAAqB,CAC7B,CAAA;gBAEL,IAAI,QAAQ,EAAE,CAAC;oBACX,OAAO,QAAQ,CAAA;gBACnB,CAAC;gBAED,IAAI,sBAAsB,CAAC,QAAQ,CAAC,oBAAoB,EAAE,CAAC;oBACvD,QAAQ;wBACJ,sBAAsB,CAAC,QAAQ,CAAC,oBAAoB,CAAC,4BAA4B,CAC7E,IAAI,CAAC,oBAAqB,CAC7B,CAAA;oBACL,IAAI,QAAQ,EAAE,CAAC;wBACX,OAAO,QAAQ,CAAA;oBACnB,CAAC;gBACL,CAAC;gBAED,MAAM,IAAI,YAAY,CAClB,+BAA+B,IAAI,CAAC,oBAAoB,2BAA2B,CACtF,CAAA;YACL,CAAC,CAAA;YACD,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAA;YAC1C,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAA;QACjC,CAAC;QACD,OAAO,IAAI,CAAC,aAAa,CAAA;IAC7B,CAAC;IAED;;;OAGG;IACH,IAAI,QAAQ;QACR,qDAAqD;QACrD,IAAI,IAAI,CAAC,QAAQ;YAAE,OAAO,IAAI,CAAC,QAAQ,CAAC,qBAAqB,CAAA;QAE7D,mCAAmC;QACnC,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC;YAClD,OAAO,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAA;QAE7D,oFAAoF;QACpF,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC;YACpE,OAAO,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;QACxD,CAAC;QAED,OAAO,SAAS,CAAA;QAEhB;;;;;;;;;;WAUG;IACP,CAAC;IAED;;OAEG;IACH,IAAI,aAAa;QACb,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACjB,MAAM,IAAI,YAAY,CAClB,sDAAsD,CACzD,CAAA;QACL,CAAC;QACD,IAAI,OAAO,IAAI,CAAC,gBAAgB,KAAK,QAAQ,EAAE,CAAC;YAC5C,MAAM,IAAI,YAAY,CAAC,mCAAmC,CAAC,CAAA;QAC/D,CAAC;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAC9C,CAAC,EACD,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,GAAG,CAAC,CACrC,CAAA;QAED,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;YACzB,OAAO,WAAW,CAAC,UAAU,CACzB,IAAI,CAAC,UAAU,CAAC,MAAM,EACtB,SAAS,EACT,aAAa,EACb,IAAI,CAAC,KAAK,CAAC,IAAI,CAClB,CAAA;QACL,CAAC;aAAM,CAAC;YACJ,OAAO,WAAW,CAAC,UAAU,CACzB,IAAI,CAAC,UAAU,CAAC,MAAM,EACtB,SAAS,EACT,IAAI,CAAC,KAAK,CAAC,IAAI,EACf,aAAa,CAChB,CAAA;QACL,CAAC;IACL,CAAC;IAED,IAAI,wBAAwB;QACxB,IAAI,CAAC,IAAI,CAAC,aAAa;YAAE,OAAO,SAAS,CAAA;QAEzC,OAAO,IAAI,CAAC,aAAc,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;IAC5C,CAAC;IAED,IAAI,yBAAyB;QACzB,IAAI,CAAC,IAAI,CAAC,aAAa;YAAE,OAAO,SAAS,CAAA;QAEzC,OAAO,IAAI,CAAC,aAAc,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;IAC5C,CAAC;CACJ", "file": "JoinAttribute.js", "sourcesContent": ["import { EntityMetadata } from \"../metadata/EntityMetadata\"\nimport { DataSource } from \"../data-source/DataSource\"\nimport { RelationMetadata } from \"../metadata/RelationMetadata\"\nimport { QueryBuilderUtils } from \"./QueryBuilderUtils\"\nimport { QueryExpressionMap } from \"./QueryExpressionMap\"\nimport { Alias } from \"./Alias\"\nimport { ObjectUtils } from \"../util/ObjectUtils\"\nimport { TypeORMError } from \"../error\"\nimport { DriverUtils } from \"../driver/DriverUtils\"\n\n/**\n * Stores all join attributes which will be used to build a JOIN query.\n */\nexport class JoinAttribute {\n    // -------------------------------------------------------------------------\n    // Public Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Join direction.\n     */\n    direction: \"LEFT\" | \"INNER\"\n\n    /**\n     * Alias of the joined (destination) table.\n     */\n    alias: Alias\n\n    /**\n     * Joined table, entity target, or relation in \"post.category\" format.\n     */\n    entityOrProperty: Function | string\n\n    /**\n     * Extra condition applied to \"ON\" section of join.\n     */\n    condition?: string\n\n    /**\n     * Property + alias of the object where to joined data should be mapped.\n     */\n    mapToProperty?: string\n\n    /**\n     * Indicates if user maps one or many objects from the join.\n     */\n    isMappingMany?: boolean\n\n    /**\n     * Useful when the joined expression is a custom query to support mapping.\n     */\n    mapAsEntity?: Function | string\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(\n        private connection: DataSource,\n        private queryExpressionMap: QueryExpressionMap,\n        joinAttribute?: JoinAttribute,\n    ) {\n        if (joinAttribute) {\n            ObjectUtils.assign(this, joinAttribute)\n        }\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    get isMany(): boolean {\n        if (this.isMappingMany !== undefined) return this.isMappingMany\n\n        if (this.relation)\n            return this.relation.isManyToMany || this.relation.isOneToMany\n\n        return false\n    }\n\n    isSelectedCache: boolean\n    isSelectedEvaluated: boolean = false\n    /**\n     * Indicates if this join is selected.\n     */\n    get isSelected(): boolean {\n        if (!this.isSelectedEvaluated) {\n            const getValue = () => {\n                for (const select of this.queryExpressionMap.selects) {\n                    if (select.selection === this.alias.name) return true\n\n                    if (\n                        this.metadata &&\n                        !!this.metadata.columns.find(\n                            (column) =>\n                                select.selection ===\n                                this.alias.name + \".\" + column.propertyPath,\n                        )\n                    )\n                        return true\n                }\n\n                return false\n            }\n            this.isSelectedCache = getValue()\n            this.isSelectedEvaluated = true\n        }\n        return this.isSelectedCache\n    }\n\n    /**\n     * Name of the table which we should join.\n     */\n    get tablePath(): string {\n        return this.metadata\n            ? this.metadata.tablePath\n            : (this.entityOrProperty as string)\n    }\n\n    /**\n     * Alias of the parent of this join.\n     * For example, if we join (\"post.category\", \"categoryAlias\") then \"post\" is a parent alias.\n     * This value is extracted from entityOrProperty value.\n     * This is available when join was made using \"post.category\" syntax.\n     */\n    get parentAlias(): string | undefined {\n        if (!QueryBuilderUtils.isAliasProperty(this.entityOrProperty))\n            return undefined\n\n        return this.entityOrProperty.substr(\n            0,\n            this.entityOrProperty.indexOf(\".\"),\n        )\n    }\n\n    /**\n     * Relation property name of the parent.\n     * This is used to understand what is joined.\n     * For example, if we join (\"post.category\", \"categoryAlias\") then \"category\" is a relation property.\n     * This value is extracted from entityOrProperty value.\n     * This is available when join was made using \"post.category\" syntax.\n     */\n    get relationPropertyPath(): string | undefined {\n        if (!QueryBuilderUtils.isAliasProperty(this.entityOrProperty))\n            return undefined\n\n        return this.entityOrProperty.substr(\n            this.entityOrProperty.indexOf(\".\") + 1,\n        )\n    }\n\n    relationCache: RelationMetadata | undefined\n    relationEvaluated: boolean = false\n    /**\n     * Relation of the parent.\n     * This is used to understand what is joined.\n     * This is available when join was made using \"post.category\" syntax.\n     * Relation can be undefined if entityOrProperty is regular entity or custom table.\n     */\n    get relation(): RelationMetadata | undefined {\n        if (!this.relationEvaluated) {\n            const getValue = () => {\n                if (!QueryBuilderUtils.isAliasProperty(this.entityOrProperty))\n                    return undefined\n\n                const relationOwnerSelection =\n                    this.queryExpressionMap.findAliasByName(this.parentAlias!)\n                let relation =\n                    relationOwnerSelection.metadata.findRelationWithPropertyPath(\n                        this.relationPropertyPath!,\n                    )\n\n                if (relation) {\n                    return relation\n                }\n\n                if (relationOwnerSelection.metadata.parentEntityMetadata) {\n                    relation =\n                        relationOwnerSelection.metadata.parentEntityMetadata.findRelationWithPropertyPath(\n                            this.relationPropertyPath!,\n                        )\n                    if (relation) {\n                        return relation\n                    }\n                }\n\n                throw new TypeORMError(\n                    `Relation with property path ${this.relationPropertyPath} in entity was not found.`,\n                )\n            }\n            this.relationCache = getValue.bind(this)()\n            this.relationEvaluated = true\n        }\n        return this.relationCache\n    }\n\n    /**\n     * Metadata of the joined entity.\n     * If table without entity was joined, then it will return undefined.\n     */\n    get metadata(): EntityMetadata | undefined {\n        // entityOrProperty is relation, e.g. \"post.category\"\n        if (this.relation) return this.relation.inverseEntityMetadata\n\n        // entityOrProperty is Entity class\n        if (this.connection.hasMetadata(this.entityOrProperty))\n            return this.connection.getMetadata(this.entityOrProperty)\n\n        // Overriden mapping entity provided for leftJoinAndMapOne with custom query builder\n        if (this.mapAsEntity && this.connection.hasMetadata(this.mapAsEntity)) {\n            return this.connection.getMetadata(this.mapAsEntity)\n        }\n\n        return undefined\n\n        /*if (typeof this.entityOrProperty === \"string\") { // entityOrProperty is a custom table\n\n            // first try to find entity with such name, this is needed when entity does not have a target class,\n            // and its target is a string name (scenario when plain old javascript is used or entity schema is loaded from files)\n            const metadata = this.connection.entityMetadatas.find(metadata => metadata.name === this.entityOrProperty);\n            if (metadata)\n                return metadata;\n\n            // check if we have entity with such table name, and use its metadata if found\n            return this.connection.entityMetadatas.find(metadata => metadata.tableName === this.entityOrProperty);\n        }*/\n    }\n\n    /**\n     * Generates alias of junction table, whose ids we get.\n     */\n    get junctionAlias(): string {\n        if (!this.relation) {\n            throw new TypeORMError(\n                `Cannot get junction table for join without relation.`,\n            )\n        }\n        if (typeof this.entityOrProperty !== \"string\") {\n            throw new TypeORMError(`Junction property is not defined.`)\n        }\n\n        const aliasProperty = this.entityOrProperty.substr(\n            0,\n            this.entityOrProperty.indexOf(\".\"),\n        )\n\n        if (this.relation.isOwning) {\n            return DriverUtils.buildAlias(\n                this.connection.driver,\n                undefined,\n                aliasProperty,\n                this.alias.name,\n            )\n        } else {\n            return DriverUtils.buildAlias(\n                this.connection.driver,\n                undefined,\n                this.alias.name,\n                aliasProperty,\n            )\n        }\n    }\n\n    get mapToPropertyParentAlias(): string | undefined {\n        if (!this.mapToProperty) return undefined\n\n        return this.mapToProperty!.split(\".\")[0]\n    }\n\n    get mapToPropertyPropertyName(): string | undefined {\n        if (!this.mapToProperty) return undefined\n\n        return this.mapToProperty!.split(\".\")[1]\n    }\n}\n"], "sourceRoot": ".."}