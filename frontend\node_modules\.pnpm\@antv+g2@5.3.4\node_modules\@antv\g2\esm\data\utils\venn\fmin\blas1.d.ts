export declare function zeros(x: any): any[];
export declare function zerosM(x: any, y: any): any[][];
export declare function dot(a: any, b: any): number;
export declare function norm2(a: any): number;
export declare function scale(ret: any, value: any, c?: any): void;
export declare function weightedSum(ret: any, w1: any, v1: any, w2: any, v2: any): void;
export declare function gemv(output: any, A: any, x: any): void;
