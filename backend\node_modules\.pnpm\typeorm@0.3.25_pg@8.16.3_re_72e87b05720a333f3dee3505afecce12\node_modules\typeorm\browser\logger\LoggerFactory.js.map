{"version": 3, "sources": ["../browser/src/logger/LoggerFactory.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,mBAAmB,EAAE,MAAM,uBAAuB,CAAA;AAC3D,OAAO,EAAE,qBAAqB,EAAE,MAAM,yBAAyB,CAAA;AAC/D,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAA;AACzC,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAA;AAC3C,OAAO,EAAE,WAAW,EAAE,MAAM,qBAAqB,CAAA;AACjD,OAAO,EAAE,sBAAsB,EAAE,MAAM,0BAA0B,CAAA;AAEjE;;GAEG;AACH,MAAM,OAAO,aAAa;IACtB;;OAEG;IACH,MAAM,CACF,MAMY,EACZ,OAAuB;QAEvB,IAAI,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC;YAAE,OAAO,MAAgB,CAAA;QAEzD,IAAI,MAAM,EAAE,CAAC;YACT,QAAQ,MAAM,EAAE,CAAC;gBACb,KAAK,gBAAgB;oBACjB,OAAO,IAAI,mBAAmB,CAAC,OAAO,CAAC,CAAA;gBAE3C,KAAK,MAAM;oBACP,OAAO,IAAI,UAAU,CAAC,OAAO,CAAC,CAAA;gBAElC,KAAK,kBAAkB;oBACnB,OAAO,IAAI,qBAAqB,CAAC,OAAO,CAAC,CAAA;gBAE7C,KAAK,mBAAmB;oBACpB,OAAO,IAAI,sBAAsB,CAAC,OAAO,CAAC,CAAA;gBAE9C,KAAK,OAAO;oBACR,OAAO,IAAI,WAAW,EAAE,CAAA;YAChC,CAAC;QACL,CAAC;QAED,OAAO,IAAI,qBAAqB,CAAC,OAAO,CAAC,CAAA;IAC7C,CAAC;CACJ", "file": "LoggerFactory.js", "sourcesContent": ["import { Logger } from \"./Logger\"\nimport { LoggerOptions } from \"./LoggerOptions\"\nimport { SimpleConsoleLogger } from \"./SimpleConsoleLogger\"\nimport { AdvancedConsoleLogger } from \"./AdvancedConsoleLogger\"\nimport { FileLogger } from \"./FileLogger\"\nimport { DebugLogger } from \"./DebugLogger\"\nimport { ObjectUtils } from \"../util/ObjectUtils\"\nimport { FormattedConsoleLogger } from \"./FormattedConsoleLogger\"\n\n/**\n * Helps to create logger instances.\n */\nexport class LoggerFactory {\n    /**\n     * Creates a new logger depend on a given connection's driver.\n     */\n    create(\n        logger?:\n            | \"advanced-console\"\n            | \"simple-console\"\n            | \"formatted-console\"\n            | \"file\"\n            | \"debug\"\n            | Logger,\n        options?: LoggerOptions,\n    ): Logger {\n        if (ObjectUtils.isObject(logger)) return logger as Logger\n\n        if (logger) {\n            switch (logger) {\n                case \"simple-console\":\n                    return new SimpleConsoleLogger(options)\n\n                case \"file\":\n                    return new FileLogger(options)\n\n                case \"advanced-console\":\n                    return new AdvancedConsoleLogger(options)\n\n                case \"formatted-console\":\n                    return new FormattedConsoleLogger(options)\n\n                case \"debug\":\n                    return new DebugLogger()\n            }\n        }\n\n        return new AdvancedConsoleLogger(options)\n    }\n}\n"], "sourceRoot": ".."}