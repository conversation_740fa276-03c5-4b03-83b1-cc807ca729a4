{"version": 3, "file": "point.js", "sourceRoot": "", "sources": ["../../src/mark/point.ts"], "names": [], "mappings": "AAEA,OAAO,EACL,WAAW,EACX,UAAU,EACV,YAAY,EACZ,YAAY,EACZ,iBAAiB,EACjB,kBAAkB,EAClB,kBAAkB,EAClB,WAAW,EACX,iBAAiB,EACjB,mBAAmB,EACnB,uBAAuB,EACvB,WAAW,EACX,SAAS,EACT,iBAAiB,EACjB,SAAS,EACT,WAAW,EACX,UAAU,EACV,SAAS,EACT,aAAa,EACb,WAAW,EACX,iBAAiB,GAClB,MAAM,UAAU,CAAC;AAClB,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,MAAM,cAAc,CAAC;AACjE,OAAO,EACL,oBAAoB,EACpB,iBAAiB,EACjB,gBAAgB,EAChB,gBAAgB,EAChB,SAAS,GACV,MAAM,SAAS,CAAC;AAEjB,MAAM,KAAK,GAAG;IACZ,MAAM,EAAE,WAAW;IACnB,aAAa,EAAE,kBAAkB;IACjC,aAAa,EAAE,kBAAkB;IACjC,YAAY,EAAE,iBAAiB;IAC/B,kBAAkB,EAAE,uBAAuB;IAC3C,cAAc,EAAE,mBAAmB;IACnC,YAAY,EAAE,iBAAiB;IAC/B,YAAY,EAAE,iBAAiB;IAC/B,KAAK,EAAE,UAAU;IACjB,IAAI,EAAE,SAAS;IACf,OAAO,EAAE,YAAY;IACrB,MAAM,EAAE,WAAW;IACnB,QAAQ,EAAE,aAAa;IACvB,OAAO,EAAE,YAAY;IACrB,KAAK,EAAE,UAAU;IACjB,MAAM,EAAE,WAAW;IACnB,MAAM,EAAE,WAAW;IACnB,IAAI,EAAE,SAAS;IACf,IAAI,EAAE,SAAS;IACf,YAAY,EAAE,iBAAiB;IAC/B,MAAM,EAAE,WAAW;CACpB,CAAC;AAIF;;;;GAIG;AACH,MAAM,CAAC,MAAM,KAAK,GAAqB,CAAC,OAAO,EAAE,EAAE;IACjD,OAAO,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,EAAE;QACzC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,KAAK,CAAC;QACtE,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,UAAU,CAAC,OAAO,EAAE,CAAC;QAC7C,MAAM,MAAM,GAAG,gBAAgB,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QACvD,MAAM,EAAE,GAA2B,CAAC,CAAC,EAAE,EAAE;YACvC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAA,EAAE,aAAF,EAAE,uBAAF,EAAE,CAAG,CAAC,CAAC,KAAI,CAAC,CAAC,CAAC;YAC3B,MAAM,EAAE,GAAG,CAAC,CAAC,CAAA,EAAE,aAAF,EAAE,uBAAF,EAAE,CAAG,CAAC,CAAC,KAAI,CAAC,CAAC,CAAC;YAC3B,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5C,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5C,MAAM,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;YAClB,MAAM,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;YAClB,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QAClB,CAAC,CAAC;QACF,MAAM,CAAC,GAAG,CAAC;YACT,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,EAAE;gBACtB,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;gBACvB,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChB,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;gBACpB,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC;gBACrB,MAAM,EAAE,GAAY,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC;gBACrC,MAAM,EAAE,GAAY,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC;gBACrC,OAAO;oBACL,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;oBAC7B,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;iBACjB,CAAC;YACjB,CAAC,CAAC;YACJ,CAAC,CAAC,KAAK,CAAC,IAAI,CACR,KAAK,EACL,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAc,CACvD,CAAC;QACN,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF,KAAK,CAAC,KAAK,GAAG;IACZ,YAAY,EAAE,QAAQ;IACtB,iBAAiB,EAAE,OAAO;IAC1B,SAAS,EAAE,KAAK;IAChB,KAAK;IACL,QAAQ,EAAE;QACR,GAAG,oBAAoB,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;QACvD,EAAE,IAAI,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE;QAC7B,EAAE,IAAI,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE;QAC7B,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE;QACjC,EAAE,IAAI,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE;QACtC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE;QACjC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE;KAClC;IACD,YAAY,EAAE;QACZ,GAAG,gBAAgB,EAAE;QACrB,EAAE,IAAI,EAAE,UAAU,EAAE;QACpB,EAAE,IAAI,EAAE,UAAU,EAAE;KACrB;IACD,aAAa,EAAE,CAAC,GAAG,iBAAiB,EAAE,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,GAAG,SAAS,EAAE,CAAC;CAC7E,CAAC"}