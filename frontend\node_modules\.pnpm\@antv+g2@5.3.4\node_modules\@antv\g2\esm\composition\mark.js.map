{"version": 3, "file": "mark.js", "sourceRoot": "", "sources": ["../../src/composition/mark.ts"], "names": [], "mappings": ";;;;;;;;;;;AAKA,8BAA8B;AAC9B,MAAM,CAAC,MAAM,IAAI,GAAoB,CAAC,EACpC,MAAM,EAAE,QAAQ,GAAG,KAAK,MACjB,EAAE,EAAE,EAAE;IACb,OAAO,CAAC,OAAO,EAAE,EAAE;QACjB,MAAM,EACJ,KAAK,EACL,MAAM,EACN,KAAK,EACL,WAAW,EACX,YAAY,EACZ,UAAU,EACV,aAAa,EACb,OAAO,EACP,KAAK,EACL,SAAS,EACT,QAAQ,EACR,UAAU,EACV,WAAW,EACX,MAAM,EACN,UAAU,EACV,YAAY,EACZ,SAAS,EACT,WAAW,EACX,IAAI,EACJ,UAAU,EACV,KAAK,EACL,SAAS,EACT,WAAW,EACX,CAAC,EACD,CAAC,EACD,CAAC,EACD,GAAG,EACH,KAAK,EACL,cAAc,EACd,SAAS,EACT,IAAI,EACJ,SAAS,EACT,KAAK,KAEH,OAAO,EADN,IAAI,UACL,OAAO,EAnCL,+XAmCL,CAAU,CAAC;QAEZ,OAAO;0CAEH,IAAI,EAAE,cAAc,EACpB,CAAC;gBACD,CAAC;gBACD,CAAC;gBACD,GAAG;gBACH,KAAK;gBACL,MAAM;gBACN,KAAK;gBACL,OAAO;gBACP,WAAW;gBACX,YAAY;gBACZ,UAAU;gBACV,KAAK;gBACL,SAAS;gBACT,QAAQ;gBACR,UAAU;gBACV,WAAW;gBACX,aAAa;gBACb,KAAK;gBACL,UAAU;gBACV,SAAS;gBACT,WAAW;gBACX,KAAK;gBACL,cAAc;gBACd,MAAM;gBACN,UAAU;gBACV,YAAY;gBACZ,SAAS;gBACT,WAAW;gBACX,SAAS;gBACT,IAAI,EACJ,KAAK,EAAE,SAAS,IAEb,CAAC,CAAC,QAAQ,IAAI,EAAE,KAAK,EAAE,CAAC,KAC3B,KAAK,EAAE,+CAAM,IAAI,KAAE,GAAG,EAAE,GAAG,GAAG,IAAI,EAAE,IAAI,KAAK,CAAC,QAAQ,IAAI,EAAE,KAAK,EAAE,CAAC,EAAG;SAE1E,CAAC;IACJ,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC"}