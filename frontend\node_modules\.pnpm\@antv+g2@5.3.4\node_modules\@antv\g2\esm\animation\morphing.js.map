{"version": 3, "file": "morphing.js", "sourceRoot": "", "sources": ["../../src/animation/morphing.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,aAAa,EAGb,IAAI,EACJ,KAAK,GACN,MAAM,SAAS,CAAC;AACjB,OAAO,EAAE,GAAG,EAAE,MAAM,YAAY,CAAC;AAEjC,OAAO,EAAE,cAAc,EAAE,MAAM,iBAAiB,CAAC;AAEjD,OAAO,EAAE,aAAa,EAAE,WAAW,EAAE,mBAAmB,EAAE,MAAM,SAAS,CAAC;AAQ1E,SAAS,WAAW,CAAC,KAAoB;IACvC,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,KAAK,CAAC,cAAc,EAAE,CAAC;IAC5C,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC;IACrB,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC;IACrB,MAAM,MAAM,GAAG,EAAE,GAAG,EAAE,CAAC;IACvB,MAAM,KAAK,GAAG,EAAE,GAAG,EAAE,CAAC;IACtB,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;AACjC,CAAC;AAED,SAAS,CAAC,CAAC,IAAU;IACnB,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC;IACnC,OAAO;QACD,CAAC,IAAI,CAAC;QACN,CAAC,GAAG,KAAK,IAAI,CAAC;QACd,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,MAAM;QACvB,CAAC,IAAI,CAAC,GAAG,MAAM;;GAEpB,CAAC;AACJ,CAAC;AAED,SAAS,IAAI,CAAC,KAAoB,EAAE,KAAa;IAC/C,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,KAAK,EAAE,MAAM,CAAC,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;IACnD,MAAM,MAAM,GAAG,MAAM,GAAG,KAAK,CAAC;IAC9B,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC;IACjD,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC;IACnC,MAAM,CAAC,GAAG,EAAE,CAAC;IACb,MAAM,CAAC,GAAG,MAAM,GAAG,GAAG,CAAC;IACvB,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,IAAI,CAAC,GAAG,KAAK,CAAC;IACd,OAAO,CAAC,GAAG,CAAC,EAAE;QACZ,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QAC3B,MAAM,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC;QACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAC1B,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;YACrB,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;YACrB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;SACzB;QACD,CAAC,IAAI,CAAC,CAAC;QACP,CAAC,IAAI,CAAC,CAAC;KACR;IACD,OAAO,CAAC,CAAC;AACX,CAAC;AAED,SAAS,cAAc,CACrB,QAAkC,MAAM;IAExC,IAAI,OAAO,KAAK,IAAI,UAAU;QAAE,OAAO,KAAK,CAAC;IAC7C,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;;GAKG;AACH,SAAS,YAAY,CACnB,IAAmB,EACnB,EAAiB,EACjB,UAA+B;IAE/B,IAAI,EAAE,SAAS,EAAE,aAAa,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC;IAC9C,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC;IAE5C,kDAAkD;IAClD,YAAY,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;IAEvB,IAAI,IAAI,GAAG,aAAa,CAAC;IACzB,IAAI,IAAI,CAAC,QAAQ,KAAK,KAAK,CAAC,KAAK,EAAE;QACjC,uCAAuC;QACvC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;QAC3C,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC;QACzC,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;QACnB,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;QACnB,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;QACnB,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;QACnB,aAAa,GAAG,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,GAAG,CAAC;KAC/D;SAAM;QACL,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;KAC9D;IAED,MAAM,SAAS,GAAG;wBAEd,SAAS,EAAE,aAAa,aAAb,aAAa,cAAb,aAAa,GAAI,MAAM,IAC/B,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;wBAGhC,SAAS,EAAE,WAAW,aAAX,WAAW,cAAX,WAAW,GAAI,MAAM,IAC7B,WAAW,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC;KAEjC,CAAC;IACF,MAAM,SAAS,GAAG,EAAE,CAAC,OAAO,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;IACpD,OAAO,SAAS,CAAC;AACnB,CAAC;AAED;;GAEG;AACH,SAAS,YAAY,CAAC,QAAuB,EAAE,QAAuB;IACpE,QAAQ,CAAC,UAAU,CAAC,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC;IAC5C,QAAQ,CAAC,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC;IACxC,aAAa;IACb,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC;IACtC,QAAQ,CAAC,UAAU,CAAC,YAAY,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;AACvD,CAAC;AAED;;GAEG;AACH,SAAS,SAAS,CAAC,IAAmB,EAAE,CAAS;IAC/C,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC;IAC1B,IAAI,QAAQ,KAAK,MAAM;QAAE,OAAO,IAAI,CAAC;IACrC,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC;QACpB,KAAK,kCACA,WAAW,CAAC,IAAI,EAAE,aAAa,CAAC,KACnC,CAAC,GACF;KACF,CAAC,CAAC;IACH,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACzB,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,eAAe,CAAC,MAAc,EAAE,OAAe;IACtD,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IACtC,MAAM,IAAI,GAAG,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IACzC,OAAO,KAAK,KAAK,IAAI,CAAC;AACxB,CAAC;AAED,8DAA8D;AAC9D,2CAA2C;AAC3C,SAAS,UAAU,CAAC,IAAY;IAC9B,OAAO,CAAC,eAAe,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AACpE,CAAC;AAED,SAAS,UAAU,CAAC,KAAoB;IACtC,MAAM,IAAI,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC;IAClC,IAAI,CAAC,IAAI;QAAE,OAAO;IAClB,kEAAkE;IAClE,6BAA6B;IAC7B,IAAI,UAAU,CAAC,IAAI,CAAC;QAAE,OAAO;IAC7B,OAAO,IAAI,CAAC;AACd,CAAC;AACD,kDAAkD;AAClD,SAAS,SAAS,CAAC,KAAoB;IACrC,MAAM,EAAE,QAAQ,EAAE,GAAG,KAAK,CAAC;IAC3B,IAAI,QAAQ,KAAK,MAAM,EAAE;QACvB,MAAM,UAAU,GAAG,GAAG,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;QAC5C,OAAO,UAAU,CAAC,SAAS,IAAI,UAAU,CAAC,WAAW,CAAC;KACvD;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,QAAQ,CACf,KAAoB,EACpB,IAAmB,EACnB,EAAiB,EACjB,UAA+B;IAE/B,6CAA6C;IAC7C,sCAAsC;IACtC,sCAAsC;IACtC,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC;IACpC,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;IAChC,MAAM,QAAQ,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;IAClC,MAAM,MAAM,GAAG,UAAU,CAAC,EAAE,CAAC,CAAC;IAC9B,MAAM,WAAW,GAAG,QAAQ,KAAK,MAAM,IAAI,QAAQ,KAAK,MAAM,CAAC;IAC/D,MAAM,cAAc,GAAG,QAAQ,KAAK,SAAS,IAAI,MAAM,KAAK,SAAS,CAAC;IACtE,yDAAyD;IACzD,MAAM,gBAAgB,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,SAAS,CAAC,EAAE,CAAC,CAAC;IAC1D,IAAI,WAAW,IAAI,cAAc,IAAI,gBAAgB;QACnD,OAAO,YAAY,CAAC,IAAI,EAAE,EAAE,EAAE,UAAU,CAAC,CAAC;IAC5C,MAAM,SAAS,GAAG,SAAS,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;IAC7C,8DAA8D;IAC9D,gEAAgE;IAChE,MAAM,SAAS,GAAe;0BAEvB,WAAW,CAAC,IAAI,EAAE,aAAa,CAAC;0BAGhC,WAAW,CAAC,EAAE,EAAE,aAAa,CAAC;KAEpC,CAAC;IACF,IAAI,QAAQ,KAAK,MAAM,EAAE;QACvB,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC;QAC1B,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;QAExB,MAAM,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;QAC3D,SAAS,CAAC,QAAQ,GAAG,GAAG,EAAE;YACxB,4CAA4C;YAC5C,cAAc,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;YAC9B,SAAS,CAAC,KAAK,CAAC,CAAC,GAAG,MAAM,CAAC;YAC3B,SAAS,CAAC,KAAK,CAAC,SAAS,GAAG,MAAM,CAAC;QACrC,CAAC,CAAC;QAEF,sDAAsD;QACtD,8BAA8B;QAC9B,SAAS,CAAC,KAAK,CAAC,SAAS,GAAG,MAAM,CAAC;QACnC,OAAO,SAAS,CAAC;KAClB;IAED,2DAA2D;IAC3D,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,aAAa,CACpB,IAAmB,EACnB,EAAmB,EACnB,UAA+B,EAC/B,KAAoB;IAEpB,oDAAoD;IACpD,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,QAAQ,CAAC;IACjC,MAAM,CAAC,GAAG,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC;IACjC,OAAO,EAAE,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE;QACzB,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC;YACpB,KAAK,kBACH,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IACJ,WAAW,CAAC,IAAI,EAAE,aAAa,CAAC,CACpC;SACF,CAAC,CAAC;QACH,OAAO,QAAQ,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;IAClD,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,aAAa,CACpB,IAAqB,EACrB,EAAiB,EACjB,UAA+B,EAC/B,KAAoB;IAEpB,MAAM,CAAC,GAAG,KAAK,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IACjC,MAAM,EAAE,WAAW,GAAG,CAAC,EAAE,aAAa,GAAG,CAAC,EAAE,OAAO,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC;IACrE,MAAM,SAAS,GAAG;QAChB,EAAE,WAAW,EAAE,CAAC,EAAE,aAAa,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE;QAChD,EAAE,WAAW,EAAE,CAAC,EAAE,aAAa,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE;QAC9D;YACE,WAAW;YACX,aAAa;YACb,OAAO;SACR;KACF,CAAC;IACF,MAAM,SAAS,GAAG,EAAE,CAAC,OAAO,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;IACpD,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE;QACvC,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC;YACpB,KAAK,EAAE;gBACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBACP,IAAI,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI;aACpB;SACF,CAAC,CAAC;QACH,OAAO,QAAQ,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;IAClD,CAAC,CAAC,CAAC;IACH,OAAO,CAAC,GAAG,UAAU,EAAE,SAAS,CAAC,CAAC;AACpC,CAAC;AAED;;;GAGG;AACH,MAAM,CAAC,MAAM,QAAQ,GAAwB,CAAC,OAAO,EAAE,EAAE;IACvD,OAAO,CAAC,IAAI,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE;QAC5B,MAAM,KAAK,GAAG,cAAc,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAC5C,MAAM,UAAU,mCAAQ,QAAQ,GAAK,OAAO,CAAE,CAAC;QAC/C,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC;QAC5B,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC;QAC1B,IAAI,CAAC,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,EAAE;YAChD,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;YACjB,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;YACf,OAAO,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC;SACtC;QACD,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE;YACtB,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;YACjB,OAAO,aAAa,CAAC,CAAC,EAAE,EAAE,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;SAChD;QACD,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE;YACtB,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;YACf,OAAO,aAAa,CAAC,IAAI,EAAE,CAAC,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;SAClD;QACD,OAAO,IAAI,CAAC;IACd,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF,QAAQ,CAAC,KAAK,GAAG,EAAE,CAAC"}